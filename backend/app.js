const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');

// Rotaları içe aktar
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const postRoutes = require('./routes/postRoutes');
const followRoutes = require('./routes/followRoutes');
const messageRoutes = require('./routes/messageRoutes');
const healthRoutes = require('./routes/healthRoutes');
const discoveryRoutes = require('./routes/discoveryRoutes');

// Middleware'leri içe aktar
const { handleMulterError } = require('./middleware/uploadMiddleware');
const limiter = require('./middleware/rateLimiter');
const { validateMobileDevice, detectSuspiciousActivity } = require('./middleware/authMiddleware');

// <PERSON><PERSON>re değişkenlerini yükle
dotenv.config();

// Express uygulamasını başlat
const app = express();

// Genel Middleware'ler
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Gelişmiş CORS ayarları
const corsOptions = {
  origin: '*', // Tüm kaynaklara izin ver (mobil uygulamalar dahil)
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Device-ID', 'X-Device-Model', 'X-App-Version', 'Accept'],
  exposedHeaders: ['Authorization', 'Content-Length'],
  credentials: true,
  maxAge: 86400, // CORS ön kontrol yanıtlarının önbellek süresi (1 gün)
  optionsSuccessStatus: 200 // Bazı tarayıcılarda OPTIONS için 204 yerine 200 kullan
};

// CORS middleware'ini uygula
app.use(cors(corsOptions));

// Express'in JSON parse işlemlerini ayarla
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Üretim ortamında CORS log
if (process.env.NODE_ENV === 'production') {
  console.log('CORS ayarları: Tüm kaynaklara izin verildi. Güvenlik için kontrol edilmelidir.');
}

// API Rate Limiter
app.use('/api', limiter.apiLimiter);

// Mobil cihaz doğrulama ve şüpheli aktivite tespiti - opsiyonel olarak etkinleştirin
if (process.env.NODE_ENV === 'production') {
  app.use('/api', detectSuspiciousActivity);
  app.use('/api', validateMobileDevice);
}

// Statik dosyaların sunulması
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Statik dosyalara erişim için
app.use(express.static('public'));

// API için loglama middleware'i
app.use('/api', (req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/follow', followRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/health', healthRoutes);
app.use('/api/discover', discoveryRoutes);

// Debug için rota listesini logla
console.log('--- API ROTALAR ---');
console.log('/api/posts/:id/comments - GET, POST, DELETE');
console.log('-------------------');

// Temel route
app.get('/', (req, res) => {
  res.send('Sosyal Medya API çalışıyor');
});

// Hata middleware'leri
app.use(handleMulterError);

// 404 - Not Found
app.use((req, res, next) => {
  console.log(`404 Rota bulunamadı: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    status: 'fail',
    message: `${req.originalUrl} rotası bulunamadı`
  });
});

// Genel hata işleyici
app.use((err, req, res, next) => {
  console.error(`Hata: ${err.message}`);
  console.error(err.stack);
  
  const statusCode = err.statusCode || 500;
  const status = err.status || 'error';
  
  res.status(statusCode).json({
    status,
    message: err.message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

module.exports = app; 