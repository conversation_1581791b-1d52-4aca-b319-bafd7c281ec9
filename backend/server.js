// Logger modülünü önce import edelim
const { logger, setupCloudWatchLogs } = require('./utils/logger');

// AWS X-Ray için SDK'yı içe aktar
let AWSXRay;
try {
  AWSXRay = process.env.NODE_ENV === 'production' 
    ? require('aws-xray-sdk') 
    : { express: { openSegment: () => (req, res, next) => next(), closeSegment: null } };
  
  if (process.env.NODE_ENV === 'production') {
    logger.info('AWS X-Ray SDK yüklendi');
  }
} catch (err) {
  logger.error('AWS X-Ray SDK yüklenirken hata oluştu:', err.message);
  // Hata durumunda dummy SDK nesnesi oluştur
  AWSXRay = { express: { openSegment: () => (req, res, next) => next(), closeSegment: null } };
}

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const app = require('./app');

// Çevre değişkenlerini yükle
dotenv.config();

// X-Ray ile izleme başlat (sadece üretimde)
if (process.env.NODE_ENV === 'production') {
  try {
    // Kullanılabilir plugin'leri kontrol et
    const plugins = [];
    if (AWSXRay.plugins && AWSXRay.plugins.EC2Plugin) {
      plugins.push(AWSXRay.plugins.EC2Plugin);
    }
    if (AWSXRay.plugins && AWSXRay.plugins.ElasticBeanstalkPlugin) {
      plugins.push(AWSXRay.plugins.ElasticBeanstalkPlugin);
    }
    
    // Plugin'ler varsa yapılandır
    if (plugins.length > 0) {
      AWSXRay.config(plugins);
    }
    
    if (AWSXRay.middleware && AWSXRay.middleware.setSamplingRules) {
      AWSXRay.middleware.setSamplingRules('./xray-sampling-rules.json');
    }
    
    // Express middleware için segment aç
    if (AWSXRay.express && AWSXRay.express.openSegment) {
      app.use(AWSXRay.express.openSegment('StyleUpBackend'));
    }
  } catch (err) {
    logger.error('AWS X-Ray yapılandırma hatası:', err);
  }
  
  // CloudWatch loglarını yapılandır
  setupCloudWatchLogs().catch(err => {
    logger.error('CloudWatch setup error:', err);
  });
  
  // Mobil uygulama için ek güvenlik önlemleri
  app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    next();
  });
}

// Veritabanı bağlantısı
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: true
})
.then(() => {
  logger.info('MongoDB bağlantısı başarılı');
})
.catch(err => {
  logger.error('MongoDB bağlantı hatası:', err);
  process.exit(1);
});

// Port ayarı
const PORT = process.env.PORT || 5000;
const server = app.listen(PORT, () => {
  logger.info(`Server ${PORT} portunda çalışıyor (${process.env.NODE_ENV} modunda)`);
});

// X-Ray için segment kapat (sadece üretimde)
if (process.env.NODE_ENV === 'production') {
  try {
    // AWSXRay.express.closeSegment bir fonksiyon olmalı, doğrudan çağırmayalım
    if (AWSXRay && typeof AWSXRay === 'object' && AWSXRay.express) {
      logger.info('AWS X-Ray segment kapatılıyor.');
      // Eğer closeSegment varsa ve bir fonksiyonsa, onu kullanalım
      if (typeof AWSXRay.express.closeSegment === 'function') {
        app.use(AWSXRay.express.closeSegment());
      }
    }
  } catch (err) {
    logger.error('AWS X-Ray segment kapatma hatası:', err);
  }
}

// Beklenmeyen hatalar için
process.on('unhandledRejection', (err) => {
  logger.error('UNHANDLED REJECTION!', err.name, err.message);
  logger.error('Stack:', err.stack);
  server.close(() => {
    process.exit(1);
  });
});

process.on('SIGTERM', () => {
  logger.warn('SIGTERM RECEIVED. Shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated!');
  });
});

module.exports = server; 