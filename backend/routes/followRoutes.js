const express = require('express');
const followController = require('../controllers/followController');
const authController = require('../controllers/authController');

const router = express.Router();

// Tüm rotaları koruma - Kimlik doğrulama gerektirir
router.use(authController.protect);

// Takip işlemleri
router.post('/users/:userId/follow', followController.followUser);
router.delete('/users/:userId/follow', followController.unfollowUser);

// Alternatif rota - Postman'den gelen istekler için
router.post('/users/:userId', followController.followUser);
router.delete('/users/:userId', followController.unfollowUser);

// Takip istekleri
router.get('/requests', followController.getFollowRequests);
router.patch('/requests/:requestId/accept', followController.acceptFollowRequest);
router.patch('/requests/:requestId/reject', followController.rejectFollowRequest);

// Takipçi ve takip edilen listeleri
router.get('/followers', followController.getFollowers);
router.get('/following', followController.getFollowing);
router.get('/users/:userId/followers', followController.getFollowers);
router.get('/users/:userId/following', followController.getFollowing);

module.exports = router; 