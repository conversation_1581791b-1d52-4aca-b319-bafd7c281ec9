const express = require('express');
const messageController = require('../controllers/messageController');
const authController = require('../controllers/authController');

const router = express.Router();

// Tüm rotaları koruma - Kimlik doğrulama gerektirir
router.use(authController.protect);

// Mesajlaşma işlemleri
router.post('/send', messageController.sendMessage);
router.get('/conversations', messageController.getConversations);
router.get('/conversations/:conversationId', messageController.getMessages);
router.post('/conversations', messageController.createConversation);
router.delete('/conversations/:conversationId', messageController.deleteConversation);
router.delete('/messages/:messageId', messageController.deleteMessage);

// Grup mesajlaşma işlemleri
router.post('/groups', messageController.createGroupConversation);
router.get('/groups/:groupId', messageController.getGroupConversation); // Yeni eklenen rota - grup bilgilerini getir
router.get('/groups/:groupId/messages', messageController.getMessages);
router.post('/groups/:groupId/users', messageController.addUserToGroup);
router.delete('/groups/:groupId/users/:userId', messageController.removeUserFromGroup);
router.post('/groups/admin', messageController.makeGroupAdmin);
router.patch('/groups/:groupId', messageController.updateGroupConversation);
router.delete('/groups/:groupId', messageController.deleteGroupConversation);

module.exports = router;