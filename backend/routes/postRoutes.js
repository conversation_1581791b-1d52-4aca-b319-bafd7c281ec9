const express = require('express');
const postController = require('../controllers/postController');
const authController = require('../controllers/authController');
const { upload, uploadPostImage, debugMulter } = require('../middleware/uploadMiddleware');

const router = express.Router();

// Tüm rotaları koruma - Kimlik doğrulama gerektirir
router.use(authController.protect);

// Gönderi işlemleri
router.post(
  '/',
  debugMulter,
  uploadPostImage,
  postController.createPost
);

router.get('/', postController.getAllPosts);
router.get('/feed', postController.getFeedPosts);
router.get('/discover', postController.getDiscoverPosts);
router.get('/:id', postController.getPost);

router.patch(
  '/:id',
  postController.updatePost
);

router.delete('/:id', postController.deletePost);

// Beğenme/Kaydetme işlemleri
router.post('/:id/like', postController.likeUnlikePost);
router.post('/:id/save', postController.saveUnsavePost);

// Yorum işlemleri
router.get('/:id/comments', postController.getPostComments);
router.post('/:id/comments', postController.addComment);
router.delete('/:id/comments/:commentId', postController.deleteComment);

// Yorum beğenme işlemleri
router.post('/:id/comments/:commentId/like', postController.likeUnlikeComment);

module.exports = router;