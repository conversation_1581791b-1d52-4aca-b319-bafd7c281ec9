const express = require('express');
const discoveryController = require('../controllers/discoveryController');
const authController = require('../controllers/authController');

const router = express.Router();

// Tüm rotaları koruma - Kimlik doğrulama gerektirir
router.use(authController.protect);

// Trend olan gönderileri keşfet
router.get('/trending', discoveryController.discoverTrendingPosts);

// Kişiselleştirilmiş öneriler
router.get('/for-you', discoveryController.discoverForYouPosts);

// <PERSON>er kullanıcıları keşfet
router.get('/similar-users', discoveryController.discoverSimilarUsers);

// Benzer kıyafetleri keşfet
router.get('/similar-clothes', discoveryController.discoverSimilarClothes);

module.exports = router; 