const express = require('express');
const authController = require('../controllers/authController');
const limiter = require('../middleware/rateLimiter');
const { uploadProfilePicture } = require('../middleware/uploadMiddleware');

const router = express.Router();

// Kayıt olma route'ları - 2 aşamalı
router.post('/signup/step1', authController.signupStepOne);
router.post('/signup/step2', uploadProfilePicture, authController.signupStepTwo);

// Eski kayıt route'u uygulaması bozmamak için korunuyor (not: zamanla kaldırılabilir)
router.post('/signup', authController.signupStepOne);

// Giriş yapma route'u (rate limiter ile korunuyor)
router.post('/login', limiter.loginLimiter, authController.login);

// Sosyal medya ile giriş/kayıt route'ları
router.post('/google', authController.googleAuth);
router.post('/apple', authController.appleAuth);

// Çıkış yapma route'u
router.post('/logout', authController.logout);

// Token yenileme route'u
router.post('/refresh-token', authController.refreshToken);

// Şifre işlemleri
router.post('/forgot-password', limiter.passwordResetLimiter, authController.forgotPassword);
router.patch('/reset-password/:token', authController.resetPassword);

// Şifre sıfırlama token'ı için GET isteği - frontend'e yönlendir
router.get('/reset-password/:token', (req, res) => {
  const token = req.params.token;
  // Mobile uygulama için deep link oluştur
  // Bu, mobil cihazda uygulamayı açacak şekilde yapılandırılmalı
  // Alternatif olarak web için yönlendirme kullanılabilir
  
  // Mobil cihaz tespiti
  const userAgent = req.headers['user-agent'];
  const isMobile = /mobile|android|iphone|ipod|blackberry/i.test(userAgent);
  
  if (isMobile) {
    // Mobil deep link - eğer uygun bir şema oluşturulmuşsa (örn. styleup://)
    res.redirect(`styleup://reset-password/${token}`);
  } else {
    // Web uygulaması için normal yönlendirme
    res.redirect(`${process.env.FRONTEND_URL}/reset-password/${token}`);
  }
});

router.patch(
  '/update-password',
  authController.protect,
  authController.updatePassword
);

// Kullanıcı bilgilerini al (token korumalı)
router.get('/me', authController.protect, (req, res) => {
  res.status(200).json({
    status: 'success',
    data: {
      user: req.user
    }
  });
});

module.exports = router; 