const express = require('express');
const userController = require('../controllers/userController');
const authController = require('../controllers/authController');
const { uploadProfilePicture } = require('../middleware/uploadMiddleware');

const router = express.Router();

// Kimlik doğrulama olmadan erişilebilen rotalar
router.get('/search', userController.searchUsers);

// Kimlik doğrulama gerektiren rotalar
router.use(authController.protect);

// Kullanıcı profili işlemleri - özel rotalar önce
router.get('/me/profile', userController.getProfile);
router.get('/me/posts', userController.getUserPosts);
router.get('/me/saved-posts', userController.getSavedPosts);

router.patch(
  '/profile',
  uploadProfilePicture,
  userController.updateProfile
);
router.delete('/delete-account', userController.deleteAccount);

// Admin işlemleri
router.get(
  '/admin/users',
  authController.protect,
  userController.getAllUsers
);

router.patch(
  '/admin/users/:userId/role',
  authController.protect,
  userController.updateUserRole
);

// Parametreli rotalar en sona (dinamik rotalar)
router.get('/:userId', userController.getProfile);
router.get('/:userId/posts', userController.getUserPosts);
// Kullanıcı ID ile profil güncelleme rotası
router.patch('/:userId', userController.updateProfile);

module.exports = router; 