const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 30
  },
  fullName: {
    type: String,
    required: [true, 'Ad Soyad zorunludur'],
    trim: true,
    minlength: 2,
    maxlength: 50
  },
  email: {
    type: String,
    required: [true, 'Email zorunludur'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Lütfen geçerli bir email adresi giriniz'
    ]
  },
  password: {
    type: String,
    required: [true, 'Şifre zorunludur'],
    minlength: 6,
    select: false // Varsayılan olarak şifreyi çekme
  },
  profilePicture: {
    type: String,
    default: 'default-profile.jpg'
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  followers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  following: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  posts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post'
  }],
  savedPosts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post'
  }],
  likedPosts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post'
  }],
  isPrivate: {
    type: Boolean,
    default: false
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  provider: {
    type: String,
    enum: ['local', 'google', 'apple'],
    default: 'local'
  },
  passwordResetToken: String,
  passwordResetExpires: Date,
  active: {
    type: Boolean,
    default: true,
    select: false
  },
  devices: [{
    deviceId: {
      type: String,
      required: true
    },
    deviceModel: String,
    appVersion: String,
    lastSeen: {
      type: Date,
      default: Date.now
    },
    isBlocked: {
      type: Boolean,
      default: false
    }
  }],
  lastLogin: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  passwordChangedAt: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Kaydetmeden önce şifreyi hashleme
userSchema.pre('save', async function(next) {
  // Şifre değişmediyse hashlemeye gerek yok
  if (!this.isModified('password')) return next();
  
  // Şifreyi hashle
  this.password = await bcrypt.hash(this.password, 12);
  next();
});

// Şifre değiştikten sonra passwordChangedAt alanını güncelle
userSchema.pre('save', function(next) {
  if (!this.isModified('password') || this.isNew) return next();
  
  // Ufak bir gecikme ekleyerek token oluşturma zamanından sonra olmasını garanti ediyoruz
  this.passwordChangedAt = Date.now() - 1000;
  next();
});

// Giriş için şifre karşılaştırma metodu
userSchema.methods.correctPassword = async function(candidatePassword, userPassword) {
  return await bcrypt.compare(candidatePassword, userPassword);
};

// Şifre değişikliği kontrolü metodu
userSchema.methods.changedPasswordAfter = function(JWTTimestamp) {
  if (this.passwordChangedAt) {
    const changedTimestamp = parseInt(
      this.passwordChangedAt.getTime() / 1000,
      10
    );
    return JWTTimestamp < changedTimestamp;
  }
  
  // False döndürme, şifre hiç değiştirilmemiş anlamına gelir
  return false;
};

// Parola sıfırlama token'ı oluşturma
userSchema.methods.createPasswordResetToken = function() {
  // crypto modülünü kullanarak rastgele token oluştur
  const crypto = require('crypto');
  const resetToken = crypto.randomBytes(32).toString('hex');
  
  // Token'ı hashleyerek veritabanında sakla
  this.passwordResetToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');
    
  // Token'ın 10 dakika sonra geçersiz olmasını ayarla
  this.passwordResetExpires = Date.now() + 10 * 60 * 1000;
  
  return resetToken;
};

// Sanal alan: Takipçi sayısı
userSchema.virtual('followerCount').get(function() {
  return this.followers ? this.followers.length : 0;
});

// Sanal alan: Takip edilen sayısı
userSchema.virtual('followingCount').get(function() {
  return this.following ? this.following.length : 0;
});

// Sanal alan: Gönderi sayısı
userSchema.virtual('postCount').get(function() {
  return this.posts ? this.posts.length : 0;
});

const User = mongoose.model('User', userSchema);

module.exports = User; 