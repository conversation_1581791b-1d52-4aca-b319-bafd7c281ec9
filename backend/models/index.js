const mongoose = require('mongoose');
const User = require('./User');
const Post = require('./Post');
const Comment = require('./Comment');
const { Message, Conversation, GroupConversation } = require('./Message');
const Follow = require('./Follow');
const BlacklistedToken = require('./BlacklistedToken');

// MongoDB bağlantı URL'si
const db = {};

db.mongoose = mongoose;
db.user = User;
db.post = Post;
db.comment = Comment;
db.message = Message;
db.conversation = Conversation;
db.groupConversation = GroupConversation;
db.follow = Follow;
db.blacklistedToken = BlacklistedToken;

module.exports = db; 