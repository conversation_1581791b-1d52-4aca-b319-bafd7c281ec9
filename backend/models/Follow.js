const mongoose = require('mongoose');

const followSchema = new mongoose.Schema({
  follower: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Takip eden kullanıcı belirtilmelidir']
  },
  following: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Takip edilen kullanıcı belirtilmelidir']
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'rejected'],
    default: 'accepted' // Özel hesaplar için 'pending' olabilir
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Aynı takip ilişkisinin tekrar oluşturulmasını engellemek için bileşik indeks
followSchema.index({ follower: 1, following: 1 }, { unique: true });

// Takip ilişkisi oluşturulduğunda kullanıcıların followers ve following dizilerini güncelle
followSchema.post('save', async function() {
  try {
    // Eğer takip isteği kabul edildiyse
    if (this.status === 'accepted' && this.isActive) {
      // Takip eden kullanıcının following dizisine takip edilen kullanıcıyı ekle
      await mongoose.model('User').findByIdAndUpdate(
        this.follower,
        { $addToSet: { following: this.following } }
      );
      
      // Takip edilen kullanıcının followers dizisine takip eden kullanıcıyı ekle
      await mongoose.model('User').findByIdAndUpdate(
        this.following,
        { $addToSet: { followers: this.follower } }
      );
    }
  } catch (err) {
    console.error('Follow post save middleware error:', err);
  }
});

// Takip ilişkisi güncellendiğinde kullanıcıların followers ve following dizilerini güncelle
followSchema.pre('findOneAndUpdate', async function() {
  try {
    // Güncellenecek belgeyi bul
    const docToUpdate = await this.model.findOne(this.getQuery());
    if (docToUpdate) {
      // Eski durumu ve aktiflik bilgisini sakla
      this._oldStatus = docToUpdate.status;
      this._oldIsActive = docToUpdate.isActive;
      
      // Güncellenecek alanları kontrol et
      const update = this.getUpdate();
      this._newStatus = update.$set?.status || docToUpdate.status;
      this._newIsActive = update.$set?.isActive !== undefined ? update.$set.isActive : docToUpdate.isActive;
    }
  } catch (err) {
    console.error('Follow pre findOneAndUpdate middleware error:', err);
  }
});

followSchema.post('findOneAndUpdate', async function(doc) {
  try {
    if (!doc) return;
    
    // Durum değişikliği kontrolü
    const statusChanged = this._oldStatus !== this._newStatus;
    const activeChanged = this._oldIsActive !== this._newIsActive;
    
    // Eğer takip isteği kabul edildiyse ve önceki durumu 'pending' ise
    if (this._newStatus === 'accepted' && statusChanged && this._newIsActive) {
      // Takip eden kullanıcının following dizisine takip edilen kullanıcıyı ekle
      await mongoose.model('User').findByIdAndUpdate(
        doc.follower,
        { $addToSet: { following: doc.following } }
      );
      
      // Takip edilen kullanıcının followers dizisine takip eden kullanıcıyı ekle
      await mongoose.model('User').findByIdAndUpdate(
        doc.following,
        { $addToSet: { followers: doc.follower } }
      );
    }
    
    // Eğer takip ilişkisi reddedildiyse veya kaldırıldıysa
    if ((this._newStatus === 'rejected' && statusChanged) || 
        (activeChanged && !this._newIsActive)) {
      // Takip eden kullanıcının following dizisinden takip edilen kullanıcıyı çıkar
      await mongoose.model('User').findByIdAndUpdate(
        doc.follower,
        { $pull: { following: doc.following } }
      );
      
      // Takip edilen kullanıcının followers dizisinden takip eden kullanıcıyı çıkar
      await mongoose.model('User').findByIdAndUpdate(
        doc.following,
        { $pull: { followers: doc.follower } }
      );
    }
  } catch (err) {
    console.error('Follow post findOneAndUpdate middleware error:', err);
  }
});

const Follow = mongoose.model('Follow', followSchema);

module.exports = Follow; 