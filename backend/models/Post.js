const mongoose = require('mongoose');

// Kıyafet bağlantısı şeması
const clothingLinkSchema = new mongoose.Schema({
  type: {
    type: String,
    // required: [true, '<PERSON>ıyafet türü belirtilmelidir'],
    trim: true
  },
  url: {
    type: String,
    // required: [true, 'Bağlantı URL\'i belirtilmelidir'],
    trim: true
  },
  title: {
    type: String,
    // required: [true, 'Bağlantı başlığı belirtilmelidir'],
    trim: true
  },
  price: {
    type: Number
  },
  brand: {
    type: String,
    trim: true
  },
  yakaType: {
    type: String,
    enum: ['', '<PERSON> Yaka', '<PERSON><PERSON><PERSON><PERSON> Yaka', '<PERSON> Yaka', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    trim: true,
    default: ''
  },
  color: {
    type: String,
    trim: true,
    default: ''
  }
}, { _id: true });

const postSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '<PERSON><PERSON><PERSON><PERSON> bir kullanıcıya ait olmalıdır']
  },
  image: {
    type: String,
    required: [true, 'Gönderi bir resim içermelidir']
  },
  filter: {
    type: String,
    default: 'No Filter'
  },
  description: {
    type: String,
    trim: true,
    maxlength: 2000
  },
  tags: [{
    type: String,
    trim: true
  }],
  clothingLinks: [clothingLinkSchema],
  likes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  comments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment'
  }],
  location: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isApproved: {
    type: Boolean,
    default: true // Admin onayı gerekiyorsa false yapılabilir
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Sanal alan: Beğeni sayısı
postSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Sanal alan: Yorum sayısı
postSchema.virtual('commentCount').get(function() {
  return this.comments ? this.comments.length : 0;
});

// Sanal alan: Etkileşim puanı (keşfet bölümü için)
postSchema.virtual('interactionScore').get(function() {
  // Beğeni ve yorumların ağırlıklı toplamı
  // Yorumlar beğenilerden daha değerli olabilir
  const likeWeight = 1;
  const commentWeight = 3;

  const likesCount = this.likes ? this.likes.length : 0;
  const commentsCount = this.comments ? this.comments.length : 0;

  return (likesCount * likeWeight) + (commentsCount * commentWeight);
});

// Gönderi silindiğinde ilgili yorumları da sil
postSchema.pre('remove', async function(next) {
  await this.model('Comment').deleteMany({ post: this._id });
  next();
});

// Etkileşim puanına göre sıralama için indeks
postSchema.index({ createdAt: -1 });

const Post = mongoose.model('Post', postSchema);

module.exports = Post;