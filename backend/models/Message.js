const mongoose = require('mongoose');

// Tekil mesaj <PERSON>
const messageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '<PERSON><PERSON><PERSON>en kullanıcı belirtilmelidir']
  },
  conversation: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'conversationType',
    required: [true, 'Mesaj bir sohbete ait olmalıdır']
  },
  conversationType: {
    type: String,
    required: true,
    enum: ['Conversation', 'GroupConversation']
  },
  text: {
    type: String,
    required: [true, 'Mesaj metni zorunludur'],
    trim: true,
    maxlength: 2000
  },
  attachments: [{
    type: String
  }],
  isRead: {
    type: Boolean,
    default: false
  },
  readBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    readAt: {
      type: Date,
      default: Date.now
    }
  }],
  readAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Sohbet şeması (iki kullanıcı arasındaki tüm mesajları içerir)
const conversationSchema = new mongoose.Schema({
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  messages: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  }],
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  lastMessageDate: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Grup sohbet şeması
const groupConversationSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Grup adı belirtilmelidir'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Grup oluşturucu belirtilmelidir']
  },
  admins: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  messages: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  }],
  groupImage: {
    type: String,
    default: 'default-group.png'
  },
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  lastMessageDate: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Sanal alan: Okunmamış mesaj sayısı
conversationSchema.virtual('unreadCount').get(function() {
  if (!this.messages) return 0;
  return this.messages.filter(message => !message.isRead).length;
});

// Sanal alan: Toplam mesaj sayısı
conversationSchema.virtual('messageCount').get(function() {
  return this.messages ? this.messages.length : 0;
});

// Her iki katılımcı için de sohbeti kolayca bulabilmek için bileşik indeks
conversationSchema.index({ participants: 1 });

// Sohbetleri son mesaj tarihine göre sıralamak için indeks
conversationSchema.index({ lastMessageDate: -1 });

// Grup sohbetleri için sanal alanlar
groupConversationSchema.virtual('unreadCount').get(function() {
  if (!this.messages) return 0;
  return this.messages.filter(message => !message.isRead).length;
});

groupConversationSchema.virtual('messageCount').get(function() {
  return this.messages ? this.messages.length : 0;
});

groupConversationSchema.virtual('participantCount').get(function() {
  return this.participants ? this.participants.length : 0;
});

// Grup sohbetleri için indeksler
groupConversationSchema.index({ participants: 1 });
groupConversationSchema.index({ lastMessageDate: -1 });
groupConversationSchema.index({ name: 'text', description: 'text' });

const Message = mongoose.model('Message', messageSchema);
const Conversation = mongoose.model('Conversation', conversationSchema);
const GroupConversation = mongoose.model('GroupConversation', groupConversationSchema);

module.exports = { Message, Conversation, GroupConversation }; 