const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Yorum bir kullanıcıya ait olmalıdır']
  },
  post: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Post',
    required: [true, 'Yorum bir gönderiye ait olmalıdır']
  },
  text: {
    type: String,
    required: [true, 'Yorum metni zorunludur'],
    trim: true,
    maxlength: 1000
  },
  likes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  // Alt yorumlar için (yanıtlar)
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Comment',
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Sanal alan: <PERSON><PERSON>eni sayısı
commentSchema.virtual('likeCount').get(function() {
  return this.likes ? this.likes.length : 0;
});

// Yorum silindiğinde alt yorumları da sil (document middleware)
commentSchema.pre('remove', async function(next) {
  try {
    await mongoose.model('Comment').deleteMany({ parent: this._id });
    next();
  } catch (err) {
    console.error('Comment pre remove middleware error:', err);
    next(err);
  }
});

// findByIdAndDelete ve findOneAndDelete işlemleri için middleware
commentSchema.pre('findOneAndDelete', async function(next) {
  try {
    // Silinecek belgeyi bul
    const docToDelete = await this.model.findOne(this.getQuery());
    if (docToDelete) {
      console.log(`Pre findOneAndDelete middleware: Found comment to delete with ID: ${docToDelete._id}`);
      // Alt yorumları sil
      const deleteResult = await mongoose.model('Comment').deleteMany({ parent: docToDelete._id });
      console.log(`Pre findOneAndDelete middleware: Deleted ${deleteResult.deletedCount} child comments`);
    }
    next();
  } catch (err) {
    console.error('Comment pre findOneAndDelete middleware error:', err);
    next(err);
  }
});

// Aynı middleware'i findByIdAndDelete için de çalıştır
commentSchema.pre('findByIdAndDelete', async function(next) {
  try {
    console.log('findByIdAndDelete middleware triggered');
    const docToDelete = await this.model.findOne(this.getQuery());
    if (docToDelete) {
      console.log(`Pre findByIdAndDelete middleware: Found comment to delete with ID: ${docToDelete._id}`);
      const deleteResult = await mongoose.model('Comment').deleteMany({ parent: docToDelete._id });
      console.log(`Pre findByIdAndDelete middleware: Deleted ${deleteResult.deletedCount} child comments`);
    } else {
      console.log('Pre findByIdAndDelete middleware: No document found to delete');
    }
    next();
  } catch (err) {
    console.error('Comment pre findByIdAndDelete middleware error:', err);
    next(err);
  }
});

// Yorum eklendiğinde ilgili gönderinin comments dizisine ekle
commentSchema.post('save', async function() {
  try {
    console.log(`Post save middleware triggered for comment ${this._id}, isActive: ${this.isActive}`);

    // Eğer bu bir alt yorum değilse (ana yorumsa)
    if (!this.parent) {
      // Eğer yorum aktifse, gönderi comments dizisine ekle
      if (this.isActive) {
        const updateResult = await mongoose.model('Post').findByIdAndUpdate(
          this.post,
          { $addToSet: { comments: this._id } },
          { new: true }
        );
        console.log(`Comment ${this._id} added to post ${this.post} comments array (via middleware)`);
      }
      // Eğer yorum pasifse (silinmişse), gönderi comments dizisinden çıkar
      else {
        const updateResult = await mongoose.model('Post').findByIdAndUpdate(
          this.post,
          { $pull: { comments: this._id } },
          { new: true }
        );
        console.log(`Comment ${this._id} removed from post ${this.post} comments array (via middleware)`);
      }
    }
  } catch (err) {
    console.error('Comment post save middleware error:', err);
  }
});

// Yorum silindiğinde post'un comments dizisinden kaldır
commentSchema.post('findOneAndDelete', async function(doc) {
  try {
    if (doc && !doc.parent) {
      console.log(`Post findOneAndDelete middleware: Removing comment ${doc._id} from post ${doc.post}`);
      const updateResult = await mongoose.model('Post').findByIdAndUpdate(
        doc.post,
        { $pull: { comments: doc._id } },
        { new: true }
      );
      console.log(`Post update result after comment deletion:`, updateResult ? 'Success' : 'Failed');
    }
  } catch (err) {
    console.error('Comment post findOneAndDelete middleware error:', err);
  }
});

// findByIdAndDelete için de aynı işlemi yap
commentSchema.post('findByIdAndDelete', async function(doc) {
  try {
    if (doc && !doc.parent) {
      console.log(`Post findByIdAndDelete middleware: Removing comment ${doc._id} from post ${doc.post}`);
      const updateResult = await mongoose.model('Post').findByIdAndUpdate(
        doc.post,
        { $pull: { comments: doc._id } },
        { new: true }
      );
      console.log(`Post update result after comment deletion:`, updateResult ? 'Success' : 'Failed');
    }
  } catch (err) {
    console.error('Comment post findByIdAndDelete middleware error:', err);
  }
});

const Comment = mongoose.model('Comment', commentSchema);

module.exports = Comment;