{"name": "sosyal-medya-backend", "version": "1.0.0", "description": "Sosyal medya uygulaması için güvenli backend API", "main": "server.js", "scripts": {"start": "node server.js", "build": "npm install --production", "dev": "nodemon server.js", "test": "jest --coverage", "test:watch": "jest --watch --runInBand", "test:coverage": "jest --coverage --runInBand --collectCoverageFrom='**/*.js' --collectCoverageFrom='!**/node_modules/**' --forceExit"}, "keywords": ["nodejs", "express", "mongodb", "jwt", "authentication", "social-media"], "author": "", "license": "ISC", "dependencies": {"aws-cloudwatch-log": "^0.1.6", "aws-sdk": "^2.1450.0", "aws-xray-sdk": "^3.10.3", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "multer": "^1.4.5-lts.1", "multer-s3": "^2.10.0", "nodemailer": "^6.9.1", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-google-oauth20": "^2.0.0", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^8.12.2", "nodemon": "^2.0.22", "supertest": "^6.3.4"}}