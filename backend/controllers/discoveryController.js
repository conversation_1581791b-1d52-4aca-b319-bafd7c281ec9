const User = require('../models/User');
const Post = require('../models/Post');
const { logger } = require('../utils/logger');

/**
 * Trend olan gönderileri keşfet
 * Bu endpoint, en çok beğeni ve yorum alan güncel gönderileri bulur
 */
exports.discoverTrendingPosts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Trend algoritması: Beğeni ve yorum sayısı ağırlıklı skor hesaplama
    // NOT: Burada  discovery algoritması yerleştirilebilir
    
    const trendingPosts = await Post.aggregate([
      // Aktif gönderileri filtrele
      { $match: { isActive: true } },
      
      // Son 7 gün içindeki gönderileri al
      { 
        $match: { 
          createdAt: { 
            $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
          } 
        } 
      },
      
      // Gönderi bilgilerini birleştir
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDetails'
        }
      },
      
      // Beğeni ve yorum sayılarını hesapla
      {
        $addFields: {
          likesCount: { $size: { $ifNull: ["$likes", []] } },
          commentsCount: { $size: { $ifNull: ["$comments", []] } },
          userDetails: { $arrayElemAt: ["$userDetails", 0] }
        }
      },
      
      // Trend skoru hesapla (beğeni x 1 + yorum x 2)
      {
        $addFields: {
          trendScore: { 
            $add: [
              "$likesCount", 
              { $multiply: ["$commentsCount", 2] }
            ] 
          }
        }
      },
      
      // Trend skoruna göre sırala
      { $sort: { trendScore: -1 } },
      
      // Sayfalama için skip ve limit
      { $skip: skip },
      { $limit: limit },
      
      // Döndürülecek alanları seç
      {
        $project: {
          _id: 1,
          image: 1,
          description: 1,
          tags: 1,
          clothingLinks: 1,
          location: 1,
          likesCount: 1,
          commentsCount: 1,
          trendScore: 1,
          createdAt: 1,
          "userDetails.username": 1,
          "userDetails.profilePicture": 1,
          "userDetails._id": 1
        }
      }
    ]);
    
    // Toplam gönderi sayısını hesapla (pagination için)
    const total = await Post.countDocuments({
      isActive: true,
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    });
    
    res.status(200).json({
      status: 'success',
      results: trendingPosts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts: trendingPosts
      }
    });
  } catch (err) {
    logger.error('Trending posts discovery error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

/**
 * Kullanıcının ilgi alanlarına göre önerilen gönderileri keşfet
 * Bu endpoint, kullanıcının geçmiş beğeni ve etiket tercihlerine göre içerik önerir
 */
exports.discoverForYouPosts = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Kullanıcının son beğendiği gönderileri bul
    const user = await User.findById(req.user.id);
    
    // Kullanıcının ilgi alanlarını belirle
    // NOT: Burada  kişiselleştirilmiş öneri algoritması yerleştirilebilir
    
    // 1. Kullanıcının beğendiği gönderilerin etiketlerini bul
    const likedPosts = await Post.find({
      _id: { $in: user.likedPosts || [] }
    }).select('tags');
    
    // 2. Etiketleri sayılarına göre topla
    const tagCounts = {};
    likedPosts.forEach(post => {
      post.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    });
    
    // 3. En çok ilgi duyulan etiketleri bul (top 5)
    const topTags = Object.entries(tagCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(entry => entry[0]);
    
    // 4. Bu etiketlere sahip, kullanıcının henüz görmediği gönderileri getir
    const recommendedPosts = await Post.find({
      tags: { $in: topTags },
      user: { $ne: req.user.id }, // Kullanıcının kendi postlarını hariç tut
      _id: { $nin: user.likedPosts || [] }, // Zaten beğenilen postları hariç tut
      isActive: true
    })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('user', 'username profilePicture');
    
    // Toplam gönderi sayısını hesapla
    const total = await Post.countDocuments({
      tags: { $in: topTags },
      user: { $ne: req.user.id },
      _id: { $nin: user.likedPosts || [] },
      isActive: true
    });
    
    res.status(200).json({
      status: 'success',
      results: recommendedPosts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts: recommendedPosts
      }
    });
  } catch (err) {
    logger.error('For you posts discovery error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

/**
 * Benzer stil sahibi kullanıcıları keşfet
 * Bu endpoint, kullanıcıya benzer stil tercihlerine sahip diğer kullanıcıları önerir
 */
exports.discoverSimilarUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // NOT: Burada  benzer kullanıcı bulma algoritması yerleştirilebilir
    
    // 1. Kullanıcının ilgilendiği etiketleri bul
    const currentUser = await User.findById(req.user.id);
    
    // Kullanıcının gönderi ve beğenilerinden etiketleri topla
    const userPosts = await Post.find({ user: req.user.id }).select('tags');
    const likedPosts = await Post.find({
      _id: { $in: currentUser.likedPosts || [] }
    }).select('tags');
    
    // Tüm etiketleri birleştir
    const allTags = new Set();
    userPosts.forEach(post => post.tags.forEach(tag => allTags.add(tag)));
    likedPosts.forEach(post => post.tags.forEach(tag => allTags.add(tag)));
    
    // 2. Benzer etiketleri kullanan diğer kullanıcıları bul
    const similarUsersPosts = await Post.aggregate([
      // Etiketlere sahip gönderileri filtrele
      { $match: { tags: { $in: Array.from(allTags) } } },
      // Kullanıcının kendisini hariç tut
      { $match: { user: { $ne: req.user._id } } },
      // Kullanıcılara göre grupla
      {
        $group: {
          _id: "$user",
          commonTagsCount: { 
            $sum: {
              $size: {
                $setIntersection: ["$tags", Array.from(allTags)]
              }
            }
          },
          totalPosts: { $sum: 1 }
        }
      },
      // Ortak etiket sayısına göre sırala
      { $sort: { commonTagsCount: -1 } },
      // Sayfalama
      { $skip: skip },
      { $limit: limit }
    ]);
    
    // 3. Kullanıcı detaylarını getir
    const userIds = similarUsersPosts.map(post => post._id);
    const similarUsers = await User.find({
      _id: { $in: userIds },
      isActive: true
    }).select('username profilePicture bio');
    
    // 4. Sonuçları birleştir
    const result = similarUsers.map(user => {
      const postData = similarUsersPosts.find(post => 
        post._id.toString() === user._id.toString()
      );
      return {
        ...user.toObject(),
        commonTagsCount: postData.commonTagsCount,
        totalPosts: postData.totalPosts
      };
    });
    
    // Toplam sayı
    const total = await Post.aggregate([
      { $match: { tags: { $in: Array.from(allTags) } } },
      { $match: { user: { $ne: req.user._id } } },
      { $group: { _id: "$user" } },
      { $count: "total" }
    ]);
    
    res.status(200).json({
      status: 'success',
      results: result.length,
      total: total.length > 0 ? total[0].total : 0,
      totalPages: total.length > 0 ? Math.ceil(total[0].total / limit) : 0,
      currentPage: page,
      data: {
        users: result
      }
    });
  } catch (err) {
    logger.error('Similar users discovery error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

/**
 * Belirli bir giyim tarzına göre benzer kıyafetleri keşfet
 */
exports.discoverSimilarClothes = async (req, res) => {
  try {
    const { type, brand, minPrice, maxPrice } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Sorgu filtreleri oluştur
    const filters = {};
    
    if (type) {
      filters['clothingLinks.type'] = type;
    }
    
    if (brand) {
      filters['clothingLinks.brand'] = brand;
    }
    
    if (minPrice || maxPrice) {
      filters['clothingLinks.price'] = {};
      if (minPrice) filters['clothingLinks.price'].$gte = parseFloat(minPrice);
      if (maxPrice) filters['clothingLinks.price'].$lte = parseFloat(maxPrice);
    }
    
    // Aktif gönderileri filtrele
    filters.isActive = true;
    
    // NOT: Burada  benzer kıyafet bulma algoritması yerleştirilebilir
    
    const posts = await Post.find(filters)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'username profilePicture');
    
    const total = await Post.countDocuments(filters);
    
    res.status(200).json({
      status: 'success',
      results: posts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts
      }
    });
  } catch (err) {
    logger.error('Similar clothes discovery error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

module.exports = exports; 