const jwt = require('jsonwebtoken');
const { promisify } = require('util');
const crypto = require('crypto');
const User = require('../models/User');
const BlacklistedToken = require('../models/BlacklistedToken');
const sendEmail = require('../utils/email');
const logger = require('../utils/logger');
const { DEFAULT_PROFILE_IMAGE } = require('../middleware/uploadMiddleware');

// Token oluşturma fonksiyonu
const signToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN
  });
};

// Refresh token oluşturma
const signRefreshToken = (id) => {
  return jwt.sign({ id }, process.env.REFRESH_TOKEN_SECRET, {
    expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN
  });
};

// Token gönderme fonksiyonu
const createSendToken = (user, statusCode, res) => {
  const token = signToken(user._id);
  const refreshToken = signRefreshToken(user._id);

  // Kullanıcının şifresini yanıtta gösterme
  user.password = undefined;

  res.status(statusCode).json({
    status: 'success',
    token,
    refreshToken,
    data: {
      user
    }
  });
};

// KAYIT OLMA 1. AŞAMA
exports.signupStepOne = async (req, res) => {
  try {
    // E-posta kontrolü
    const existingUser = await User.findOne({ email: req.body.email });
    if (existingUser) {
      return res.status(400).json({
        status: 'fail',
        message: 'Bu e-posta adresi zaten kullanılıyor'
      });
    }

    // Geçici kullanıcı oluştur (veritabanına kaydetmeden)
    const userData = {
      fullName: req.body.fullName,
      email: req.body.email,
      password: req.body.password,
      profilePicture: DEFAULT_PROFILE_IMAGE // S3'teki varsayılan profil resmi
    };

    // Geçici bilgileri oluştur ve oturum veya önbelleğe kaydet
    // Bu örnek için JWT kullanıyoruz, ama Redis gibi bir cache sistemi daha uygun olabilir
    const tempToken = jwt.sign(userData, process.env.JWT_SECRET, {
      expiresIn: '15m' // Geçici token 15 dakika geçerli
    });

    res.status(200).json({
      status: 'success',
      tempToken,
      profilePicture: userData.profilePicture,
      message: 'Kayıt işleminin ilk adımı tamamlandı, lütfen kullanıcı adı seçin'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// KAYIT OLMA 2. AŞAMA
exports.signupStepTwo = async (req, res) => {
  try {
    const { tempToken, username } = req.body;

    if (!tempToken || !username) {
      return res.status(400).json({
        status: 'fail',
        message: 'Geçici token ve kullanıcı adı gereklidir'
      });
    }

    // Geçici token'ı doğrula
    const decodedToken = await promisify(jwt.verify)(tempToken, process.env.JWT_SECRET);

    // Kullanıcı adının benzersiz olduğunu kontrol et
    const existingUsername = await User.findOne({ username });
    if (existingUsername) {
      return res.status(400).json({
        status: 'fail',
        message: 'Bu kullanıcı adı zaten kullanılıyor'
      });
    }

    // Profil resmi kontrolü - eğer yüklenen resim varsa, onu kullan
    let profilePicture = decodedToken.profilePicture; // Varsayılan S3 resim URL'ini al

    // Eğer req.file varsa (kullanıcı bir resim yükledi) ve multer-s3 kullanıyorsa
    if (req.file && req.file.location) {
      // S3'e yüklenen resmin URL'ini kullan
      profilePicture = req.file.location;
    }

    // Token'dan gelen bilgilerle yeni kullanıcı oluştur
    const newUser = await User.create({
      fullName: decodedToken.fullName,
      email: decodedToken.email,
      password: decodedToken.password,
      username: username,
      profilePicture: profilePicture,
      provider: decodedToken.provider || 'local'
    });

    // Access ve refresh token oluştur
    createSendToken(newUser, 201, res);
  } catch (err) {
    if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
      return res.status(401).json({
        status: 'fail',
        message: 'Geçici token geçersiz veya süresi dolmuş, lütfen tekrar kayıt olmayı deneyin'
      });
    }

    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcı adı veya email ile GİRİŞ YAPMA
exports.login = async (req, res) => {
  try {
    const { identifier, password } = req.body;

    // 1) Kullanıcı adı/Email ve şifre var mı kontrol et
    if (!identifier || !password) {
      return res.status(400).json({
        status: 'fail',
        message: 'Lütfen kullanıcı adı/email ve şifre girin'
      });
    }

    // 2) Kullanıcı var mı ve şifre doğru mu kontrol et
    // Kullanıcı adı veya email ile sorgula
    const user = await User.findOne({
      $or: [
        { email: identifier },
        { username: identifier }
      ]
    }).select('+password');

    // Kullanıcı bulunamadı
    if (!user) {
      return res.status(401).json({
        status: 'fail',
        message: 'Girdiğiniz e-posta veya şifre hatalı. Lütfen kontrol ediniz.',
        errorCode: 'EMAIL_NOT_REGISTERED'
      });
    }

    // Şifre kontrolü
    const isPasswordCorrect = await user.correctPassword(password, user.password);

    if (!isPasswordCorrect) {
      return res.status(401).json({
        status: 'fail',
        message: 'Girdiğiniz e-posta veya şifre hatalı. Lütfen kontrol ediniz.'
      });
    }

    // 3) Her şey doğruysa token gönder
    createSendToken(user, 200, res);
  } catch (err) {
    console.error('Login error:', err.message);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// ÇIKIŞ YAPMA
exports.logout = async (req, res) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];

    if (token) {
      // Token'ı blacklist'e ekle
      await BlacklistedToken.create({ token });
    }

    res.status(200).json({
      status: 'success',
      message: 'Başarıyla çıkış yapıldı'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// TOKEN YENİLEME
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const deviceId = req.headers['x-device-id'];

    if (!refreshToken) {
      return res.status(400).json({
        status: 'fail',
        message: 'Refresh token gereklidir'
      });
    }

    // Refresh token doğrulama
    const decoded = await promisify(jwt.verify)(
      refreshToken,
      process.env.REFRESH_TOKEN_SECRET
    );

    // Kullanıcı bulma
    const user = await User.findById(decoded.id);
    if (!user) {
      return res.status(401).json({
        status: 'fail',
        message: 'Bu token sahibi kullanıcı mevcut değil'
      });
    }

    // Cihaz kontrolü (opsiyonel ama önerilir - cihaz kısıtlaması)
    if (deviceId && process.env.NODE_ENV === 'production') {
      // Kullanıcının bu cihaz ID'si ile kayıtlı ve bloke edilmemiş bir cihazı var mı?
      const registeredDevice = user.devices.find(
        d => d.deviceId === deviceId && !d.isBlocked
      );

      if (!registeredDevice) {
        logger.warn(`Tanınmayan cihazdan token yenileme denemesi: ${deviceId}, Kullanıcı: ${user.email}`);
        return res.status(401).json({
          status: 'fail',
          message: 'Yetkisiz cihaz'
        });
      }

      // Cihazın son görülme zamanını güncelle
      await User.updateOne(
        { _id: user._id, "devices.deviceId": deviceId },
        { $set: { "devices.$.lastSeen": new Date() } }
      );
    }

    // Yeni token oluştur
    const token = signToken(user._id);
    const newRefreshToken = signRefreshToken(user._id);

    // Son giriş zamanını güncelle
    user.lastLogin = Date.now();
    await user.save({ validateBeforeSave: false });

    res.status(200).json({
      status: 'success',
      token,
      refreshToken: newRefreshToken
    });
  } catch (err) {
    res.status(401).json({
      status: 'fail',
      message: 'Token yenilenirken hata oluştu: ' + err.message
    });
  }
};

// KORUMA MİDDLEWARE (RESTRİCT ROUTES)
exports.protect = async (req, res, next) => {
  try {
    // 1) Token var mı kontrol et
    let token;
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return res.status(401).json({
        status: 'fail',
        message: 'Bu sayfaya erişmek için giriş yapmalısınız'
      });
    }

    // 2) Token blacklist'te mi kontrol et
    const isBlacklisted = await BlacklistedToken.findOne({ token });
    if (isBlacklisted) {
      return res.status(401).json({
        status: 'fail',
        message: 'Bu token artık geçerli değil, lütfen tekrar giriş yapın'
      });
    }

    // 3) Token doğrulama
    const decoded = await promisify(jwt.verify)(token, process.env.JWT_SECRET);

    // 4) Kullanıcı hala var mı kontrol et
    const currentUser = await User.findById(decoded.id);
    if (!currentUser) {
      return res.status(401).json({
        status: 'fail',
        message: 'Bu token sahibi kullanıcı artık mevcut değil'
      });
    }

    // 5) Şifre değişti mi kontrol et
    if (currentUser.changedPasswordAfter(decoded.iat)) {
      return res.status(401).json({
        status: 'fail',
        message: 'Şifreniz yakın zamanda değiştirildi, lütfen tekrar giriş yapın'
      });
    }

    // YETKİ VER - SONRAKI MIDDLEWARE'E GEÇ
    req.user = currentUser;
    next();
  } catch (err) {
    res.status(401).json({
      status: 'fail',
      message: 'Doğrulama hatası: ' + err.message
    });
  }
};

// ŞİFRE SIFIRLAMA MAİLİ GÖNDERME
exports.forgotPassword = async (req, res) => {
  try {
    // 1) Email adresine göre kullanıcıyı bul
    const user = await User.findOne({ email: req.body.email });
    if (!user) {
      return res.status(404).json({
        status: 'fail',
        message: 'Bu email adresine sahip kullanıcı bulunamadı'
      });
    }

    // 2) Rastgele reset token oluştur
    const resetToken = user.createPasswordResetToken();
    await user.save({ validateBeforeSave: false });

    // 3) E-posta gönderme işlemi
    try {
      // API URL yerine frontend URL'yi kullan
      const resetURL = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

      // Debug için URL'yi logla
      console.log('Şifre sıfırlama URL:', resetURL);
      console.log('FRONTEND_URL değeri:', process.env.FRONTEND_URL);

      const message = `Şifrenizi mi unuttunuz? Yeni şifrenizi ayarlamak için bu bağlantıya tıklayın: ${resetURL}\nEğer şifrenizi unutmadıysanız, lütfen bu e-postayı dikkate almayın.`;

      const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e1e1e1; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Şifre Sıfırlama</h2>
        <p>Merhaba ${user.username},</p>
        <p>Şifrenizi mi unuttunuz? Endişelenmeyin, size yardımcı olabiliriz.</p>
        <p>Aşağıdaki butona tıklayarak yeni bir şifre belirleyebilirsiniz:</p>
        <div style="text-align: center; margin: 25px 0;">
          <a href="${resetURL}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">Şifremi Sıfırla</a>
        </div>
        <p>Bu link 10 dakika boyunca geçerlidir.</p>
        <p>Eğer şifre sıfırlama talebinde bulunmadıysanız, lütfen bu e-postayı dikkate almayın.</p>
        <hr style="border: none; border-top: 1px solid #e1e1e1; margin: 20px 0;">
        <p style="font-size: 12px; color: #777; text-align: center;">Sosyal Medya Uygulaması</p>
      </div>
      `;

      await sendEmail({
        email: user.email,
        subject: 'Şifre Sıfırlama İsteğiniz (10 dakika geçerli)',
        message,
        html: htmlContent
      });

      res.status(200).json({
        status: 'success',
        message: 'Şifre sıfırlama bağlantısı e-posta adresinize gönderildi!'
      });
    } catch (err) {
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      await user.save({ validateBeforeSave: false });

      return res.status(500).json({
        status: 'error',
        message: 'E-posta gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin!'
      });
    }
  } catch (err) {
    res.status(500).json({
      status: 'error',
      message: 'Bu işlem sırasında bir hata oluştu! Lütfen daha sonra tekrar deneyin.'
    });
  }
};

// ŞİFRE SIFIRLAMA
exports.resetPassword = async (req, res) => {
  try {
    // 1) Token'a göre kullanıcıyı bul
    const hashedToken = crypto
      .createHash('sha256')
      .update(req.params.token)
      .digest('hex');

    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: Date.now() }
    });

    // 2) Token hala geçerli ve kullanıcı mevcutsa, yeni şifreyi ayarla
    if (!user) {
      return res.status(400).json({
        status: 'fail',
        message: 'Token geçersiz veya süresi dolmuş'
      });
    }

    user.password = req.body.password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save({ validateBeforeSave: false }); // Doğrulamayı atla

    // 3) Şifre başarıyla değiştirildiğine dair e-posta gönder
    try {
      const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e1e1e1; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Şifreniz Değiştirildi</h2>
        <p>Merhaba ${user.username},</p>
        <p>Bu e-posta, şifrenizin başarıyla değiştirildiğini bildirmek için gönderilmiştir.</p>
        <p>Eğer bu değişikliği siz yapmadıysanız, lütfen hemen bizimle iletişime geçin.</p>
        <hr style="border: none; border-top: 1px solid #e1e1e1; margin: 20px 0;">
        <p style="font-size: 12px; color: #777; text-align: center;">Sosyal Medya Uygulaması</p>
      </div>
      `;

      await sendEmail({
        email: user.email,
        subject: 'Şifreniz Başarıyla Değiştirildi',
        message: 'Şifreniz başarıyla değiştirildi. Eğer bu değişikliği siz yapmadıysanız, lütfen hemen bizimle iletişime geçin.',
        html: htmlContent
      });
    } catch (err) {
      console.log('Şifre değişikliği e-postası gönderilemedi:', err);
    }

    // 4) Kullanıcıyı giriş yap
    createSendToken(user, 200, res);
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// ŞİFRE DEĞİŞTİRME (giriş yapmış kullanıcı için)
exports.updatePassword = async (req, res) => {
  try {
    // 1) Kullanıcıyı al
    const user = await User.findById(req.user.id).select('+password');

    // 2) Gönderilen şifre doğru mu kontrol et
    if (!(await user.correctPassword(req.body.currentPassword, user.password))) {
      return res.status(401).json({
        status: 'fail',
        message: 'Mevcut şifreniz yanlış'
      });
    }

    // 3) Şifreyi güncelle
    user.password = req.body.newPassword;
    await user.save(); // Bu, şifre değişikliği için pre-save hook'u çalıştıracak

    // 4) Şifre değişikliği e-postası gönder
    try {
      const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e1e1e1; border-radius: 5px;">
        <h2 style="color: #333; text-align: center;">Şifreniz Değiştirildi</h2>
        <p>Merhaba ${user.username},</p>
        <p>Bu e-posta, şifrenizin başarıyla değiştirildiğini bildirmek için gönderilmiştir.</p>
        <p>Eğer bu değişikliği siz yapmadıysanız, lütfen hemen bizimle iletişime geçin.</p>
        <hr style="border: none; border-top: 1px solid #e1e1e1; margin: 20px 0;">
        <p style="font-size: 12px; color: #777; text-align: center;">Sosyal Medya Uygulaması</p>
      </div>
      `;

      await sendEmail({
        email: user.email,
        subject: 'Şifreniz Başarıyla Değiştirildi',
        message: 'Şifreniz başarıyla değiştirildi. Eğer bu değişikliği siz yapmadıysanız, lütfen hemen bizimle iletişime geçin.',
        html: htmlContent
      });
    } catch (err) {
      console.log('Şifre değişikliği e-postası gönderilemedi:', err);
    }

    // 5) Kullanıcıyı tekrar giriş yap
    createSendToken(user, 200, res);
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// GOOGLE İLE GİRİŞ YAPMA / KAYIT OLMA
exports.googleAuth = async (req, res) => {
  try {
    const { idToken, userInfo } = req.body;

    if (!idToken) {
      return res.status(400).json({
        status: 'fail',
        message: 'Google ID token gereklidir'
      });
    }

    console.log('Google Auth isteği:', { idToken, userInfo });

    // Google'dan gelen tüm user bilgilerini kullanarak düzgün veri oluştur
    const googleUser = {
      email: userInfo?.email || req.body.email,
      fullName: userInfo?.name || `${userInfo?.givenName || ''} ${userInfo?.familyName || ''}`.trim(),
      profilePicture: userInfo?.photo || '/images/default-profile.jpg'
    };

    console.log('İşlenmiş Google kullanıcı bilgileri:', googleUser);

    // Kullanıcının email'i ile kullanıcıyı veritabanında ara
    let user = await User.findOne({ email: googleUser.email });

    if (user) {
      // Kullanıcı zaten var, giriş yap
      console.log('Mevcut kullanıcı bulundu:', user.email);

      // Varolan kullanıcının bilgilerini güncelle (opsiyonel)
      if (googleUser.fullName && !user.fullName) {
        user.fullName = googleUser.fullName;
        await user.save({ validateBeforeSave: false });
      }

      console.log('Mevcut kullanıcı bulundu, token gönderiliyor. User object:', user);
      // Yanıtı göndermeden önce user nesnesini kontrol et
      if (!user || !user.email) {
          console.error('[BACKEND ERROR] Mevcut kullanıcı için token gönderilirken user veya user.email eksik!', user);
          return res.status(500).json({ status: 'fail', message: 'Sunucu hatası: Kullanıcı bilgisi eksik.'});
      }
      return createSendToken(user, 200, res);
    } else {
      // Kullanıcı yoksa, kayıt sürecini başlat
      console.log('Yeni kullanıcı oluşturuluyor:', googleUser.email);

      // Google'dan gelen bilgilere dayanarak geçici bir token oluştur
      const userData = {
        fullName: googleUser.fullName || 'Google Kullanıcısı',
        email: googleUser.email,
        password: crypto.randomBytes(16).toString('hex'), // Rastgele şifre (kullanıcı bunu bilmeyecek)
        profilePicture: googleUser.profilePicture || '/images/default-profile.jpg',
        provider: 'google'
      };

      const tempToken = jwt.sign(userData, process.env.JWT_SECRET, {
        expiresIn: '15m'
      });

      const responsePayload = {
        status: 'success',
        tempToken,
        profilePicture: userData.profilePicture,
        isNewUser: true,
        data: {
          userInfo: userData
        },
        message: 'Kayıt işleminin ilk adımı tamamlandı, lütfen kullanıcı adı seçin'
      };

      console.log('Yeni kullanıcı için yanıt gönderiliyor. Payload:', responsePayload);
      // Yanıtı göndermeden önce userInfo nesnesini ve email'i kontrol et
      if (!responsePayload.data?.userInfo?.email) {
        console.error('[BACKEND ERROR] Yeni kullanıcı için yanıt gönderilirken userInfo veya userInfo.email eksik!', responsePayload.data?.userInfo);
         return res.status(500).json({ status: 'fail', message: 'Sunucu hatası: Yeni kullanıcı bilgisi eksik.'});
      }

      return res.status(200).json(responsePayload);
    }
  } catch (err) {
    console.error('Google Auth error:', err.message);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// APPLE İLE GİRİŞ YAPMA / KAYIT OLMA
exports.appleAuth = async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({
        status: 'fail',
        message: 'Apple ID token gereklidir'
      });
    }

    // Apple OAuth doğrulaması (tam implementasyon için passport veya apple-signin-auth kullanılabilir)
    // Bu örnekte bir mockup response kullanıyoruz
    // Gerçek implementasyon için Apple API'sını kullanarak idToken doğrulanmalıdır

    // Simüle edilmiş token doğrulama
    const appleUser = {
      email: req.body.email, // Gerçek implementasyonda bu değerler token'dan alınacak
      fullName: req.body.fullName,
      profilePicture: req.body.profilePicture || '/images/default-profile.jpg'
    };

    // Kullanıcının email'i ile kullanıcıyı veritabanında ara
    let user = await User.findOne({ email: appleUser.email });

    if (user) {
      // Kullanıcı zaten var, giriş yap
      return createSendToken(user, 200, res);
    } else {
      // Kullanıcı yoksa, kayıt sürecini başlat
      // Apple'dan gelen bilgilere dayanarak geçici bir token oluştur
      const userData = {
        fullName: appleUser.fullName || 'Apple Kullanıcısı',
        email: appleUser.email,
        password: crypto.randomBytes(16).toString('hex'), // Rastgele şifre (kullanıcı bunu bilmeyecek)
        profilePicture: appleUser.profilePicture || '/images/default-profile.jpg',
        provider: 'apple'
      };

      const tempToken = jwt.sign(userData, process.env.JWT_SECRET, {
        expiresIn: '15m'
      });

      return res.status(200).json({
        status: 'success',
        tempToken,
        profilePicture: userData.profilePicture,
        isNewUser: true,
        message: 'Kayıt işleminin ilk adımı tamamlandı, lütfen kullanıcı adı seçin'
      });
    }
  } catch (err) {
    console.error('Apple Auth error:', err.message);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};