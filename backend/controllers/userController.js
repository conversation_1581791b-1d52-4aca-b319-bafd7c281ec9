const db = require('../models');
const User = db.user;
const Post = db.post;
const Follow = db.follow;

// Kullanıcı profilini getir
exports.getProfile = async (req, res) => {
  try {
    // Parametredeki veya giriş yapmış kullanıcının ID'sini kullan
    const userId = req.params.userId || req.user.id;

    const user = await User.findById(userId)
      .select('-password -passwordResetToken -passwordResetExpires');

    if (!user) {
      return res.status(404).json({
        status: 'fail',
        message: 'Kullanıcı bulunamadı'
      });
    }

    // Takipçi ve takip edilen sayılarını al
    const followersCount = await Follow.countDocuments({
      following: userId,
      status: 'accepted',
      isActive: true
    });

    const followingCount = await Follow.countDocuments({
      follower: userId,
      status: 'accepted',
      isActive: true
    });

    // <PERSON><PERSON><PERSON><PERSON> sayı<PERSON>ını al
    const postsCount = await Post.countDocuments({
      user: userId,
      isActive: true
    });

    // Kullanıcı giriş yapmışsa takip durumunu kontrol et
    let isFollowing = false;
    let followStatus = null;

    if (req.user && req.user.id !== userId) {
      const followRecord = await Follow.findOne({
        follower: req.user.id,
        following: userId,
        isActive: true
      });

      if (followRecord) {
        isFollowing = followRecord.status === 'accepted';
        followStatus = followRecord.status;
      }
    }

    res.status(200).json({
      status: 'success',
      data: {
        user,
        stats: {
          followersCount,
          followingCount,
          postsCount
        },
        relationship: {
          isFollowing,
          followStatus
        }
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcı profilini güncelle
exports.updateProfile = async (req, res) => {
  try {
    // Güncelleme için izin verilen alanlar
    const allowedFields = ['username', 'bio', 'isPrivate', 'fullName'];

    // Güncelleme verilerini oluştur
    const updateData = {};
    for (const field of allowedFields) {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    }

    // Email alanı varsa ve geçerli değilse hata döndür
    if (req.body.email) {
      const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
      if (!emailRegex.test(req.body.email)) {
        return res.status(400).json({
          status: 'fail',
          message: 'Geçersiz email formatı. Lütfen geçerli bir email adresi giriniz.'
        });
      }
      updateData.email = req.body.email;
    }

    // Kullanıcı adı varsa, benzersiz olup olmadığını kontrol et
    if (req.body.username) {
      const existingUser = await User.findOne({
        username: req.body.username,
        _id: { $ne: req.params.userId || req.user.id } // Kendisi hariç
      });

      if (existingUser) {
        return res.status(400).json({
          status: 'fail',
          message: 'Bu kullanıcı adı zaten kullanılıyor. Lütfen başka bir kullanıcı adı seçiniz.'
        });
      }
    }

    // Profil resmi S3'e yüklendiyse, URL'i kullan
    if (req.file && req.file.location) {
      updateData.profilePicture = req.file.location;
    }

    // Güncellenecek kullanıcı ID'sini belirle
    let userId = req.user.id;

    // Eğer URL'de bir userId parametresi varsa ve kullanıcı kendisi veya admin ise o ID'yi kullan
    if (req.params.userId) {
      // Admin mi kontrol et
      const isAdmin = req.user.role === 'admin';

      // Kullanıcının kendisi mi kontrol et
      const isSelf = req.params.userId === req.user.id || req.params.userId === req.user._id.toString();

      if (!isAdmin && !isSelf) {
        return res.status(403).json({
          status: 'fail',
          message: 'Başka bir kullanıcının profilini güncelleme yetkiniz yok'
        });
      }

      userId = req.params.userId;
    }

    // Kullanıcıyı güncelle
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -passwordResetToken -passwordResetExpires');

    if (!updatedUser) {
      return res.status(404).json({
        status: 'fail',
        message: 'Kullanıcı bulunamadı'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        user: updatedUser
      }
    });
  } catch (err) {
    // MongoDB hata kodlarını kontrol et
    if (err.code === 11000) {
      // Duplicate key error
      const field = Object.keys(err.keyPattern)[0];
      return res.status(400).json({
        status: 'fail',
        message: `Bu ${field} zaten kullanılıyor. Lütfen başka bir ${field} seçiniz.`
      });
    }

    console.error('Profil güncelleme hatası:', err);

    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcı hesabını sil (soft delete)
exports.deleteAccount = async (req, res) => {
  try {
    // Kullanıcıyı pasif yap
    await User.findByIdAndUpdate(req.user.id, { active: false });

    // Kullanıcının gönderilerini pasif yap
    await Post.updateMany(
      { user: req.user.id },
      { isActive: false }
    );

    // Kullanıcının takip ilişkilerini pasif yap
    await Follow.updateMany(
      { $or: [{ follower: req.user.id }, { following: req.user.id }] },
      { isActive: false }
    );

    res.status(200).json({
      status: 'success',
      message: 'Hesabınız başarıyla silinmiştir'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcı ara
exports.searchUsers = async (req, res) => {
  try {
    const { query } = req.query;

    if (!query) {
      return res.status(400).json({
        status: 'fail',
        message: 'Arama sorgusu gereklidir'
      });
    }

    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Kullanıcıları ara
    const users = await User.find({
      active: true,
      $or: [
        { username: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } },
        { fullName: { $regex: query, $options: 'i' } }
      ]
    })
      .select('username email profilePicture bio fullName')
      .skip(skip)
      .limit(limit);

    // Toplam sonuç sayısı
    const total = await User.countDocuments({
      active: true,
      $or: [
        { username: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } },
        { fullName: { $regex: query, $options: 'i' } }
      ]
    });

    res.status(200).json({
      status: 'success',
      results: users.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        users
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcının gönderilerini getir
exports.getUserPosts = async (req, res) => {
  try {
    // Kullanıcı kimliğini al (userId veya id parametresi olabilir)
    const userId = req.params.userId || req.params.id || req.user.id;

    console.log('Gönderileri getirilecek kullanıcı ID:', userId);

    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10000;
    const skip = (page - 1) * limit;

    // Kullanıcının gönderilerini getir
    const posts = await Post.find({ user: userId, isActive: true })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'username profilePicture')
      .populate({
        path: 'comments',
        match: { isActive: true }, // Sadece aktif yorumları getir
        options: { limit: 3, sort: { createdAt: -1 } },
        populate: { path: 'user', select: 'username profilePicture' }
      });

    console.log(`${userId} için bulunan gönderi sayısı:`, posts.length);

    // Toplam gönderi sayısı
    const total = await Post.countDocuments({ user: userId, isActive: true });

    res.status(200).json({
      status: 'success',
      results: posts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts
      }
    });
  } catch (err) {
    console.error('Kullanıcı gönderileri alınırken hata:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kaydedilen gönderileri getir
exports.getSavedPosts = async (req, res) => {
  try {
    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Kullanıcıyı bilgilerle getir
    const user = await User.findById(req.user.id)
      .populate({
        path: 'savedPosts',
        match: { isActive: true },
        options: {
          sort: { createdAt: -1 },
          skip,
          limit
        },
        populate: [
          {
            path: 'user',
            select: 'username profilePicture'
          },
          {
            path: 'comments',
            match: { isActive: true }, // Sadece aktif yorumları getir
            options: { limit: 3, sort: { createdAt: -1 } },
            populate: { path: 'user', select: 'username profilePicture' }
          }
        ]
      });

    // Aktif olan kaydedilmiş gönderileri filtrele
    const savedPosts = user.savedPosts.filter(post => post.isActive);

    // Toplam gönderi sayısı
    const total = savedPosts.length;

    res.status(200).json({
      status: 'success',
      results: savedPosts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts: savedPosts
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Admin: Tüm kullanıcıları getir
exports.getAllUsers = async (req, res) => {
  try {
    // Sadece admin kullanıcılar erişebilir
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu işleme yetkili değilsiniz'
      });
    }

    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Filtreleme için parametreler
    const filter = {};
    if (req.query.active === 'true') filter.active = true;
    if (req.query.active === 'false') filter.active = false;
    if (req.query.role) filter.role = req.query.role;

    // Kullanıcıları getir
    const users = await User.find(filter)
      .select('-password -passwordResetToken -passwordResetExpires')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Toplam kullanıcı sayısı
    const total = await User.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: users.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        users
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Admin: Kullanıcı rolünü güncelle
exports.updateUserRole = async (req, res) => {
  try {
    // Sadece admin kullanıcılar erişebilir
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu işleme yetkili değilsiniz'
      });
    }

    const { role } = req.body;

    if (!role || !['user', 'admin'].includes(role)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Geçerli bir rol belirtmelisiniz'
      });
    }

    // Kullanıcıyı bul ve güncelle
    const updatedUser = await User.findByIdAndUpdate(
      req.params.userId,
      { role },
      { new: true, runValidators: true }
    ).select('-password -passwordResetToken -passwordResetExpires');

    if (!updatedUser) {
      return res.status(404).json({
        status: 'fail',
        message: 'Kullanıcı bulunamadı'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        user: updatedUser
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};