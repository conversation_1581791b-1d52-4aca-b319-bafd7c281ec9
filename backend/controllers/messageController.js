const db = require('../models');
const Conversation = db.conversation;
const GroupConversation = db.groupConversation;
const User = db.user;
const Message = db.message;

// Yeni mesaj gönder
exports.sendMessage = async (req, res) => {
  try {
    const { recipientId, text, groupId } = req.body;

    // Grup veya tekil mesaj olduğunu kontrol et
    if (groupId) {
      return await sendGroupMessage(req, res);
    }

    // Alıcı ve mesaj metni kontrolü
    if (!recipientId) {
      return res.status(400).json({
        status: 'fail',
        message: '<PERSON>ıcı belirtilmelidir'
      });
    }

    if (!text || text.trim() === '') {
      return res.status(400).json({
        status: 'fail',
        message: '<PERSON>j metni boş olamaz'
      });
    }

    // Alıcı kullanıcıyı kontrol et
    const recipient = await User.findById(recipientId);
    if (!recipient) {
      return res.status(404).json({
        status: 'fail',
        message: 'Alıcı kullanıcı bulunamadı'
      });
    }

    // Kendisine mesaj göndermeye çalışıyorsa
    if (req.user.id === recipientId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Kendinize mesaj gönderemezsiniz'
      });
    }

    // Sohbet var mı kontrol et
    let conversation = await Conversation.findOne({
      participants: { $all: [req.user.id, recipientId] },
      isActive: true
    });

    // Sohbet yoksa yeni oluştur
    if (!conversation) {
      conversation = new Conversation({
        participants: [req.user.id, recipientId],
        messages: []
      });
      await conversation.save();
    }

    // Yeni mesaj oluştur
    const newMessage = new Message({
      sender: req.user.id,
      conversation: conversation._id,
      conversationType: 'Conversation',
      text
    });

    // Mesajı kaydet
    await newMessage.save();

    // Sohbete mesajı ekle ve son mesaj bilgisini güncelle
    conversation.messages.push(newMessage._id);
    conversation.lastMessage = newMessage._id;
    conversation.lastMessageDate = newMessage.createdAt;
    await conversation.save();

    // Mesajı popüle ederek döndür
    const populatedMessage = await Message.findById(newMessage._id)
      .populate('sender', 'username profilePicture');

    res.status(200).json({
      status: 'success',
      data: {
        message: populatedMessage
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Grup mesajı gönder (yardımcı fonksiyon)
const sendGroupMessage = async (req, res) => {
  try {
    const { groupId, text } = req.body;

    if (!groupId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup ID belirtilmelidir'
      });
    }

    if (!text || text.trim() === '') {
      return res.status(400).json({
        status: 'fail',
        message: 'Mesaj metni boş olamaz'
      });
    }

    // Grubu kontrol et
    const group = await GroupConversation.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Kullanıcı grubun bir üyesi mi?
    if (!group.participants.includes(req.user.id) && !group.admins.includes(req.user.id) && group.creator.toString() !== req.user.id) {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu gruba mesaj gönderme yetkiniz yok'
      });
    }

    // Yeni mesaj oluştur
    const newMessage = new Message({
      sender: req.user.id,
      conversation: groupId,
      conversationType: 'GroupConversation',
      text
    });

    // Mesajı kaydet
    await newMessage.save();

    // Gruba mesajı ekle ve son mesaj bilgisini güncelle
    group.messages.push(newMessage._id);
    group.lastMessage = newMessage._id;
    group.lastMessageDate = newMessage.createdAt;
    await group.save();

    // Mesajı popüle ederek döndür
    const populatedMessage = await Message.findById(newMessage._id)
      .populate('sender', 'username profilePicture');

    return res.status(200).json({
      status: 'success',
      data: {
        message: populatedMessage
      }
    });
  } catch (err) {
    return res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Sohbetleri getir
exports.getConversations = async (req, res) => {
  try {
    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Kullanıcının sohbetlerini getir
    const conversations = await Conversation.find({
      participants: req.user.id,
      isActive: true
    })
      .sort({ lastMessageDate: -1 })
      .populate('participants', 'username profilePicture')
      .populate('lastMessage', 'text createdAt')
      .skip(skip)
      .limit(limit);

    // Her sohbet için karşı tarafı belirle
    const conversationsWithOtherParticipant = conversations.map(conversation => {
      const otherParticipant = conversation.participants.find(
        participant => participant._id.toString() !== req.user.id
      );

      return {
        _id: conversation._id,
        otherParticipant,
        lastMessage: conversation.lastMessage,
        updatedAt: conversation.updatedAt,
        createdAt: conversation.createdAt
      };
    });

    // Kullanıcının grup sohbetlerini getir
    const groupConversations = await GroupConversation.find({
      $or: [
        { participants: req.user.id },
        { admins: req.user.id },
        { creator: req.user.id }
      ],
      isActive: true
    })
      .sort({ lastMessageDate: -1 })
      .populate('creator', 'username profilePicture')
      .populate('lastMessage')
      .populate('participants', 'username profilePicture')
      .skip(skip)
      .limit(limit);

    // Tüm sohbetleri birleştir ve tarihe göre sırala
    const allConversations = [
      ...conversationsWithOtherParticipant.map(conv => ({
        ...conv,
        type: 'direct',
        lastMessageDate: conv.updatedAt
      })),
      ...groupConversations.map(group => ({
        _id: group._id,
        name: group.name,
        groupImage: group.groupImage,
        participantCount: group.participants.length,
        lastMessage: group.lastMessage,
        lastMessageDate: group.lastMessageDate,
        updatedAt: group.updatedAt,
        createdAt: group.createdAt,
        type: 'group'
      }))
    ].sort((a, b) => new Date(b.lastMessageDate) - new Date(a.lastMessageDate));

    // Toplam sohbet sayısı
    const totalDirectConversations = await Conversation.countDocuments({
      participants: req.user.id,
      isActive: true
    });

    const totalGroupConversations = await GroupConversation.countDocuments({
      $or: [
        { participants: req.user.id },
        { admins: req.user.id },
        { creator: req.user.id }
      ],
      isActive: true
    });

    const total = totalDirectConversations + totalGroupConversations;

    res.status(200).json({
      status: 'success',
      results: allConversations.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        conversations: allConversations
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Belirli bir sohbetin mesajlarını veya grup mesajlarını getir
exports.getMessages = async (req, res) => {
  try {
    const { conversationId, groupId } = req.params;
    const userId = req.user.id;

    let conversation;
    let messages;

    if (conversationId) {
      // 1. Sohbeti ve katılımcılarını getir
      conversation = await Conversation.findById(conversationId)
        .populate('participants', 'username profilePicture'); // Katılımcıları popüle et

      if (!conversation) {
        return res.status(404).json({ status: 'fail', message: 'Sohbet bulunamadı' });
      }

      // Kullanıcının sohbete erişimi var mı kontrol et
      const isParticipant = conversation.participants.some(p => p._id.toString() === userId);
      if (!isParticipant) {
        return res.status(403).json({ status: 'fail', message: 'Bu sohbete erişim izniniz yok' });
      }

      // 2. Sohbetin mesajlarını getir
      messages = await Message.find({
        conversation: conversationId,
        conversationType: 'Conversation'
      })
        .populate('sender', 'username profilePicture')
        .sort({ createdAt: 1 }); // Mesajları eskiden yeniye sırala

    } else if (groupId) {
       // Grup sohbeti için mevcut mantık (gerekirse burası da güncellenebilir)
       // Şimdilik grup mesajlarının ayrı bir endpoint'ten geldiğini varsayıyoruz
       // VEYA grup bilgilerini de burada getirebiliriz
       conversation = await GroupConversation.findById(groupId)
           .populate('participants', 'username profilePicture')
           .populate('admins', 'username profilePicture')
           .populate('creator', 'username profilePicture'); // Grubu ve üyelerini getir

       if (!conversation) {
           return res.status(404).json({ status: 'fail', message: 'Grup bulunamadı' });
       }

       // Kullanıcı grup üyesi mi?
       const isMember = conversation.participants.some(p => p._id.toString() === userId) ||
                        conversation.admins.some(a => a._id.toString() === userId) ||
                        conversation.creator._id.toString() === userId;

       if (!isMember) {
           return res.status(403).json({ status: 'fail', message: 'Bu gruba erişim izniniz yok' });
       }

       messages = await Message.find({
           conversation: groupId,
           conversationType: 'GroupConversation'
       })
        .populate('sender', 'username profilePicture')
        .sort({ createdAt: 1 });

    } else {
      return res.status(400).json({ status: 'fail', message: 'Sohbet veya Grup ID belirtilmelidir' });
    }

    res.status(200).json({
      status: 'success',
      data: {
        // Ön uçta beklenen yapıya uygun yanıt döndür
        // Ön uç 'conversations' anahtarını bekliyorsa:
        conversations: {
           ...conversation.toObject(), // Sohbet detaylarını ekle
           messages: messages // Mesajları ekle
        }
        // Alternatif olarak:
        // conversation: conversation,
        // messages: messages
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Yeni sohbet başlat (ilk mesaj gönderilmeden önce)
exports.createConversation = async (req, res) => {
  try {
    const { recipientId } = req.body;

    if (!recipientId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Alıcı kullanıcı belirtilmelidir'
      });
    }

    // Alıcıyı bul
    const recipient = await User.findById(recipientId);
    if (!recipient) {
      return res.status(404).json({
        status: 'fail',
        message: 'Alıcı kullanıcı bulunamadı'
      });
    }

    // Kendisi ile sohbet etmeye çalışıyorsa
    if (req.user.id === recipientId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Kendinizle sohbet başlatamazsınız'
      });
    }

    // Katılımcıları tanımla
    const participants = [req.user.id, recipientId].sort();

    // Mevcut sohbeti kontrol et
    const existingConversation = await Conversation.findOne({
      participants: { $all: participants },
      isActive: true
    });

    if (existingConversation) {
      return res.status(200).json({
        status: 'success',
        message: 'Sohbet zaten mevcut',
        data: {
          conversation: existingConversation
        }
      });
    }

    // Yeni sohbet oluştur
    const newConversation = new Conversation({
      participants,
      messages: []
    });

    await newConversation.save();

    // Sohbeti kullanıcı bilgileriyle doldur
    const populatedConversation = await Conversation.findById(newConversation._id)
      .populate('participants', 'username profilePicture');

    res.status(201).json({
      status: 'success',
      data: {
        conversation: populatedConversation
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Yeni grup sohbeti oluştur
exports.createGroupConversation = async (req, res) => {
  try {
    const { name, description, participants } = req.body;

    if (!name) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup adı belirtilmelidir'
      });
    }

    if (!participants || !Array.isArray(participants) || participants.length === 0) {
      return res.status(400).json({
        status: 'fail',
        message: 'En az bir katılımcı belirtilmelidir'
      });
    }

    // Kendisi dışındaki katılımcıları doğrula
    const uniqueParticipants = [...new Set(participants)];
    const users = await User.find({ _id: { $in: uniqueParticipants } });

    if (users.length !== uniqueParticipants.length) {
      return res.status(400).json({
        status: 'fail',
        message: 'Geçersiz katılımcı ID(leri)'
      });
    }

    // Grup oluştur
    const newGroup = new GroupConversation({
      name,
      description: description || '',
      creator: req.user.id,
      admins: [req.user.id],
      participants: [...uniqueParticipants, req.user.id]
    });

    await newGroup.save();

    // Grubu kullanıcı bilgileriyle doldur
    const populatedGroup = await GroupConversation.findById(newGroup._id)
      .populate('creator', 'username profilePicture')
      .populate('admins', 'username profilePicture')
      .populate('participants', 'username profilePicture');

    res.status(201).json({
      status: 'success',
      data: {
        group: populatedGroup
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Gruba kullanıcı ekle
exports.addUserToGroup = async (req, res) => {
  try {
    const { groupId, userId } = req.body;

    if (!groupId || !userId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup ID ve kullanıcı ID belirtilmelidir'
      });
    }

    // Grubu bul
    const group = await GroupConversation.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Kullanıcının yetkisi var mı?
    const isAdmin = group.admins.includes(req.user.id);
    const isCreator = group.creator.toString() === req.user.id;

    if (!isAdmin && !isCreator) {
      return res.status(403).json({
        status: 'fail',
        message: 'Gruba kullanıcı ekleme yetkiniz yok'
      });
    }

    // Eklenecek kullanıcıyı bul
    const userToAdd = await User.findById(userId);
    if (!userToAdd) {
      return res.status(404).json({
        status: 'fail',
        message: 'Eklenecek kullanıcı bulunamadı'
      });
    }

    // Kullanıcı zaten grupta mı?
    if (group.participants.includes(userId)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Kullanıcı zaten grubun bir üyesi'
      });
    }

    // Kullanıcıyı gruba ekle
    group.participants.push(userId);
    await group.save();

    // Grubu kullanıcı bilgileriyle doldur
    const updatedGroup = await GroupConversation.findById(groupId)
      .populate('creator', 'username profilePicture')
      .populate('admins', 'username profilePicture')
      .populate('participants', 'username profilePicture');

    res.status(200).json({
      status: 'success',
      message: 'Kullanıcı gruba eklendi',
      data: {
        group: updatedGroup
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcıyı gruptan çıkar
exports.removeUserFromGroup = async (req, res) => {
  try {
    const { groupId, userId } = req.params;

    if (!groupId || !userId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup ID ve kullanıcı ID belirtilmelidir'
      });
    }

    // Grubu bul
    const group = await GroupConversation.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Kullanıcının yetkisi var mı?
    const isAdmin = group.admins.includes(req.user.id);
    const isCreator = group.creator.toString() === req.user.id;
    const isSelfRemoval = userId === req.user.id;

    // Kullanıcının kendisi çıkmak istiyorsa izin ver veya admin/oluşturucu ise izin ver
    if (!isAdmin && !isCreator && !isSelfRemoval) {
      return res.status(403).json({
        status: 'fail',
        message: 'Gruptan kullanıcı çıkarma yetkiniz yok'
      });
    }

    // Grup oluşturucusu çıkarılamaz (kendisi çıkmak isterse grup silinebilir)
    if (userId === group.creator.toString() && !isSelfRemoval) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup oluşturucusu gruptan çıkarılamaz'
      });
    }

    // Kullanıcıyı gruptan çıkar
    group.participants = group.participants.filter(
      participant => participant.toString() !== userId
    );

    // Eğer kullanıcı aynı zamanda bir admin ise, admin listesinden de çıkar
    if (group.admins.includes(userId)) {
      group.admins = group.admins.filter(
        admin => admin.toString() !== userId
      );
    }

    // Grupta kullanıcı kalmadıysa grubu sil (soft delete)
    if (group.participants.length === 0) {
      group.isActive = false;
      await group.save();

      return res.status(200).json({
        status: 'success',
        message: 'Son kullanıcı gruptan çıktı, grup silindi'
      });
    }

    // Oluşturucu gruptan çıkıyorsa yeni bir oluşturucu ata
    if (isSelfRemoval && isCreator) {
      // Önce adminlerden birini seç, admin yoksa katılımcılardan birini seç
      const newCreator = group.admins.length > 0
        ? group.admins[0]
        : group.participants[0];

      group.creator = newCreator;
    }

    await group.save();

    // Grubu kullanıcı bilgileriyle doldur
    const updatedGroup = await GroupConversation.findById(groupId)
      .populate('creator', 'username profilePicture')
      .populate('admins', 'username profilePicture')
      .populate('participants', 'username profilePicture');

    res.status(200).json({
      status: 'success',
      message: 'Kullanıcı gruptan çıkarıldı',
      data: {
        group: updatedGroup
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Kullanıcıyı grup admini yap
exports.makeGroupAdmin = async (req, res) => {
  try {
    const { groupId, userId } = req.body;

    if (!groupId || !userId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup ID ve kullanıcı ID belirtilmelidir'
      });
    }

    // Grubu bul
    const group = await GroupConversation.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Sadece grup oluşturucusu admin atayabilir
    if (group.creator.toString() !== req.user.id) {
      return res.status(403).json({
        status: 'fail',
        message: 'Sadece grup oluşturucusu admin atayabilir'
      });
    }

    // Kullanıcı grupta mı?
    if (!group.participants.includes(userId)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Kullanıcı grupta değil'
      });
    }

    // Kullanıcı zaten admin mi?
    if (group.admins.includes(userId)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Kullanıcı zaten grup admini'
      });
    }

    // Kullanıcıyı admin yap
    group.admins.push(userId);
    await group.save();

    // Grubu kullanıcı bilgileriyle doldur
    const updatedGroup = await GroupConversation.findById(groupId)
      .populate('creator', 'username profilePicture')
      .populate('admins', 'username profilePicture')
      .populate('participants', 'username profilePicture');

    res.status(200).json({
      status: 'success',
      message: 'Kullanıcı grup admini yapıldı',
      data: {
        group: updatedGroup
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Grup bilgilerini güncelle
exports.updateGroupConversation = async (req, res) => {
  try {
    const { groupId } = req.params;
    const { name, description } = req.body;

    if (!groupId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Grup ID belirtilmelidir'
      });
    }

    // Grubu bul
    const group = await GroupConversation.findById(groupId);
    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Kullanıcının yetkisi var mı?
    const isAdmin = group.admins.includes(req.user.id);
    const isCreator = group.creator.toString() === req.user.id;

    if (!isAdmin && !isCreator) {
      return res.status(403).json({
        status: 'fail',
        message: 'Grup bilgilerini güncelleme yetkiniz yok'
      });
    }

    // Güncellenecek alanları kontrol et
    const updateData = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;

    // Güncelle
    const updatedGroup = await GroupConversation.findByIdAndUpdate(
      groupId,
      updateData,
      { new: true }
    )
      .populate('creator', 'username profilePicture')
      .populate('admins', 'username profilePicture')
      .populate('participants', 'username profilePicture');

    res.status(200).json({
      status: 'success',
      message: 'Grup bilgileri güncellendi',
      data: {
        group: updatedGroup
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Sohbeti sil (soft delete)
exports.deleteConversation = async (req, res) => {
  try {
    // Sohbeti bul
    const conversation = await Conversation.findById(req.params.conversationId);

    if (!conversation) {
      return res.status(404).json({
        status: 'fail',
        message: 'Sohbet bulunamadı'
      });
    }

    // Kullanıcı bu sohbetin bir parçası mı?
    if (!conversation.participants.includes(req.user.id)) {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu sohbete erişim yetkiniz yok'
      });
    }

    // Sohbeti pasif yap (soft delete)
    conversation.isActive = false;
    await conversation.save();

    res.status(200).json({
      status: 'success',
      message: 'Sohbet başarıyla silindi'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Grubu sil (soft delete)
exports.deleteGroupConversation = async (req, res) => {
  try {
    const { groupId } = req.params;

    // Grubu bul
    const group = await GroupConversation.findById(groupId);

    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Sadece oluşturucu grubu silebilir
    if (group.creator.toString() !== req.user.id) {
      return res.status(403).json({
        status: 'fail',
        message: 'Sadece grup oluşturucusu grubu silebilir'
      });
    }

    // Grubu pasif yap (soft delete)
    group.isActive = false;
    await group.save();

    res.status(200).json({
      status: 'success',
      message: 'Grup başarıyla silindi'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Mesajı sil
exports.deleteMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user.id;

    // Mesajı bul
    const message = await Message.findById(messageId);

    if (!message) {
      return res.status(404).json({
        status: 'fail',
        message: 'Mesaj bulunamadı'
      });
    }

    // Mesajı sadece gönderen silebilir
    if (message.sender.toString() !== userId) {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu mesajı silme yetkiniz yok'
      });
    }

    // Mesajın ait olduğu sohbeti bul
    let conversation;
    if (message.conversationType === 'Conversation') {
      conversation = await Conversation.findById(message.conversation);
    } else {
      conversation = await GroupConversation.findById(message.conversation);
    }

    if (!conversation) {
      return res.status(404).json({
        status: 'fail',
        message: 'Mesajın ait olduğu sohbet bulunamadı'
      });
    }

    // Mesajı sil
    await Message.findByIdAndDelete(messageId);

    // Sohbetin mesajlar listesinden kaldır
    conversation.messages = conversation.messages.filter(
      msgId => msgId.toString() !== messageId
    );

    // Eğer silinen mesaj son mesaj ise, son mesajı güncelle
    if (conversation.lastMessage && conversation.lastMessage.toString() === messageId) {
      // Son mesajı bul (en son gönderilen mesaj)
      const lastMessage = await Message.findOne({
        conversation: conversation._id,
        conversationType: message.conversationType
      }).sort({ createdAt: -1 });

      conversation.lastMessage = lastMessage ? lastMessage._id : null;
      conversation.lastMessageDate = lastMessage ? lastMessage.createdAt : conversation.updatedAt;
    }

    await conversation.save();

    res.status(200).json({
      status: 'success',
      message: 'Mesaj başarıyla silindi'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Grup bilgilerini getir
exports.getGroupConversation = async (req, res) => {
  try {
    const { groupId } = req.params;
    const userId = req.user.id;

    // Grubu bul ve ilgili alanları popüle et
    const group = await GroupConversation.findById(groupId)
      .populate('creator', 'username profilePicture')
      .populate('admins', 'username profilePicture')
      .populate('participants', 'username profilePicture')
      .populate('lastMessage');

    if (!group) {
      return res.status(404).json({
        status: 'fail',
        message: 'Grup bulunamadı'
      });
    }

    // Kullanıcının grup üyesi olup olmadığını kontrol et
    const isMember = group.participants.some(p => p._id.toString() === userId) ||
                     group.admins.some(a => a._id.toString() === userId) ||
                     group.creator._id.toString() === userId;

    if (!isMember) {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu gruba erişim izniniz yok'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        group
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};