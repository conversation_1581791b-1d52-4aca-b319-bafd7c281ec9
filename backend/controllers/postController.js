const db = require('../models');
const Post = db.post;
const User = db.user;
const Comment = db.comment;
const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Yeni gönderi oluştur
exports.createPost = async (req, res) => {
  try {
    console.log("createPost başladı");
    console.log("Request body:", req.body);
    console.log("Yüklenen dosya:", req.file);

    if (!req.body) {
      return res.status(400).json({ success: false, error: "Request body eksik" });
    }

    // Dosya yüklendi mi kontrol et
    if (!req.file) {
      return res.status(400).json({ success: false, error: "Lütfen bir resim yükleyin" });
    }

    // Clothing links parse etme
    let clothingLinks = [];
    if (req.body.clothingLinks) {
      try {
        clothingLinks = JSON.parse(req.body.clothingLinks);
        console.log("Kıyafet bağlantıları:", clothingLinks);

        // Her bir kıyafet bağlantısında yakaType ve color özelliklerinin olup olmadığını kontrol et
        clothingLinks = clothingLinks.map(link => {
          // Eğer yakaType ve color özellikleri yoksa, bunları boş string olarak belirleyelim
          if (!link.hasOwnProperty('yakaType')) link.yakaType = '';
          if (!link.hasOwnProperty('color')) link.color = '';
          return link;
        });
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: "Kıyafet bağlantıları doğru formatta değil",
          details: error.message
        });
      }
    }

    // Tags parse etme
    let tags = [];
    if (req.body.tags) {
      try {
        // Eğer tags bir JSON string ise parse et
        tags = JSON.parse(req.body.tags);
        console.log("Etiketler (JSON):", tags);
      } catch (error) {
        console.log("JSON parse hatası:", error.message);

        // JSON parse edilemiyorsa, virgülle ayrılmış string olarak deneyelim
        try {
          if (typeof req.body.tags === 'string') {
            tags = req.body.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '');
            console.log("Etiketler (String):", tags);
          }
        } catch (stringError) {
          console.error("String işleme hatası:", stringError.message);
        }

        // Alternatif olarak tags[0], tags[1] gibi anahtar biçimindeki etiketleri arayalım
        const tagKeys = Object.keys(req.body).filter(key => key.startsWith('tags[') && key.endsWith(']'));
        if (tagKeys.length > 0 && tags.length === 0) {
          tags = tagKeys.map(key => req.body[key]);
          console.log("Etiketler (Array):", tags);
        }

        // Hala etiket bulunamadıysa hata döndür
        if (tags.length === 0) {
          return res.status(400).json({
            success: false,
            error: "Etiketler doğru formatta değil",
            details: error.message
          });
        }
      }
    }

    // S3 ile yüklenen dosya için URL alın
    let imageUrl = '';
    if (req.file) {
      // AWS S3 için dosya URL'si
      if (req.file.location) {
        imageUrl = req.file.location; // S3 için dosya URL'si
      } else if (req.file.filename) {
        imageUrl = req.file.filename; // Yerel depolama için dosya adı
      } else {
        return res.status(400).json({ success: false, error: "Yüklenen resim dosyasında bir sorun var" });
      }
    }

    // Yeni gönderi oluştur
    const post = new Post({
      user: req.user._id,
      image: imageUrl,
      description: req.body.description,
      tags: tags,
      clothingLinks: clothingLinks,
      location: req.body.location,
      filter: req.body.filter || 'No Filter'
    });

    // Gönderiyi kaydet
    const savedPost = await post.save();
    console.log("Gönderi kaydedildi:", savedPost._id);

    // Kullanıcının gönderilerine ekle
    await User.findByIdAndUpdate(
      req.user._id,
      { $push: { posts: savedPost._id } }
    );
    console.log("Kullanıcının gönderileri güncellendi");

    res.status(201).json({
      success: true,
      data: savedPost
    });

  } catch (error) {
    console.error("Gönderi oluşturma hatası:", error);
    res.status(500).json({
      success: false,
      error: "Gönderi oluşturulamadı",
      details: error.message
    });
  }
};

// Tüm gönderileri getir (admin için)
exports.getAllPosts = async (req, res) => {
  try {
    // Test ortamında daha basit sorgular kullanacağız
    // Sayfalama işlemlerini kaldıralım
    const posts = await Post.find()
      .populate('user', 'username profilePicture');

    res.status(200).json({
      status: 'success',
      results: posts.length,
      data: {
        posts
      }
    });
  } catch (err) {
    console.error('getAllPosts error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Ana sayfa için takip edilen kullanıcıların gönderilerini getir
exports.getFeedPosts = async (req, res) => {
  try {
    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10000;
    const skip = (page - 1) * limit;

    // Kullanıcının takip ettiği kullanıcıları bul
    const user = await User.findById(req.user.id);
    const following = user.following;

    // Takip edilen kullanıcıların ve kullanıcının kendi gönderilerini getir
    const posts = await Post.find({
      user: { $in: [...following, req.user.id] },
      isActive: true
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'username profilePicture')
      .populate({
        path: 'comments',
        match: { isActive: true }, // Sadece aktif yorumları getir
        options: { limit: 3, sort: { createdAt: -1 } },
        populate: { path: 'user', select: 'username profilePicture' }
      });

    // Toplam gönderi sayısı
    const total = await Post.countDocuments({
      user: { $in: [...following, req.user.id] },
      isActive: true
    });

    res.status(200).json({
      status: 'success',
      results: posts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Keşfet bölümü için popüler gönderileri getir
exports.getDiscoverPosts = async (req, res) => {
  try {
    // Sayfalama için parametreler
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Son 7 gün içindeki gönderileri getir
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    // Popüler gönderileri getir (beğeni ve yorum sayısına göre)
    const posts = await Post.aggregate([
      {
        $match: {
          createdAt: { $gte: oneWeekAgo },
          isActive: true
        }
      },
      {
        $addFields: {
          likesCount: { $size: "$likes" },
          commentsCount: { $size: "$comments" },
          interactionScore: {
            $add: [
              { $size: "$likes" },
              { $multiply: [{ $size: "$comments" }, 3] } // Yorumlar daha değerli
            ]
          }
        }
      },
      {
        $sort: { interactionScore: -1 }
      },
      {
        $skip: skip
      },
      {
        $limit: limit
      }
    ]);

    // Kullanıcı ve yorum bilgilerini ekle
    const populatedPosts = await Post.populate(posts, [
      { path: 'user', select: 'username profilePicture' },
      {
        path: 'comments',
        match: { isActive: true }, // Sadece aktif yorumları getir
        options: { limit: 3, sort: { createdAt: -1 } },
        populate: { path: 'user', select: 'username profilePicture' }
      }
    ]);

    // Toplam gönderi sayısı
    const total = await Post.countDocuments({
      createdAt: { $gte: oneWeekAgo },
      isActive: true
    });

    res.status(200).json({
      status: 'success',
      results: populatedPosts.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: {
        posts: populatedPosts
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Belirli bir gönderiyi getir
exports.getPost = async (req, res) => {
  try {
    console.log(`getPost çağrıldı - Post ID: ${req.params.id}`);

    // Önce gönderiyi bul
    let post = await Post.findById(req.params.id)
      .populate('user', 'username profilePicture bio');

    if (!post) {
      return res.status(404).json({
        status: 'fail',
        message: 'Gönderi bulunamadı'
      });
    }

    // Gönderi bulundu, şimdi sadece aktif yorumları içerecek şekilde comments dizisini güncelle
    // Önce post nesnesini JSON'a çevir ki üzerinde değişiklik yapabilelim (virtuals dahil)
    post = post.toObject({ virtuals: true });

    // Eğer comments dizisi varsa, sadece aktif yorumları içerecek şekilde filtrele
    if (post.comments && post.comments.length > 0) {
      // Aktif yorumları bul
      const activeComments = await Comment.find({
        _id: { $in: post.comments },
        isActive: true
      });

      // Post nesnesinin comments dizisini sadece aktif yorumlarla güncelle
      post.comments = activeComments.map(comment => comment._id);

      console.log(`Post ${post._id} için ${post.comments.length} aktif yorum bulundu.`);
    }

    // Ensure virtual fields are included
    console.log(`Post ${post._id} - likeCount: ${post.likeCount}, commentCount: ${post.commentCount}, likes array length: ${post.likes?.length || 0}`);

    res.status(200).json({
      status: 'success',
      data: {
        post
      }
    });
  } catch (err) {
    console.error('getPost error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Gönderiyi güncelle
exports.updatePost = async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: "Gönderi bulunamadı"
      });
    }

    // Gönderiyi güncelleme yetkiniz var mı?
    if (post.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: "Bu gönderiyi güncelleme yetkiniz yok"
      });
    }

    // Güncellenecek alanları topla
    const updateData = {};

    if (req.body.description) updateData.description = req.body.description;
    if (req.body.location) updateData.location = req.body.location;

    // Eğer bir resim yüklendiyse
    if (req.file) {
      // Eski resmi sil
      if (post.image) {
        const oldImagePath = path.join(__dirname, '..', 'uploads', post.image);
        if (fs.existsSync(oldImagePath)) {
          fs.unlinkSync(oldImagePath);
        }
      }
      updateData.image = req.file.filename;
    }

    // Tags parse etme
    if (req.body.tags) {
      try {
        updateData.tags = JSON.parse(req.body.tags);
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: "Etiketler doğru formatta değil",
          details: error.message
        });
      }
    }

    // Clothing links parse etme
    if (req.body.clothingLinks) {
      try {
        let clothingLinks = JSON.parse(req.body.clothingLinks);

        // Her bir kıyafet bağlantısında yakaType ve color özelliklerinin olup olmadığını kontrol et
        clothingLinks = clothingLinks.map(link => {
          // Eğer yakaType ve color özellikleri yoksa, bunları boş string olarak belirleyelim
          if (!link.hasOwnProperty('yakaType')) link.yakaType = '';
          if (!link.hasOwnProperty('color')) link.color = '';
          return link;
        });

        updateData.clothingLinks = clothingLinks;
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: "Kıyafet bağlantıları doğru formatta değil",
          details: error.message
        });
      }
    }

    // Gönderiyi güncelle
    const updatedPost = await Post.findByIdAndUpdate(
      req.params.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('user', 'username profilePicture');

    res.status(200).json({
      success: true,
      data: updatedPost
    });

  } catch (error) {
    console.error("Gönderi güncelleme hatası:", error);
    res.status(500).json({
      success: false,
      error: "Gönderi güncellenemedi",
      details: error.message
    });
  }
};

// Gönderiyi sil (tamamen sil)
exports.deletePost = async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        status: 'fail',
        message: 'Gönderi bulunamadı'
      });
    }

    // Sadece gönderiyi oluşturan kullanıcı veya admin silebilir
    if (post.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu gönderiyi silme yetkiniz yok'
      });
    }

    // Test için gerçekten silme işlemi yap
    await Post.findByIdAndDelete(req.params.id);

    res.status(204).json({
      status: 'success',
      data: null
    });
  } catch (err) {
    console.error('deletePost error:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Gönderiyi beğen/beğenmekten vazgeç
exports.likeUnlikePost = async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post || !post.isActive) {
      return res.status(404).json({
        status: 'fail',
        message: 'Gönderi bulunamadı'
      });
    }

    // Kullanıcı gönderiyi zaten beğenmiş mi kontrol et
    const isLiked = post.likes.includes(req.user.id);

    let updatedPost;
    if (isLiked) {
      // Beğeniden vazgeç
      updatedPost = await Post.findByIdAndUpdate(
        req.params.id,
        { $pull: { likes: req.user.id } },
        { new: true } // Return the updated document
      );

      // Kullanıcının likedPosts dizisinden gönderiyi çıkar
      await User.findByIdAndUpdate(
        req.user.id,
        { $pull: { likedPosts: req.params.id } }
      );

      res.status(200).json({
        status: 'success',
        message: 'Gönderi beğenisi kaldırıldı',
        data: {
          isLiked: false,
          likeCount: updatedPost.likes.length,
          postId: req.params.id
        }
      });
    } else {
      // Gönderiyi beğen
      updatedPost = await Post.findByIdAndUpdate(
        req.params.id,
        { $push: { likes: req.user.id } },
        { new: true } // Return the updated document
      );

      // Kullanıcının likedPosts dizisine gönderiyi ekle
      await User.findByIdAndUpdate(
        req.user.id,
        { $push: { likedPosts: req.params.id } }
      );

      res.status(200).json({
        status: 'success',
        message: 'Gönderi beğenildi',
        data: {
          isLiked: true,
          likeCount: updatedPost.likes.length,
          postId: req.params.id
        }
      });
    }
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Gönderiyi kaydet/kaydetmekten vazgeç
exports.saveUnsavePost = async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post || !post.isActive) {
      return res.status(404).json({
        status: 'fail',
        message: 'Gönderi bulunamadı'
      });
    }

    // Kullanıcı gönderiyi zaten kaydetmiş mi kontrol et
    const user = await User.findById(req.user.id);
    const isSaved = user.savedPosts.includes(req.params.id);

    if (isSaved) {
      // Kaydedilmiş gönderilerden çıkar
      await User.findByIdAndUpdate(
        req.user.id,
        { $pull: { savedPosts: req.params.id } }
      );

      res.status(200).json({
        status: 'success',
        message: 'Gönderi kaydedilenlerden kaldırıldı'
      });
    } else {
      // Gönderiyi kaydet
      await User.findByIdAndUpdate(
        req.user.id,
        { $push: { savedPosts: req.params.id } }
      );

      res.status(200).json({
        status: 'success',
        message: 'Gönderi kaydedildi'
      });
    }
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Gönderinin yorumlarını getir
exports.getPostComments = async (req, res) => {
  try {
    console.log(`getPostComments çağrıldı - Post ID: ${req.params.id}`);

    const postId = req.params.id;

    // Gönderinin var olup olmadığını kontrol et
    const post = await Post.findById(postId);

    if (!post || !post.isActive) {
      console.log(`Post bulunamadı, ID: ${postId}`);
      return res.status(404).json({
        status: 'fail',
        message: 'Gönderi bulunamadı'
      });
    }

    console.log(`Post bulundu, ID: ${postId}, yorumları getiriliyor...`);

    // Gönderinin yorumlarını getir (hem ana yorumlar hem de alt yorumlar)
    const comments = await Comment.find({ post: postId, isActive: true })
      .populate('user', 'username profilePicture')
      .sort({ createdAt: 1 });

    console.log(`${postId} ID'li gönderinin yorumları yüklendi. ${comments.length} yorum bulundu.`);

    res.status(200).json({
      status: 'success',
      results: comments.length,
      data: {
        comments
      }
    });
  } catch (err) {
    console.error('Yorum getirme hatası:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Gönderiye yorum ekle
exports.addComment = async (req, res) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post || !post.isActive) {
      return res.status(404).json({
        status: 'fail',
        message: 'Gönderi bulunamadı'
      });
    }

    const { text, parentComment, isSubReply, originalParentId } = req.body;

    if (!text) {
      return res.status(400).json({
        status: 'fail',
        message: 'Yorum metni zorunludur'
      });
    }

    // Log the incoming request for debugging
    console.log('Add Comment Request:', {
      postId: req.params.id,
      text,
      parentComment,
      isSubReply,
      originalParentId
    });

    // Determine the correct parent for the comment
    let parentId = parentComment || null;

    // If this is a sub-reply (reply to a reply), we need to handle it differently
    if (isSubReply && parentComment) {
      console.log('Processing sub-reply to comment:', parentComment);

      // Verify that the parent comment exists
      const parentCommentObj = await Comment.findById(parentComment);
      if (!parentCommentObj) {
        return res.status(404).json({
          status: 'fail',
          message: 'Parent comment not found'
        });
      }

      // For sub-replies, we set the parent to the comment we're directly replying to
      parentId = parentComment;
    }

    // Yeni yorum oluştur
    const newComment = new Comment({
      user: req.user.id,
      post: req.params.id,
      text,
      parent: parentId
    });

    // Yorumu kaydet
    const savedComment = await newComment.save();

    // Kullanıcı bilgilerini ekle
    const populatedComment = await Comment.findById(savedComment._id)
      .populate('user', 'username profilePicture');

    console.log('Comment saved successfully:', {
      id: populatedComment._id,
      parent: populatedComment.parent,
      isSubReply
    });

    res.status(201).json({
      status: 'success',
      data: {
        comment: populatedComment
      }
    });
  } catch (err) {
    console.error('Error adding comment:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Yorumu beğen/beğenmekten vazgeç
exports.likeUnlikeComment = async (req, res) => {
  try {
    const comment = await Comment.findById(req.params.commentId);

    if (!comment || !comment.isActive) {
      return res.status(404).json({
        status: 'fail',
        message: 'Yorum bulunamadı'
      });
    }

    // Kullanıcı yorumu zaten beğenmiş mi kontrol et
    const isLiked = comment.likes.includes(req.user.id);

    let updatedComment;
    if (isLiked) {
      // Beğeniden vazgeç
      updatedComment = await Comment.findByIdAndUpdate(
        req.params.commentId,
        { $pull: { likes: req.user.id } },
        { new: true }
      ).populate('user', 'username profilePicture');

      res.status(200).json({
        status: 'success',
        message: 'Yorum beğenisi kaldırıldı',
        data: {
          isLiked: false,
          likeCount: updatedComment.likes.length,
          commentId: req.params.commentId,
          comment: updatedComment
        }
      });
    } else {
      // Yorumu beğen
      updatedComment = await Comment.findByIdAndUpdate(
        req.params.commentId,
        { $push: { likes: req.user.id } },
        { new: true }
      ).populate('user', 'username profilePicture');

      res.status(200).json({
        status: 'success',
        message: 'Yorum beğenildi',
        data: {
          isLiked: true,
          likeCount: updatedComment.likes.length,
          commentId: req.params.commentId,
          comment: updatedComment
        }
      });
    }
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Yorumu sil
exports.deleteComment = async (req, res) => {
  try {
    console.log(`Deleting comment with ID: ${req.params.commentId}`);

    const comment = await Comment.findById(req.params.commentId);

    if (!comment) {
      return res.status(404).json({
        status: 'fail',
        message: 'Yorum bulunamadı'
      });
    }

    // Sadece yorumu yazan kullanıcı, gönderi sahibi veya admin silebilir
    const post = await Post.findById(comment.post);

    if (
      comment.user.toString() !== req.user.id &&
      post.user.toString() !== req.user.id &&
      req.user.role !== 'admin'
    ) {
      return res.status(403).json({
        status: 'fail',
        message: 'Bu yorumu silme yetkiniz yok'
      });
    }

    // Store comment info for logging
    const commentId = comment._id;
    const postId = comment.post;
    const isParentComment = !comment.parent;

    // Yorumu tamamen sil (hard delete)
    const deleteResult = await Comment.findByIdAndDelete(commentId);
    console.log(`Comment deletion result:`, deleteResult ? 'Success' : 'Failed');

    // Alt yorumları da sil
    if (isParentComment) {
      const childDeleteResult = await Comment.deleteMany({ parent: commentId });
      console.log(`Child comments deletion result: ${childDeleteResult.deletedCount} comments deleted`);
    }

    // Yorumu gönderinin comments dizisinden kaldır
    if (isParentComment) {
      const updateResult = await Post.findByIdAndUpdate(
        postId,
        { $pull: { comments: commentId } },
        { new: true }
      );
      console.log(`Post update result:`, updateResult ? 'Success' : 'Failed');
      console.log(`Comment ${commentId} removed from post ${postId} comments array`);
    }

    res.status(200).json({
      status: 'success',
      message: 'Yorum başarıyla silindi'
    });
  } catch (err) {
    console.error('Error deleting comment:', err);
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};