const db = require('../models');
const Follow = db.follow;
const User = db.user;

// Takip etme işlemi
exports.followUser = async (req, res) => {
  try {
    // Takip edilecek kullanıcıyı bul
    const userToFollow = await User.findById(req.params.userId);
    
    if (!userToFollow) {
      return res.status(404).json({
        status: 'fail',
        message: 'Takip edilecek kullanıcı bulunamadı'
      });
    }
    
    // Kendisini takip etmeye çalışıyorsa
    if (req.user.id === req.params.userId) {
      return res.status(400).json({
        status: 'fail',
        message: 'Kendinizi takip edemezsiniz'
      });
    }
    
    // Zaten takip ediyor mu kontrol et
    const existingFollow = await Follow.findOne({
      follower: req.user.id,
      following: req.params.userId,
      isActive: true
    });
    
    if (existingFollow) {
      return res.status(400).json({
        status: 'fail',
        message: '<PERSON><PERSON> kullanıcıyı zaten takip ediyorsunuz'
      });
    }
    
    // Takip durumunu belirle
    let status = 'accepted';
    
    // Eğer takip edilecek hesap özel ise, takip isteği gönder
    if (userToFollow.isPrivate) {
      status = 'pending';
    }
    
    // Yeni takip kaydı oluştur
    const newFollow = await Follow.create({
      follower: req.user.id,
      following: req.params.userId,
      status
    });
    
    // Duruma göre mesaj
    let message = 'Kullanıcıyı takip etmeye başladınız';
    if (status === 'pending') {
      message = 'Takip isteği gönderildi';
    }
    
    res.status(200).json({
      status: 'success',
      message,
      data: {
        follow: newFollow
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Takipten çıkma işlemi
exports.unfollowUser = async (req, res) => {
  try {
    // Takip ilişkisini bul ve sil
    const follow = await Follow.findOneAndDelete({
      follower: req.user.id,
      following: req.params.userId,
      isActive: true
    });
    
    if (!follow) {
      return res.status(400).json({
        status: 'fail',
        message: 'Bu kullanıcıyı takip etmiyorsunuz'
      });
    }
    
    // Kullanıcı dizilerini manuel olarak güncelle
    await Promise.all([
      // Takip eden kullanıcının following dizisinden takip edilen kullanıcıyı çıkar
      User.findByIdAndUpdate(
        req.user.id,
        { $pull: { following: req.params.userId } }
      ),
      
      // Takip edilen kullanıcının followers dizisinden takip eden kullanıcıyı çıkar
      User.findByIdAndUpdate(
        req.params.userId,
        { $pull: { followers: req.user.id } }
      )
    ]);
    
    res.status(200).json({
      status: 'success',
      message: 'Kullanıcıyı takipten çıktınız'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Takip isteklerini getir
exports.getFollowRequests = async (req, res) => {
  try {
    // Bekleyen takip isteklerini bul
    const requests = await Follow.find({
      following: req.user.id,
      status: 'pending',
      isActive: true
    }).populate('follower', 'username profilePicture bio');
    
    res.status(200).json({
      status: 'success',
      results: requests.length,
      data: {
        requests
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Takip isteğini kabul et
exports.acceptFollowRequest = async (req, res) => {
  try {
    // Takip isteğini bul
    const request = await Follow.findOne({
      _id: req.params.requestId,
      following: req.user.id,
      status: 'pending',
      isActive: true
    });
    
    if (!request) {
      return res.status(404).json({
        status: 'fail',
        message: 'Takip isteği bulunamadı'
      });
    }
    
    // İsteği kabul et
    request.status = 'accepted';
    await request.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Takip isteği kabul edildi',
      data: {
        follow: request
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Takip isteğini reddet
exports.rejectFollowRequest = async (req, res) => {
  try {
    // Takip isteğini bul
    const request = await Follow.findOne({
      _id: req.params.requestId,
      following: req.user.id,
      status: 'pending',
      isActive: true
    });
    
    if (!request) {
      return res.status(404).json({
        status: 'fail',
        message: 'Takip isteği bulunamadı'
      });
    }
    
    // İsteği reddet
    request.status = 'rejected';
    await request.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Takip isteği reddedildi'
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Takipçileri getir
exports.getFollowers = async (req, res) => {
  try {
    // Kullanıcı ID'sini belirle (kendi profili veya başka kullanıcı)
    const userId = req.params.userId || req.user.id;
    
    // Kullanıcıyı kontrol et
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: 'fail',
        message: 'Kullanıcı bulunamadı'
      });
    }
    
    // Takipçileri bul
    const followers = await Follow.find({
      following: userId,
      status: 'accepted',
      isActive: true
    }).populate('follower', 'username profilePicture bio fullName');
    
    // Takipçi listesini oluştur
    const followersList = followers.map(follow => follow.follower);
    
    res.status(200).json({
      status: 'success',
      results: followersList.length,
      data: {
        followers: followersList
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
};

// Takip edilenleri getir
exports.getFollowing = async (req, res) => {
  try {
    // Kullanıcı ID'sini belirle (kendi profili veya başka kullanıcı)
    const userId = req.params.userId || req.user.id;
    
    // Kullanıcıyı kontrol et
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        status: 'fail',
        message: 'Kullanıcı bulunamadı'
      });
    }
    
    // Takip edilenleri bul
    const following = await Follow.find({
      follower: userId,
      status: 'accepted',
      isActive: true
    }).populate('following', 'username profilePicture bio fullName');
    
    // Takip edilen listesini oluştur
    const followingList = following.map(follow => follow.following);
    
    res.status(200).json({
      status: 'success',
      results: followingList.length,
      data: {
        following: followingList
      }
    });
  } catch (err) {
    res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
}; 