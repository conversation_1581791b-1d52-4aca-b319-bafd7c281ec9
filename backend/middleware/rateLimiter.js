const rateLimit = require('express-rate-limit');

// Ortama göre rate limit değerlerini ayarla
const isProd = process.env.NODE_ENV === 'production';
const API_WINDOW_MS = isProd ? 15 * 60 * 1000 : 1 * 60 * 1000; // Üretimde 15 dk, dev'de 1 dk
const API_MAX = isProd ? 150 : 300; // Üretimde daha kısıtlı
const LOGIN_WINDOW_MS = isProd ? 15 * 60 * 1000 : 1 * 60 * 1000;
const LOGIN_MAX = isProd ? 10 : 50;

// Genel rate limiter - tüm API istekleri için
exports.apiLimiter = rateLimit({
  windowMs: API_WINDOW_MS,
  max: API_MAX, // Her IP adresi için maksimum istek
  standardHeaders: true, // Rate limit bilgilerini RateLimit-* başlıklarında döndür
  legacyHeaders: false, // X-RateLimit-* başlıklarını devre dışı bırak
  message: {
    status: 'fail',
    message: 'Çok fazla istek yaptınız, lütfen daha sonra tekrar deneyin!'
  }
});

// Login için özel rate limiter - brute force saldırılarını engellemek için
exports.loginLimiter = rateLimit({
  windowMs: LOGIN_WINDOW_MS,
  max: LOGIN_MAX, // Her IP adresi için maksimum giriş denemesi
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 'fail',
    message: 'Çok fazla giriş denemesi yaptınız, lütfen daha sonra tekrar deneyin!'
  }
});

// Şifre sıfırlama için rate limiter
exports.passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 saat
  max: isProd ? 3 : 10, // Üretimde daha kısıtlı
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 'fail',
    message: 'Çok fazla şifre sıfırlama isteği yaptınız, lütfen 1 saat sonra tekrar deneyin!'
  }
}); 