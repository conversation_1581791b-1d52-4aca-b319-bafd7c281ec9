const multer = require('multer');
const path = require('path');
const fs = require('fs');
const AWS = require('aws-sdk');
const multerS3 = require('multer-s3');
const { logger } = require('../utils/logger');

// AWS S3 konfigürasyonu
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'eu-north-1'
});

const s3 = new AWS.S3();
const bucketName = process.env.S3_BUCKET_NAME || 'styleupbucket';

console.log('S3 Storage is forced enabled.');

// Varsayılan profil resmi URL'i
const DEFAULT_PROFILE_IMAGE = 'https://styleupbucket.s3.eu-north-1.amazonaws.com/default-profile.jpg';

// Dosya filtresi - sadece resim dosyalarını kabul et
const fileFilter = (req, file, cb) => {
  // Gelen dosyanın detaylarını logla
  console.log(`Dosya yükleme isteği alındı: ${file.fieldname}, mimetype: ${file.mimetype}, originalname: ${file.originalname}`);

  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Sadece resim dosyaları yüklenebilir!'), false);
  }
};

// S3 depolama ayarları
// Not: ACL parametresi kaldırıldı çünkü S3 bucket artık ACL'lere izin vermiyor
// Bucket politikası ile dosya erişimi yönetiliyor
const s3Storage = multerS3({
  s3: s3,
  bucket: bucketName,
  contentType: multerS3.AUTO_CONTENT_TYPE,
  key: function (req, file, cb) {
    console.log("S3 key fonksiyonu çağrıldı");
    const userId = req.user ? req.user.id || req.user._id : 'guest';
    const timestamp = Date.now();
    const filename = file.originalname.replace(/\s+/g, '-').toLowerCase();
    const ext = path.extname(filename) || '.jpg';

    // Fieldname'e göre S3 dizinini ayarla (profil/gönderi)
    let folder = 'uploads'; // Varsayılan klasör

    // Alan adına göre klasör belirle
    if (file.fieldname === 'profilePicture') {
      folder = 'profiles';
    } else if (file.fieldname === 'postImage') {
      folder = 'posts';
    }

    // Field adını ve dosya adını logla
    console.log(`S3'e Dosya yükleniyor: Alan adı: ${file.fieldname}, Dosya adı: ${filename}, Hedef: ${folder}`);
    const key = `${folder}/${userId}_${timestamp}${ext}`;
    console.log("Oluşturulan S3 key:", key);
    cb(null, key);
  },
  metadata: function (req, file, cb) {
    console.log("S3 metadata fonksiyonu çağrıldı");
    cb(null, { fieldName: file.fieldname });
  },
  // Add cache control headers for better performance
  contentDisposition: 'inline',
  cacheControl: 'max-age=31536000' // Cache for 1 year
  // acl parameter removed as the bucket does not allow ACLs
});

// Multer ayarları - Doğrudan s3Storage kullan
const upload = multer({
  storage: s3Storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  }
});

// AWS S3 bağlantı testi
const testS3Connection = async () => {
  try {
    console.log("S3 bağlantısı test ediliyor...");
    console.log("S3 Bucket:", bucketName);
    console.log("AWS Region:", process.env.AWS_REGION || 'eu-north-1');

    const data = await s3.listBuckets().promise();
    console.log("S3 bağlantısı başarılı, buckets:", data.Buckets.map(b => b.Name));

    // Bucket varlığını kontrol et
    const bucketExists = data.Buckets.some(b => b.Name === bucketName);
    if (!bucketExists) {
      console.error(`UYARI: ${bucketName} adında bir bucket bulunamadı!`);
    } else {
      console.log(`${bucketName} bucket'ı bulundu ve erişilebilir.`);
    }

    return true;
  } catch (error) {
    console.error("S3 bağlantı hatası:", error.message);
    console.error("AWS kimlik bilgilerinizi kontrol edin!");
    return false;
  }
};

// S3 bağlantı testini çağır
testS3Connection().then(connected => {
  if (connected) {
    console.log("S3 depolama hazır, dosya yüklemeleri için kullanılabilir.");
  } else {
    console.error("S3 depolama kullanılamıyor! Dosya yüklemeleri çalışmayabilir.");
  }
});

// Debug için multer hata ayıklama middleware'i
const debugMulter = (req, res, next) => {
  console.log('Debug Multer - Request Headers:', req.headers);
  console.log('Debug Multer - Content-Type:', req.headers['content-type']);

  // Formdaki alanların içeriğini kontrol et
  if (req.body) {
    console.log('Debug Multer - Body keys:', Object.keys(req.body));
  }

  // Eğer bir file veya files varsa logla
  if (req.file) {
    console.log('Debug Multer - File mevcut:', req.file);
  } else {
    console.log('Debug Multer - File bulunamadı');
  }

  if (req.files) {
    console.log('Debug Multer - Files mevcut:', Object.keys(req.files).length);
  } else {
    console.log('Debug Multer - Files bulunamadı');
  }

  next();
};

// Özel upload middleware'leri oluşturalım
const uploadPostImage = (req, res, next) => {
  console.log('uploadPostImage middleware başladı (S3)');
  console.log('İstek Content-Type:', req.headers['content-type']);

  // multipart/form-data kontrolü
  if (!req.headers['content-type'] || !req.headers['content-type'].includes('multipart/form-data')) {
    console.error('Content-Type multipart/form-data değil!', req.headers['content-type']);
    return res.status(400).json({
      status: 'fail',
      message: 'Hata: İstek multipart/form-data tipinde olmalıdır'
    });
  }

  upload.single('postImage')(req, res, function(err) {
    console.log('Multer işlemi tamamlandı, hata var mı:', err ? 'EVET' : 'HAYIR');

    if (err) {
      console.error('Multer S3 upload hatası:', err);
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          status: 'fail',
          message: 'Dosya boyutu çok büyük! Maksimum 5MB olmalıdır.'
        });
      }
      return res.status(400).json({
        status: 'fail',
        message: err.message || 'Dosya yükleme hatası'
      });
    }

    // Dosya kontrolü
    if (!req.file) {
      console.error('Dosya bulunamadı (S3): req.file yok');
      console.log('Headers:', req.headers);
      console.log('Body keys:', Object.keys(req.body));

      // Form alanlarını detaylı göster
      for (const key in req.body) {
        console.log(`Form alanı ${key}:`, req.body[key].substring ? req.body[key].substring(0, 100) + '...' : req.body[key]);
      }

      return res.status(400).json({
        status: 'fail',
        message: 'Dosya yüklenemedi. Lütfen geçerli bir resim seçin.'
      });
    }

    console.log('uploadPostImage (S3) başarılı, req.file:', req.file);
    console.log('File location (URL):', req.file.location);
    next();
  });
};

// Profil resmi için multer middleware (Sadece S3)
const uploadProfilePicture = upload.single('profilePicture');

logger.info('AWS S3 storage configured for file uploads (Forced)');

// Hata işleme
const handleMulterError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        status: 'fail',
        message: 'Dosya boyutu çok büyük! Maksimum 5MB olmalıdır.'
      });
    }
    return res.status(400).json({
      status: 'fail',
      message: err.message
    });
  }
  next(err);
};

module.exports = {
  upload,
  uploadProfilePicture,
  uploadPostImage,
  debugMulter,
  DEFAULT_PROFILE_IMAGE
};
module.exports.handleMulterError = handleMulterError;