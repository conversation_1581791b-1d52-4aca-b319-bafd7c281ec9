const User = require('../models/User');
const { logger } = require('../utils/logger');

/**
 * Mobil cihaz kimlik doğrulaması için middleware
 * Bu middleware, istek header'larında cihaz bilgilerini kontrol eder
 */
exports.validateMobileDevice = async (req, res, next) => {
  try {
    // Cihaz bilgilerini al
    const deviceId = req.headers['x-device-id'];
    const deviceModel = req.headers['x-device-model'];
    const appVersion = req.headers['x-app-version'];
    
    // Minimum app sürümü kontrolü yapabilirsiniz
    const minimumAppVersion = process.env.MINIMUM_APP_VERSION || '1.0.0';
    
    // Basit validasyon kontrolü
    if (!deviceId) {
      logger.warn(`Cihaz kimliği eksik: ${req.originalUrl}`);
      // İstersek bunu zorunlu hale getirebiliriz
      // return res.status(403).json({
      //   status: 'fail',
      //   message: 'Cihaz kimliği gereklidir'
      // });
    }
    
    // Eğer kullanıcı oturum açmışsa ve cihaz kimliği varsa
    if (req.user && deviceId) {
      // Kullanıcının kayıtlı cihazlarını kontrol et (isteğe bağlı)
      // Örnek: Bu kullanıcının bu cihazla daha önce giriş yapıp yapmadığını kontrol et
      
      // Cihaz kaydını güncelle/ekle (opsiyonel)
      await User.findByIdAndUpdate(
        req.user._id,
        { 
          $addToSet: { 
            devices: { 
              deviceId, 
              deviceModel,
              lastSeen: new Date(),
              appVersion
            } 
          } 
        },
        { new: true }
      );
    }
    
    // Cihaz bilgilerini request nesnesine ekle
    req.device = { deviceId, deviceModel, appVersion };
    
    next();
  } catch (err) {
    logger.error(`Cihaz doğrulama hatası: ${err.message}`);
    next();
  }
};

/**
 * Şüpheli istekleri tespit etmek için middleware 
 */
exports.detectSuspiciousActivity = (req, res, next) => {
  // Şüpheli header kombinasyonlarını kontrol et
  const userAgent = req.headers['user-agent'] || '';
  const acceptLanguage = req.headers['accept-language'] || '';
  const acceptEncoding = req.headers['accept-encoding'] || '';
  const forwardedFor = req.headers['x-forwarded-for'] || req.ip;
  
  // Kullanıcı oturum açmışsa ve mobil cihaz değilse şüpheli olabilir
  const isMobileUserAgent = /mobile|android|iphone|ipad|ipod/i.test(userAgent);
  const claimsToBeApp = req.headers['x-device-id'] || req.headers['x-app-version'];
  
  if (claimsToBeApp && !isMobileUserAgent) {
    logger.warn(`Şüpheli istek: Mobil cihaz olduğunu iddia ediyor ama user-agent mobil değil. IP: ${forwardedFor}`);
  }
  
  next();
}; 