# StyleUp API Dokümantasyonu

## İçindekiler
1. [<PERSON><PERSON>](#genel-bilgiler)
2. [<PERSON><PERSON>ru<PERSON>](#kimlik-doğrulama)
3. [Kullanıcı İşlemleri](#kullanıcı-işlemleri)
4. [Gönderi İşlemleri](#gönderi-işlemleri)
5. [<PERSON><PERSON>p İşlemleri](#takip-işlemleri)
6. [Keşfet Bölümü](#keşfet-bölümü)
7. [Mesajlaşma İşlemleri](#mesajlaşma-işlemleri)
8. [Ş<PERSON>re Sıfırlama İşlemleri](#şifre-sıfırlama-işlemleri)
9. [Kullanıcı Arama İşlemleri](#kullanıcı-arama-işlemleri)
10. [<PERSON>a Kodları](#hata-kodları)
11. [Önemli Notlar](#önemli-notlar)
12. [<PERSON><PERSON> İşlemleri](#yorum-işlemleri)

## Genel Bilgiler
- Base URL: `http://localhost:5000`
- Tüm istekler için `Content-Type: application/json` header'ı gereklidir
- Kimlik doğrulama gerektiren endpoint'ler için `Authorization: Bearer <token>` header'ı gereklidir

## Kimlik Doğrulama

### Kayıt Olma - Adım 1
```
POST /api/auth/signup/step1
```
**Request Body:**
```json
{
  "fullName": "string",
  "email": "string",
  "password": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "tempToken": "string",
  "profilePicture": "https://styleupbucket.s3.eu-north-1.amazonaws.com/default-profile.jpg",
  "message": "Kayıt işleminin ilk adımı tamamlandı, lütfen kullanıcı adı seçin"
}
```

### Google ile Giriş/Kayıt
```
POST /api/auth/google
```
**Request Body:**
```json
{
  "idToken": "string",
  "email": "string",
  "fullName": "string (opsiyonel)",
  "profilePicture": "string (opsiyonel)"
}
```
**Response (Kullanıcı zaten varsa):**
```json
{
  "status": "success",
  "token": "string",
  "refreshToken": "string",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "profilePicture": "string",
      "bio": "string",
      "provider": "google"
    }
  }
}
```
**Response (Yeni kullanıcı kaydı gerekiyorsa):**
```json
{
  "status": "success",
  "tempToken": "string",
  "profilePicture": "string",
  "isNewUser": true,
  "message": "Kayıt işleminin ilk adımı tamamlandı, lütfen kullanıcı adı seçin"
}
```

### Apple ile Giriş/Kayıt
```
POST /api/auth/apple
```
**Request Body:**
```json
{
  "idToken": "string",
  "email": "string",
  "fullName": "string (opsiyonel)",
  "profilePicture": "string (opsiyonel)"
}
```
**Response (Kullanıcı zaten varsa):**
```json
{
  "status": "success",
  "token": "string",
  "refreshToken": "string",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "profilePicture": "string",
      "bio": "string",
      "provider": "apple"
    }
  }
}
```
**Response (Yeni kullanıcı kaydı gerekiyorsa):**
```json
{
  "status": "success",
  "tempToken": "string",
  "profilePicture": "string",
  "isNewUser": true,
  "message": "Kayıt işleminin ilk adımı tamamlandı, lütfen kullanıcı adı seçin"
}
```

### Kayıt Olma - Adım 2
```
POST /api/auth/signup/step2
```
**Request Body (multipart/form-data):**
- tempToken: string
- username: string
- profilePicture: file (opsiyonel)

**Response:**
```json
{
  "status": "success",
  "token": "string",
  "refreshToken": "string",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "profilePicture": "string (S3 URL)",
      "bio": "string"
    }
  }
}
```

### Giriş Yapma
```
POST /api/auth/login
```
**Request Body:**
```json
{
  "identifier": "string", // E-posta adresi veya kullanıcı adı
  "password": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "token": "string",
  "refreshToken": "string",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "profilePicture": "string",
      "bio": "string"
    }
  }
}
```

### Token Yenileme
```
POST /api/auth/refresh-token
```
**Response:**
```json
{
  "status": "success",
  "token": "string",
  "refreshToken": "string",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "profilePicture": "string",
      "bio": "string"
    }
  }
}
```

## Kullanıcı İşlemleri

### Kullanıcı Profili Görüntüleme
```
GET /api/users/:userId
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "profilePicture": "string",
      "bio": "string",
      "followers": ["userId"],
      "following": ["userId"],
      "postCount": "number",
      "followingCount": "number"
    }
  }
}
```

### Kullanıcı Arama
```
GET /api/users/search?q=searchTerm
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "profilePicture": "string",
        "bio": "string"
      }
    ]
  }
}
```

### Profil Güncelleme
```
PATCH /api/users/profile
```
**Request Body (multipart/form-data):**
- username: string (opsiyonel)
- email: string (opsiyonel)
- bio: string (opsiyonel)
- isPrivate: boolean (opsiyonel)
- profilePicture: file (opsiyonel) - Resim dosyası Amazon S3'e yüklenecektir

**Response:**
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "profilePicture": "string (S3 URL)",
      "bio": "string",
      "isPrivate": boolean
    }
  }
}
```

## Gönderi İşlemleri

### Gönderi Oluşturma
```
POST /api/posts
```
**Request Body (multipart/form-data):**
- postImage: file (zorunlu) - Gönderi resmi dosyası (S3'e yüklenecek)
- description: string (opsiyonel)
- tags: string veya array (opsiyonel) - Virgülle ayrılmış etiketler veya dizi
- clothingLinks: string (opsiyonel) - JSON formatında kıyafet bağlantıları
  - Her bir bağlantı şu alanları içerebilir:
    - type: string (zorunlu) - Kıyafet türü (örn: shirt, pants, jacket)
    - url: string (zorunlu) - Ürün bağlantısı
    - title: string (zorunlu) - Ürün başlığı
    - price: number (opsiyonel) - Ürün fiyatı
    - brand: string (opsiyonel) - Ürün markası
    - yakaType: string (opsiyonel) - Yaka tipi ('V Yaka', 'Bisiklet Yaka', 'Polo Yaka', 'Hakim Yaka', 'Diğer')
    - color: string (opsiyonel) - Ürün rengi
- location: string (opsiyonel)

**Response:**
```json
{
  "status": "success",
  "data": {
    "post": {
      "id": "string",
      "user": "string",
      "image": "string (S3 URL)",
      "description": "string",
      "tags": ["string"],
      "clothingLinks": [
        {
          "type": "string",
          "url": "string",
          "title": "string",
          "price": "number",
          "brand": "string",
          "yakaType": "string",
          "color": "string"
        }
      ],
      "likes": [],
      "comments": [],
      "location": "string",
      "likeCount": 0,
      "commentCount": 0,
      "createdAt": "string",
      "updatedAt": "string"
    }
  }
}
```

### Ana Sayfa Gönderileri
```
GET /api/posts/feed
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "posts": [
      {
        "id": "string",
        "user": {
          "id": "string",
          "username": "string",
          "profilePicture": "string"
        },
        "image": "string",
        "description": "string",
        "tags": ["string"],
        "clothingLinks": [
          {
            "type": "string",
            "url": "string",
            "title": "string",
            "price": "number",
            "brand": "string",
            "yakaType": "string",
            "color": "string"
          }
        ],
        "location": "string",
        "likes": ["userId"],
        "comments": ["commentId"],
        "likeCount": "number",
        "commentCount": "number",
        "createdAt": "date"
      }
    ]
  }
}
```

### Gönderi Detayı
```
GET /api/posts/:id
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "post": {
      "id": "string",
      "user": {
        "id": "string",
        "username": "string",
        "profilePicture": "string"
      },
      "image": "string",
      "description": "string",
      "tags": ["string"],
      "clothingLinks": [
        {
          "type": "string",
          "url": "string",
          "title": "string",
          "price": "number",
          "brand": "string",
          "yakaType": "string",
          "color": "string"
        }
      ],
      "location": "string",
      "likes": ["userId"],
      "comments": [
        {
          "id": "string",
          "user": {
            "id": "string",
            "username": "string",
            "profilePicture": "string"
          },
          "text": "string",
          "likes": ["userId"],
          "likeCount": "number",
          "createdAt": "date"
        }
      ],
      "likeCount": "number",
      "commentCount": "number",
      "createdAt": "date"
    }
  }
}
```

### Gönderi Beğenme/Beğenmekten Vazgeçme
```
POST /api/posts/:id/like
```
**Headers:**
```
Authorization: Bearer <token>
```
**Response:**
```json
{
  "status": "success",
  "message": "Gönderi beğenildi"
}
```
veya
```json
{
  "status": "success",
  "message": "Gönderi beğenisi kaldırıldı"
}
```

## Takip İşlemleri

### Kullanıcıyı Takip Et
```
POST /api/follow/users/:userId/follow
```
veya
```
POST /api/follow/users/:userId
```
**Headers:**
```
Authorization: Bearer <token>
```
**Response:**
```json
{
  "status": "success",
  "message": "Kullanıcıyı takip etmeye başladınız",
  "data": {
    "follow": {
      "id": "string",
      "follower": "string",
      "following": "string",
      "status": "accepted",
      "createdAt": "string"
    }
  }
}
```

### Takipten Çık
```
DELETE /api/follow/users/:userId/follow
```
veya
```
DELETE /api/follow/users/:userId
```
**Headers:**
```
Authorization: Bearer <token>
```
**Response:**
```json
{
  "status": "success",
  "message": "Kullanıcıyı takipten çıktınız"
}
```

### Takipçileri Görüntüleme
```
GET /api/follow/users/:userId/followers
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "followers": [
      {
        "id": "string",
        "username": "string",
        "profilePicture": "string",
        "bio": "string"
      }
    ]
  }
}
```

### Takip Edilenleri Görüntüleme
```
GET /api/follow/users/:userId/following
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "following": [
      {
        "id": "string",
        "username": "string",
        "profilePicture": "string",
        "bio": "string"
      }
    ]
  }
}
```

## Keşfet Bölümü

### Keşfet Gönderileri
```
GET /api/discover
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "posts": [
      {
        "id": "string",
        "user": {
          "id": "string",
          "username": "string",
          "profilePicture": "string"
        },
        "image": "string",
        "description": "string",
        "tags": ["string"],
        "clothingLinks": [
          {
            "type": "string",
            "url": "string",
            "title": "string",
            "price": "number",
            "brand": "string",
            "yakaType": "string",
            "color": "string"
          }
        ],
        "location": "string",
        "likes": ["userId"],
        "comments": ["commentId"],
        "likeCount": "number",
        "commentCount": "number",
        "interactionScore": "number",
        "createdAt": "date"
      }
    ]
  }
}
```

## Güncel Değişiklikler ve Geliştirmeler

### Gönderi Oluşturma ve Görüntüleme Geliştirmeleri (2024 Haziran)

1. **Kıyafet Bağlantıları İyileştirmeleri**:
   - Kıyafet bağlantılarına yaka tipi (yakaType) ve renk (color) alanları eklendi.
   - yakaType için önceden tanımlanmış değerler: 'V Yaka', 'Bisiklet Yaka', 'Polo Yaka', 'Hakim Yaka', 'Diğer'.
   - color alanı serbest metin olarak girilebiliyor.

2. **Gönderi Görüntüleme**:
   - Tüm gönderi görüntüleme yanıtlarında clothingLinks içinde yakaType ve color bilgileri de dönülmeye başlandı.
   - Frontend bu alanları gönderi detaylarında ve listeleme ekranlarında gösterebilir.

3. **AWS S3 Entegrasyonu İyileştirmeleri**:
   - S3 bucket yapılandırması güncellendi ve ACL kontrolü kaldırıldı.
   - Bu değişiklikle birlikte gönderi resimleri daha güvenli bir biçimde depolanıyor.

Bu güncellemelerle ilgili daha detaylı bilgi için backend ekibiyle iletişime geçebilirsiniz.

## Mesajlaşma İşlemleri

### Sohbetleri Listeleme
```
GET /api/messages/conversations
```
**Query Parameters:**
- `page`: Sayfa numarası (varsayılan: 1)
- `limit`: Sayfa başına sohbet sayısı (varsayılan: 20)

**Response:**
```json
{
  "status": "success",
  "results": "number",
  "total": "number",
  "totalPages": "number",
  "currentPage": "number",
  "data": {
    "conversations": [
      {
        "_id": "string",
        "otherParticipant": {
          "_id": "string",
          "username": "string",
          "profilePicture": "string"
        },
        "lastMessage": {
          "_id": "string",
          "text": "string",
          "sender": {
            "_id": "string",
            "username": "string",
            "profilePicture": "string"
          },
          "createdAt": "date"
        },
        "updatedAt": "date",
        "createdAt": "date"
      }
    ]
  }
}
```

### Sohbet Detayı ve Mesajları
```
GET /api/messages/conversations/:conversationId
```
**Query Parameters:**
- `page`: Sayfa numarası (varsayılan: 1)
- `limit`: Sayfa başına mesaj sayısı (varsayılan: 20)

**Response:**
```json
{
  "status": "success",
  "results": "number",
  "total": "number",
  "totalPages": "number",
  "currentPage": "number",
  "data": {
    "messages": [
      {
        "_id": "string",
        "sender": {
          "_id": "string",
          "username": "string",
          "profilePicture": "string"
        },
        "text": "string",
        "attachments": ["string"],
        "isRead": "boolean",
        "readAt": "date",
        "createdAt": "date"
      }
    ]
  }
}
```

### Yeni Sohbet Başlatma
```
POST /api/messages/conversations
```

## Şifre Sıfırlama İşlemleri

### Şifre Sıfırlama İsteği Gönderme
```
POST /api/auth/forgot-password
```
**Request Body:**
```json
{
  "email": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Şifre sıfırlama bağlantısı e-posta adresinize gönderildi!"
}
```

### Şifre Sıfırlama
```
PATCH /api/auth/reset-password/:token
```
**Request Body:**
```json
{
  "password": "string"
}
```
**Response:**
```json
{
  "status": "success",
  "token": "string",
  "refreshToken": "string",
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "fullName": "string",
      "profilePicture": "string",
      "bio": "string"
    }
  }
}
```

## Kullanıcı Arama İşlemleri

### Kullanıcı Arama
```
GET /api/users/search
```
**Query Parameters:**
- `query`: Arama sorgusu (kullanıcı adı, e-posta veya tam ad ile arama)
- `page`: Sayfa numarası (varsayılan: 1)
- `limit`: Sayfa başına sonuç sayısı (varsayılan: 20)

**Response:**
```json
{
  "status": "success",
  "results": "number",
  "total": "number",
  "totalPages": "number",
  "currentPage": "number",
  "data": {
    "users": [
      {
        "id": "string",
        "username": "string",
        "email": "string",
        "fullName": "string",
        "profilePicture": "string",
        "bio": "string"
      }
    ]
  }
}
```

## Yorum İşlemleri

### Gönderinin Yorumlarını Getirme
- **URL**: `GET /api/posts/:id/comments`
- **Parametre**: 
  - `id`: Gönderi ID'si
- **Yanıt**: 
  ```json
  {
    "status": "success",
    "results": 2,
    "data": {
      "comments": [
        {
          "_id": "60d21b4667d0d8992e610c85",
          "user": {
            "_id": "60d0fe4f5311236168a109ca",
            "username": "kullanici_adi",
            "profilePicture": "profile-1.jpg"
          },
          "post": "60d21b4667d0d8992e610c85",
          "text": "Harika bir kombin!",
          "likes": [],
          "parent": null,
          "createdAt": "2023-06-21T08:40:32.142Z",
          "updatedAt": "2023-06-21T08:40:32.142Z",
          "likeCount": 0
        },
        {
          "_id": "60d21b4667d0d8992e610c86",
          "user": {
            "_id": "60d0fe4f5311236168a109cb",
            "username": "baska_kullanici",
            "profilePicture": "profile-2.jpg"
          },
          "post": "60d21b4667d0d8992e610c85",
          "text": "Teşekkürler! Nereden aldın?",
          "likes": [],
          "parent": "60d21b4667d0d8992e610c85",
          "createdAt": "2023-06-21T09:15:22.142Z",
          "updatedAt": "2023-06-21T09:15:22.142Z",
          "likeCount": 0
        }
      ]
    }
  }
  ```

#### Yorum Ekleme
```
POST /api/posts/:id/comments
```
**Request Body:**
```json
{
  "text": "string",
  "parentComment": "string (opsiyonel, alt yorum için)"
}
```
**Response:**
```json
{
  "status": "success",
  "data": {
    "comment": {
      "id": "string",
      "user": {
        "id": "string",
        "username": "string",
        "profilePicture": "string"
      },
      "post": "string",
      "text": "string",
      "parent": "string or null",
      "likes": [],
      "likeCount": 0,
      "createdAt": "date"
    }
  }
}
```

### Yorum Silme
```
DELETE /api/posts/:id/comments/:commentId
```
**Response:**
```json
{
  "status": "success",
  "message": "Yorum başarıyla silindi"
}
```