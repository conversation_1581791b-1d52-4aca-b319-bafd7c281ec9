const winston = require('winston');
const AWS = require('aws-sdk');

// AWS ortamında olup olmadığımızı kontrol et
const isAWS = process.env.NODE_ENV === 'production';

// AWS CloudWatch yapılandırması
let cloudWatchLogs;
if (isAWS) {
  AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'eu-central-1'
  });
  
  cloudWatchLogs = new AWS.CloudWatchLogs();
}

// Konsol transport
const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return `${timestamp} [${level}]: ${message} ${
        Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''
      }`;
    })
  )
});

// Logger oluşturma
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'styleup-backend' },
  transports: [consoleTransport]
});

// CloudWatch log grubunu ve stream'ini oluştur (sadece AWS ortamında)
const setupCloudWatchLogs = async () => {
  if (!isAWS) return;
  
  const logGroupName = '/aws/elasticbeanstalk/styleup-backend';
  const logStreamName = `${new Date().toISOString().split('T')[0]}-${process.env.NODE_ENV}`;
  
  try {
    // Log grubunu oluştur (zaten varsa hata oluşur, görmezden gel)
    try {
      await cloudWatchLogs.createLogGroup({
        logGroupName
      }).promise();
    } catch (err) {
      if (err.code !== 'ResourceAlreadyExistsException') {
        throw err;
      }
    }
    
    // Log stream'ini oluştur (zaten varsa hata oluşur, görmezden gel)
    try {
      await cloudWatchLogs.createLogStream({
        logGroupName,
        logStreamName
      }).promise();
    } catch (err) {
      if (err.code !== 'ResourceAlreadyExistsException') {
        throw err;
      }
    }
    
    console.log('CloudWatch logs configured successfully');
  } catch (err) {
    console.error('Error setting up CloudWatch logs:', err);
  }
};

// Logger'ı dışa aktar
module.exports = {
  logger,
  setupCloudWatchLogs
}; 