/**
 * CORS (Cross-Origin Resource Sharing) yardımcı işlevleri
 * Farklı ortamlar ve farklı istemciler için CORS ayarlarını yönetir
 */

const { logger } = require('./logger');

/**
 * İstek kaynağına göre doğru CORS origin politikasını döndürür
 * @param {string} requestOrigin - İstek yapan kaynağın origin değeri
 * @param {object} allowList - İzin verilen kaynakların listesi ve ayarları
 * @returns {boolean|string} - İzin verilen origin veya false
 */
const dynamicOrigin = (requestOrigin, allowList = {}) => {
  // Eğer origin null ise (örn. REST istemcileri veya mobil uygulamalar), izin ver
  if (!requestOrigin) return true;
  
  // Eğer allowList boşsa veya * ise, tüm kaynaklara izin ver
  if (Object.keys(allowList).length === 0 || allowList['*']) return true;

  // İstek kaynağının iznini kontrol et
  if (allowList[requestOrigin]) {
    logger.info(`CORS isteği onaylandı: ${requestOrigin}`);
    return requestOrigin;
  }
  
  // Regex desenleri kontrol et
  for (const pattern in allowList) {
    if (pattern.startsWith('^') && new RegExp(pattern).test(requestOrigin)) {
      logger.info(`CORS isteği regex ile onaylandı: ${requestOrigin} -> ${pattern}`);
      return requestOrigin;
    }
  }
  
  logger.warn(`CORS isteği reddedildi: ${requestOrigin}`);
  return false;
};

/**
 * Farklı ortamlar için CORS ayarlarını oluşturur
 * @param {string} environment - Çalışma ortamı (production, development vb.)
 * @returns {Object} - CORS yapılandırma nesnesi
 */
const getCorsOptions = (environment = 'development') => {
  // Temel CORS seçenekleri
  const options = {
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'X-Requested-With', 
      'X-Device-ID', 
      'X-Device-Model', 
      'X-App-Version'
    ],
    exposedHeaders: ['Authorization'],
    maxAge: 86400,
    optionsSuccessStatus: 200
  };
  
  // Şu an tüm kaynaklara izin veriliyor
  options.origin = '*';
  
  // İleriki aşamalarda dinamik izin listesi oluşturulabilir
  // const allowList = {
  //   'https://example.com': true,
  //   'https://www.example.com': true,
  //   'http://localhost:3000': environment === 'development',
  //   '^https://.+\.yourdomain\.com$': true // Regex deseni
  // };
  // options.origin = (origin, callback) => {
  //   const allowed = dynamicOrigin(origin, allowList);
  //   callback(allowed ? null : new Error('CORS policy violation'), allowed);
  // };
  
  return options;
};

module.exports = {
  dynamicOrigin,
  getCorsOptions
}; 