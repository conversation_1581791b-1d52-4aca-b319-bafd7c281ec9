const nodemailer = require('nodemailer');

const sendEmail = async options => {
  // 1) Transporter oluştur
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    auth: {
      user: process.env.EMAIL_USERNAME,
      pass: process.env.EMAIL_PASSWORD
    }
  });

  // 2) Email seçeneklerini tanımla
  const mailOptions = {
    from: `Sosyal Medya Uygulaması <${process.env.EMAIL_FROM}>`,
    to: options.email,
    subject: options.subject,
    text: options.message,
    html: options.html
  };

  // 3) Email'i gönder
  await transporter.sendMail(mailOptions);
};

module.exports = sendEmail; 