/**
 * Uygulama genelinde kullanılan yapılandırma ayarları
 */

// API URL yapılandırması - üretimde AWS URL'si, geliştirmede localhost
const API_URL = process.env.NODE_ENV === 'production'
  ? process.env.API_URL || 'http://localhost:4000'
  : 'http://localhost:4000';

// Dosya yükleme URL'si - üretimde S3, geliştirmede yerel sunucu
const UPLOAD_BASE_URL = process.env.NODE_ENV === 'production'
  ? `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com`
  : `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com`;

module.exports = {
  API_URL,
  UPLOAD_BASE_URL
}; 