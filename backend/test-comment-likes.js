const request = require('supertest');
const app = require('./app');
const mongoose = require('mongoose');
const User = require('./models/User');
const Post = require('./models/Post');
const Comment = require('./models/Comment');

describe('Comment Likes API', () => {
  let authToken;
  let userId;
  let postId;
  let commentId;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/styleup-test');
    }
  });

  beforeEach(async () => {
    // Clean up database
    await User.deleteMany({});
    await Post.deleteMany({});
    await Comment.deleteMany({});

    // Create a test user
    const userResponse = await request(app)
      .post('/api/auth/signup/step1')
      .send({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123'
      });

    // Login to get token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    authToken = loginResponse.body.token;
    userId = loginResponse.body.data.user.id;

    // Create a test post
    const postResponse = await request(app)
      .post('/api/posts')
      .set('Authorization', `Bearer ${authToken}`)
      .field('description', 'Test post for comment likes')
      .attach('image', Buffer.from('fake image data'), 'test.jpg');

    postId = postResponse.body.data.post._id;

    // Create a test comment
    const commentResponse = await request(app)
      .post(`/api/posts/${postId}/comments`)
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        text: 'Test comment for likes'
      });

    commentId = commentResponse.body.data.comment._id;
  });

  afterAll(async () => {
    // Clean up and close connection
    await User.deleteMany({});
    await Post.deleteMany({});
    await Comment.deleteMany({});
    await mongoose.connection.close();
  });

  describe('POST /api/posts/:id/comments/:commentId/like', () => {
    it('should like a comment successfully', async () => {
      const response = await request(app)
        .post(`/api/posts/${postId}/comments/${commentId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.isLiked).toBe(true);
      expect(response.body.data.likeCount).toBe(1);
      expect(response.body.data.commentId).toBe(commentId);
    });

    it('should unlike a comment when already liked', async () => {
      // First like the comment
      await request(app)
        .post(`/api/posts/${postId}/comments/${commentId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Then unlike it
      const response = await request(app)
        .post(`/api/posts/${postId}/comments/${commentId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.isLiked).toBe(false);
      expect(response.body.data.likeCount).toBe(0);
      expect(response.body.data.commentId).toBe(commentId);
    });

    it('should return 404 for non-existent comment', async () => {
      const fakeCommentId = new mongoose.Types.ObjectId();
      
      const response = await request(app)
        .post(`/api/posts/${postId}/comments/${fakeCommentId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.status).toBe('fail');
      expect(response.body.message).toBe('Yorum bulunamadı');
    });

    it('should return 401 without authentication', async () => {
      await request(app)
        .post(`/api/posts/${postId}/comments/${commentId}/like`)
        .expect(401);
    });

    it('should update comment likes array correctly', async () => {
      // Like the comment
      await request(app)
        .post(`/api/posts/${postId}/comments/${commentId}/like`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Check the comment in database
      const comment = await Comment.findById(commentId);
      expect(comment.likes).toHaveLength(1);
      expect(comment.likes[0].toString()).toBe(userId);
      expect(comment.likeCount).toBe(1);
    });
  });
});
