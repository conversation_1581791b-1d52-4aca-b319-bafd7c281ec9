<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Sparkles</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.20620325615-63b4hamdj3acvjt8c7fisuipvtaoickm</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Fotoğraf çekmek için kameraya erişim gerekiyor</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string></string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Fotoğraflara erişmek için galeriye erişim gerekiyor</string>
	<key>UIAppFonts</key>
	<array>
		<string>Poppins-Regular.ttf</string>
		<string>Poppins-Bold.ttf</string>
		<string>Poppins-Medium.ttf</string>
		<string>Poppins-SemiBold.ttf</string>
		<string>Poppins-Light.ttf</string>
		<string>Poppins-Thin.ttf</string>
		<string>Poppins-Italic.ttf</string>
		<string>Poppins-BoldItalic.ttf</string>
		<string>Poppins-MediumItalic.ttf</string>
		<string>Poppins-SemiBoldItalic.ttf</string>
		<string>Poppins-LightItalic.ttf</string>
		<string>Poppins-ThinItalic.ttf</string>
		<string>Poppins-ExtraLight.ttf</string>
		<string>Poppins-ExtraLightItalic.ttf</string>
		<string>Poppins-ExtraBold.ttf</string>
		<string>Poppins-ExtraBoldItalic.ttf</string>
		<string>Poppins-Black.ttf</string>
		<string>Poppins-BlackItalic.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
