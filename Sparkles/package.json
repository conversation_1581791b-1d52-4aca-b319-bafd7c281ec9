{"name": "Style_Up", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/checkbox": "^0.5.17", "@react-native-google-signin/google-signin": "^13.2.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@redux-devtools/extension": "^3.3.0", "@reduxjs/toolkit": "^2.6.1", "@shopify/flash-list": "^1.7.3", "axios": "^1.8.4", "jwt-decode": "^3.1.2", "react": "18.3.1", "react-native": "0.77.0", "react-native-camera-kit": "^14.2.0", "react-native-color-matrix-image-filters": "^7.0.2", "react-native-config": "^1.5.5", "react-native-dotenv": "^3.4.11", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.23.1", "react-native-image-picker": "^8.0.0", "react-native-keyboard-accessory": "^0.1.16", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.16.7", "react-native-keyboard-spacer": "^0.4.1", "react-native-linear-gradient": "^2.8.3", "react-native-masonry-list": "^2.16.2", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.13.1", "react-native-permissions": "^5.3.0", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.6.0", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.11.1", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.4", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.0", "@react-native/eslint-config": "0.77.0", "@react-native/metro-config": "0.77.0", "@react-native/typescript-config": "0.77.0", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}