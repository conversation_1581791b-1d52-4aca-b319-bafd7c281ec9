/**
 * Font setup utility for the application
 * Sets up default font family for Text components
 */

import { Text, TextInput } from 'react-native';
import { typography } from '../theme';

/**
 * Sets up default font family for all Text components in the app
 */
export const setupFonts = () => {
  // Set default font for Text components
  Text.defaultProps = Text.defaultProps || {};
  Text.defaultProps.style = {
    ...(Text.defaultProps.style || {}),
    fontFamily: typography.fontFamily.regular,
  };

  // Set default font for TextInput components
  TextInput.defaultProps = TextInput.defaultProps || {};
  TextInput.defaultProps.style = {
    ...(TextInput.defaultProps.style || {}),
    fontFamily: typography.fontFamily.regular,
  };
};

export default setupFonts;
