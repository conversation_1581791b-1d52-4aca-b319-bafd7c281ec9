import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearReadMessages } from './readMessageStorage';
import { clearApiCache } from '../api/api';
import { clearImageCache } from './imagePrefetch';

/**
 * Comprehensive function to clear all user-specific data from AsyncStorage
 * This should be called when a user logs out to ensure no data persists
 * from the previous user session
 */
export const clearAllUserData = async () => {
  try {
    console.log('[CLEAR USER DATA] Starting comprehensive data clearing...');

    // Get all AsyncStorage keys first to identify any persist keys
    const allKeys = await AsyncStorage.getAllKeys();
    console.log('[CLEAR USER DATA] All AsyncStorage keys before clearing:', allKeys);

    // List of all known AsyncStorage keys that should be cleared on logout
    const keysToRemove = [
      // Authentication tokens
      'token',
      'refreshToken',

      // User session data
      'isLoggedIn',
      'isRegistered',

      // User preferences and cached data
      'currentBackgroundIndex',
      'savedUsers',

      // Redux persist keys (if any)
      'persist:root',
      'persist:auth',
      'persist:user',
      'persist:posts',
      'persist:conversations',
      'persist:messages',

      // Any other user-specific keys that might exist
      // Add more keys here as needed when new features are added
    ];

    // Also add any keys that start with 'persist:' that we might have missed
    const persistKeys = allKeys.filter(key => key.startsWith('persist:'));
    keysToRemove.push(...persistKeys);

    // Remove all specified keys
    await AsyncStorage.multiRemove(keysToRemove);
    console.log('[CLEAR USER DATA] Removed keys:', keysToRemove);

    // Clear read messages using the dedicated utility
    await clearReadMessages();

    // Clear API cache to prevent serving cached data from previous user
    console.log('[CLEAR USER DATA] Clearing API cache...');
    clearApiCache();

    // Clear image cache to prevent showing cached images from previous user
    console.log('[CLEAR USER DATA] Clearing image cache...');
    clearImageCache();

    // Get all remaining keys to check for any user-specific data we might have missed
    const remainingKeys = await AsyncStorage.getAllKeys();
    console.log('[CLEAR USER DATA] Remaining AsyncStorage keys after cleanup:', remainingKeys);

    // Optional: Clear ALL AsyncStorage data (use with caution)
    // This is commented out by default to preserve app-level settings
    // Uncomment only if you want to clear absolutely everything
    // await AsyncStorage.clear();
    // console.log('[CLEAR USER DATA] Cleared ALL AsyncStorage data');

    console.log('[CLEAR USER DATA] User data clearing completed successfully');
    return true;
  } catch (error) {
    console.error('[CLEAR USER DATA] Error clearing user data:', error);
    throw error;
  }
};

/**
 * Clear only authentication-related data
 * This is a lighter version that only clears tokens and auth state
 */
export const clearAuthData = async () => {
  try {
    console.log('[CLEAR AUTH DATA] Clearing authentication data...');

    const authKeys = ['token', 'refreshToken', 'isLoggedIn'];
    await AsyncStorage.multiRemove(authKeys);

    console.log('[CLEAR AUTH DATA] Authentication data cleared');
    return true;
  } catch (error) {
    console.error('[CLEAR AUTH DATA] Error clearing auth data:', error);
    throw error;
  }
};

/**
 * Clear user preferences and cached data (but keep auth tokens)
 * Useful for resetting user preferences without logging out
 */
export const clearUserPreferences = async () => {
  try {
    console.log('[CLEAR USER PREFERENCES] Clearing user preferences...');

    const preferenceKeys = [
      'currentBackgroundIndex',
      'savedUsers',
    ];

    await AsyncStorage.multiRemove(preferenceKeys);
    await clearReadMessages();

    console.log('[CLEAR USER PREFERENCES] User preferences cleared');
    return true;
  } catch (error) {
    console.error('[CLEAR USER PREFERENCES] Error clearing preferences:', error);
    throw error;
  }
};
