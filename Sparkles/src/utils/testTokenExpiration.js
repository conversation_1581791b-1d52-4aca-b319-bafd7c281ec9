import AsyncStorage from '@react-native-async-storage/async-storage';
import { isTokenExpired, isTokenExpiringSoon, decodeJWTPayload } from './tokenUtils';

/**
 * Test utility to simulate token expiration scenarios
 * This is for development/testing purposes only
 */

// Create a mock expired JWT token for testing
const createMockExpiredToken = () => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    id: 'test-user-id',
    exp: Math.floor(Date.now() / 1000) - 3600, // Expired 1 hour ago
    iat: Math.floor(Date.now() / 1000) - 7200  // Issued 2 hours ago
  }));
  const signature = 'mock-signature';
  
  return `${header}.${payload}.${signature}`;
};

// Create a mock token that expires soon
const createMockExpiringSoonToken = () => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    id: 'test-user-id',
    exp: Math.floor(Date.now() / 1000) + 120, // Expires in 2 minutes
    iat: Math.floor(Date.now() / 1000) - 3600  // Issued 1 hour ago
  }));
  const signature = 'mock-signature';
  
  return `${header}.${payload}.${signature}`;
};

// Create a mock valid token
const createMockValidToken = () => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    id: 'test-user-id',
    exp: Math.floor(Date.now() / 1000) + 3600, // Expires in 1 hour
    iat: Math.floor(Date.now() / 1000) - 3600   // Issued 1 hour ago
  }));
  const signature = 'mock-signature';
  
  return `${header}.${payload}.${signature}`;
};

/**
 * Test token expiration detection
 */
export const testTokenExpiration = () => {
  console.log('=== Testing Token Expiration Detection ===');
  
  const expiredToken = createMockExpiredToken();
  const expiringSoonToken = createMockExpiringSoonToken();
  const validToken = createMockValidToken();
  
  console.log('Expired token test:', isTokenExpired(expiredToken)); // Should be true
  console.log('Expiring soon token test:', isTokenExpiringSoon(expiringSoonToken)); // Should be true
  console.log('Valid token test:', isTokenExpired(validToken)); // Should be false
  
  console.log('Expired token payload:', decodeJWTPayload(expiredToken));
  console.log('Valid token payload:', decodeJWTPayload(validToken));
};

/**
 * Simulate expired token scenario for testing
 */
export const simulateExpiredTokenScenario = async () => {
  console.log('=== Simulating Expired Token Scenario ===');
  
  const expiredToken = createMockExpiredToken();
  const expiredRefreshToken = createMockExpiredToken();
  
  // Store expired tokens
  await AsyncStorage.multiSet([
    ['token', expiredToken],
    ['refreshToken', expiredRefreshToken],
    ['isLoggedIn', 'true']
  ]);
  
  console.log('Stored expired tokens for testing');
  console.log('You can now test the app behavior with expired tokens');
  console.log('The app should automatically logout and redirect to login screen');
};

/**
 * Simulate expiring soon token scenario for testing
 */
export const simulateExpiringSoonTokenScenario = async () => {
  console.log('=== Simulating Expiring Soon Token Scenario ===');
  
  const expiringSoonToken = createMockExpiringSoonToken();
  const validRefreshToken = createMockValidToken();
  
  // Store tokens where access token is expiring soon but refresh token is valid
  await AsyncStorage.multiSet([
    ['token', expiringSoonToken],
    ['refreshToken', validRefreshToken],
    ['isLoggedIn', 'true']
  ]);
  
  console.log('Stored expiring soon tokens for testing');
  console.log('The app should proactively refresh the token');
};

/**
 * Reset to normal tokens for testing
 */
export const resetToValidTokens = async () => {
  console.log('=== Resetting to Valid Tokens ===');
  
  const validToken = createMockValidToken();
  const validRefreshToken = createMockValidToken();
  
  await AsyncStorage.multiSet([
    ['token', validToken],
    ['refreshToken', validRefreshToken],
    ['isLoggedIn', 'true']
  ]);
  
  console.log('Reset to valid tokens');
};

/**
 * Clear all tokens for testing
 */
export const clearAllTokensForTesting = async () => {
  console.log('=== Clearing All Tokens ===');
  
  await AsyncStorage.multiRemove(['token', 'refreshToken', 'isLoggedIn']);
  
  console.log('All tokens cleared');
};
