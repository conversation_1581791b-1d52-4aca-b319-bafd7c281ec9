import FastImage from 'react-native-fast-image';

/**
 * Prefetches a list of images using FastImage prefetch
 * @param {Array<string>} imageUrls - Array of image URLs to prefetch
 * @param {string} priority - Priority for FastImage prefetch ('low', 'normal', 'high')
 */
export const prefetchImages = (imageUrls, priority = 'normal') => {
  if (!imageUrls || !Array.isArray(imageUrls) || imageUrls.length === 0) {
    return;
  }

  // Filter valid URLs
  const validUrls = imageUrls
    .filter(url => typeof url === 'string' && url.startsWith('http'));

  if (validUrls.length > 0) {
    // Convert priority string to FastImage priority
    const fastImagePriority =
      priority === 'high' ? FastImage.priority.high :
      priority === 'low' ? FastImage.priority.low :
      FastImage.priority.normal;

    // Create source objects for FastImage
    const sources = validUrls.map(uri => ({
      uri,
      priority: fastImagePriority,
    }));

    // Use FastImage prefetch
    FastImage.preload(sources);
  }
};

/**
 * Clears the FastImage cache
 */
export const clearImageCache = () => {
  FastImage.clearMemoryCache();
  FastImage.clearDiskCache();
};

/**
 * Clears the FastImage cache for a specific profile picture
 * This is useful when a user updates their profile picture
 * @param {string} profilePictureUrl - The URL of the profile picture to clear from cache
 */
export const clearProfilePictureCache = (profilePictureUrl) => {
  if (!profilePictureUrl || typeof profilePictureUrl !== 'string') {
    return;
  }

  console.log('[IMAGE CACHE] Clearing profile picture cache for:', profilePictureUrl);

  // Clear the entire cache since FastImage doesn't support clearing specific URLs
  // This ensures the updated profile picture will be fetched fresh
  FastImage.clearMemoryCache();
  FastImage.clearDiskCache();

  console.log('[IMAGE CACHE] Profile picture cache cleared');
};
