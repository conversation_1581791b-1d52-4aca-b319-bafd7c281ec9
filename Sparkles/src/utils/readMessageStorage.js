import AsyncStorage from '@react-native-async-storage/async-storage';

// Key for storing read message IDs in AsyncStorage
const READ_MESSAGES_KEY = 'sparkles_read_messages';

/**
 * Get all read message IDs from AsyncStorage
 * @returns {Promise<Object>} Object with message IDs as keys and true as values
 */
export const getReadMessages = async () => {
  try {
    const readMessagesJson = await AsyncStorage.getItem(READ_MESSAGES_KEY);
    return readMessagesJson ? JSON.parse(readMessagesJson) : {};
  } catch (error) {
    console.error('Error getting read messages from storage:', error);
    return {};
  }
};

/**
 * Mark a message as read in AsyncStorage
 * @param {string} messageId - ID of the message to mark as read
 * @returns {Promise<void>}
 */
export const markMessageAsRead = async (messageId) => {
  try {
    if (!messageId) return;
    
    // Get current read messages
    const readMessages = await getReadMessages();
    
    // Add the new message ID
    readMessages[messageId] = true;
    
    // Save back to AsyncStorage
    await AsyncStorage.setItem(READ_MESSAGES_KEY, JSON.stringify(readMessages));
    
    console.log(`Message ${messageId} marked as read in storage`);
  } catch (error) {
    console.error('Error marking message as read in storage:', error);
  }
};

/**
 * Check if a message is read
 * @param {string} messageId - ID of the message to check
 * @returns {Promise<boolean>} True if the message is read, false otherwise
 */
export const isMessageRead = async (messageId) => {
  try {
    if (!messageId) return false;
    
    const readMessages = await getReadMessages();
    return !!readMessages[messageId];
  } catch (error) {
    console.error('Error checking if message is read:', error);
    return false;
  }
};

/**
 * Clear all read message data
 * @returns {Promise<void>}
 */
export const clearReadMessages = async () => {
  try {
    await AsyncStorage.removeItem(READ_MESSAGES_KEY);
    console.log('All read message data cleared');
  } catch (error) {
    console.error('Error clearing read messages:', error);
  }
};
