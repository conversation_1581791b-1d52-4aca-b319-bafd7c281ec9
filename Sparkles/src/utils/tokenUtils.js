import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Decode JWT token payload without verification
 * @param {string} token - JWT token
 * @returns {object|null} - Decoded payload or null if invalid
 */
export const decodeJWTPayload = (token) => {
  try {
    if (!token) return null;
    
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = parts[1];
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
    return decoded;
  } catch (error) {
    console.error('Error decoding JWT payload:', error);
    return null;
  }
};

/**
 * Check if JWT token is expired
 * @param {string} token - JWT token
 * @returns {boolean} - True if token is expired
 */
export const isTokenExpired = (token) => {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    const expirationTime = payload.exp;
    
    // Add a 30-second buffer to account for network delays
    const buffer = 30;
    return currentTime >= (expirationTime - buffer);
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

/**
 * Check if token will expire soon (within next 5 minutes)
 * @param {string} token - JWT token
 * @returns {boolean} - True if token will expire soon
 */
export const isTokenExpiringSoon = (token) => {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    const expirationTime = payload.exp;
    const fiveMinutes = 5 * 60; // 5 minutes in seconds
    
    return currentTime >= (expirationTime - fiveMinutes);
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
};

/**
 * Get token expiration time in milliseconds
 * @param {string} token - JWT token
 * @returns {number|null} - Expiration time in milliseconds or null if invalid
 */
export const getTokenExpirationTime = (token) => {
  try {
    const payload = decodeJWTPayload(token);
    if (!payload || !payload.exp) return null;
    
    return payload.exp * 1000; // Convert to milliseconds
  } catch (error) {
    console.error('Error getting token expiration time:', error);
    return null;
  }
};

/**
 * Check if stored tokens are valid and not expired
 * @returns {Promise<{hasValidToken: boolean, hasValidRefreshToken: boolean}>}
 */
export const checkStoredTokensValidity = async () => {
  try {
    const token = await AsyncStorage.getItem('token');
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    
    const hasValidToken = token && !isTokenExpired(token);
    const hasValidRefreshToken = refreshToken && !isTokenExpired(refreshToken);
    
    return {
      hasValidToken,
      hasValidRefreshToken
    };
  } catch (error) {
    console.error('Error checking stored tokens validity:', error);
    return {
      hasValidToken: false,
      hasValidRefreshToken: false
    };
  }
};

/**
 * Clear expired tokens from storage
 * @returns {Promise<boolean>} - True if any tokens were cleared
 */
export const clearExpiredTokens = async () => {
  try {
    const token = await AsyncStorage.getItem('token');
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    
    const tokensToRemove = [];
    
    if (token && isTokenExpired(token)) {
      tokensToRemove.push('token');
      console.log('[TOKEN UTILS] Clearing expired access token');
    }
    
    if (refreshToken && isTokenExpired(refreshToken)) {
      tokensToRemove.push('refreshToken');
      console.log('[TOKEN UTILS] Clearing expired refresh token');
    }
    
    if (tokensToRemove.length > 0) {
      await AsyncStorage.multiRemove(tokensToRemove);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error clearing expired tokens:', error);
    return false;
  }
};
