import {View, Image, TouchableOpacity, Text, StyleSheet, Alert} from 'react-native';
import SvgBack from '../../assets/back';
import {ScrollView, TextInput} from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { useCallback, useEffect, useRef, useState } from 'react';
import { addPost } from '../../redux/slices/userSlice';
import { height } from '../../utils/helpers';
import CategorySearchComponent from '../../components/Post/CategorySearchComponent';
import { clearSelectedBrands } from '../../redux/slices/brandSlice';
import { clearSelectedColors } from '../../redux/slices/colorSlice';
import {  setPoll, setTitle,  updateDesc,
  insertTag,
  insertMention,
  deleteTag,
  deleteMention,
  setMentions,
  setTags,
  addMention,
  addTag,
  resetPostDetail} from '../../redux/slices/postDetailSlice';
import { setPhotoUri, setFilterName } from '../../redux/slices/photoSlice';
  import { debounce } from 'lodash'
import { createPost } from '../../redux/actions/postCreateActions';
import { selectCategoryData, selectSelectedCategories, toggleCategory } from '../../redux/slices/categoriesSlice';
import store from '../../redux/store';
import { resetProductLinks } from '../../redux/slices/productLinkSlice';

const PostConfirmScreen = ({route, navigation}) => {
  const dispatch = useDispatch();
  const photoUri = useSelector(state => state.photo.photoUri);
  const filterName = useSelector(state => state.photo.filterName);
  const selectedBrands = useSelector(state => state.brands.selectedBrands);
  const selectedColors = useSelector(state => state.colors.selectedColors)
  const productLink = useSelector((state) => state.productLink.productLink);
  const descriptionInputRef = useRef(null);
  const [description, setDescription] = useState('');
  const postData = useSelector(state => state.postDetail.postData);
  const [titleInput, setTitleInput] = useState('');
  const { currentProduct, clothingLinks } = useSelector(state => state.productLink);

  // Seçili markaları console'a yazdır
  useEffect(() => {
    console.log("Seçilen markalar, renkler:, link", productLink, selectedBrands, selectedColors);
  }, [selectedBrands,selectedColors, productLink]);
   // Redux state'ini konsola yazdırma fonksiyonu
   useEffect(() => {
    console.log('--- REDUX STATE UPDATE ---');
    console.log('Title:', postData.title);
    console.log('Description:', postData.description);
    console.log('Tags:', postData.tags);
    console.log('Mentions:', postData.mentions);
    console.log('Poll:', postData.poll);
    console.log('-------------------------');
  }, [postData])


  // Log when photoUri changes
  useEffect(() => {
    console.log("PostConfirmScreen - photoUri changed:", photoUri);
    console.log("PostConfirmScreen - filterName:", filterName);
  }, [photoUri, filterName]);

  useEffect(() => {
    return () => {
      // Clean up all post-related state when component unmounts
      dispatch(clearSelectedBrands());
      dispatch(clearSelectedColors());
      dispatch(resetPostDetail());
      dispatch(resetProductLinks());
      dispatch(setPhotoUri(null));
    };
  }, [dispatch]);

//   const handlePost = () => {
//     const newPost = {
//       id: Date.now(),
//     photo: "https://via.placeholder.com/150",
//     title: "Yeni Post",
//     description: "Bu bir test gönderisidir.",
//     category: { name: "ceket", brand: "Apple", color: "Siyah" },
//     poll: null,
//     tags: ["React", "Redux"],
//     mentions: ["@kullanici"],
//     };
//     console.log("Post dispatch ediliyor:", newPost);
// dispatch(addPost(newPost));

//     // setPhotoUri(null);
//     setTitle('');
//     setDescription('');
//     setPoll('');
//     setHashtags('');
//     setMentions('');
//     setCategory('');

//     navigation.navigate('Home');
//   };

const closeScreen = () => {
    // Clear all post-related state in Redux
    dispatch(resetPostDetail());
    dispatch(clearSelectedBrands());
    dispatch(clearSelectedColors());
    dispatch(resetProductLinks());
    // Clear the photo URI in Redux
    dispatch(setPhotoUri(null));
    navigation.goBack();
  };




  // const [descriptionInput, setDescriptionInput] = useState('');

  // Başlık değiştiğinde
  const handleTitleChange = (text) => {
    console.log('Başlık değişti:', text);
    setTitleInput(text);
    dispatch(setTitle(text));
  };


  // Anket ekleme
  const handleAddPoll = () => {
    const newPoll = {
      question: 'Yeni anket',
      options: ['Seçenek 1', 'Seçenek 2']
    };
    console.log('Anket eklendi:', newPoll);
    dispatch(setPoll(newPoll));
  };





     // Etiket ekleme fonksiyonu (YENİ İSİM)
  const handleAddTag = () => {
    const newText = description + ' #';
    setDescription(newText);
    dispatch(updateDesc(newText));
    descriptionInputRef.current.focus();
  };

  // Bahset ekleme fonksiyonu (YENİ İSİM)
  const handleAddMention = () => {
    const newText = description + ' @';
    setDescription(newText);
    dispatch(updateDesc(newText));
    descriptionInputRef.current.focus();
  };



  const handleDescriptionChange = (text) => {
    setDescription(text);
    dispatch(updateDesc(text));
  };
  const { tags } = useSelector(state => state.postDetail.postData);

  // const handleSharePost = async () => {
  //   try {
  //     const postData = new FormData();

  //     // Resim ekleme
  //     if (photoUri) {
  //       postData.append('postImage', {
  //         uri: photoUri,
  //         name: 'post_image.jpg',
  //         type: 'image/jpeg'
  //       });
  //     }

  //     // Diğer alanlar
  //     postData.append('description', description || '');

  //     if (tags?.length) {
  //       postData.append('tags', tags.join(','));
  //     }

  //     // 1. currentProduct kontrolü
  //     console.log("Current Product:", JSON.stringify(currentProduct, null, 2));

  //     // 2. clothingLinks kontrolü
  //     console.log("All Clothing Links:", JSON.stringify(clothingLinks, null, 2));

  //     // 3. clothingLinks verisi ekleme
  //     if (currentProduct.url) {
  //       postData.append('clothingLinks', JSON.stringify([{
  //         url: currentProduct.url,
  //         title: currentProduct.title || 'Ürün Başlığı',
  //         type: currentProduct.type || 'product',
  //         price: currentProduct.price,
  //         brand: currentProduct.brand,
  //         yakaType: currentProduct.yakaType,
  //         color: currentProduct.color
  //       }]));
  //       console.log("Added currentProduct as clothingLink");
  //     } else if (clothingLinks.length > 0) {
  //       postData.append('clothingLinks', JSON.stringify(clothingLinks));
  //       console.log("Added all clothingLinks:", clothingLinks.length);
  //     } else {
  //       console.warn("No product data - clothingLinks will be empty.");
  //     }

  //     // Ek alanlar
  //     if (selectedBrands && selectedBrands.length > 0) {
  //       postData.append('brands', selectedBrands.join(','));
  //     }

  //     if (selectedColors && selectedColors.length > 0) {
  //       postData.append('colors', selectedColors.join(','));
  //     }

  //     // Debug için
  //     console.log('FormData contents:');
  //     for (let [key, value] of postData._parts) {
  //       console.log(key, value);
  //     }

  //     // Post isteği gönderme
  //     await dispatch(createPost(postData)).unwrap();
  //     Alert.alert('Success', 'Post shared successfully!');

  //   } catch (error) {
  //     console.error('Post creation failed:', error);
  //     Alert.alert('Error', 'Failed to share post: ' + error.message);
  //   }
  // };

  const handleSharePost = async () => {
    try {
      const postData = new FormData();

      // Resim ekleme
      if (photoUri) {
        postData.append('postImage', {
          uri: photoUri,
          name: 'post_image.jpg',
          type: 'image/jpeg'
        });
      }

      // Diğer alanlar
      postData.append('description', description || '');

      if (tags?.length) {
        postData.append('tags', tags.join(','));
      }

      // Redux state'ini almak için dispatch kullanımı
      const state = store.getState(); // Redux store'una erişim
      const categoryData = selectCategoryData(state);
      const selectedCategories = selectSelectedCategories(state);

      // Kategori verilerini işle
      const processedClothingLinks = selectedCategories.map(category => ({
        type: category,
        url: categoryData[category]?.url || '',
        title: `${category} Ürünü`,
        price: 0, // Fiyat bilgisi yoksa 0 olarak ayarla
        brand: categoryData[category]?.brand || '',
        yakaType: categoryData[category]?.collarType || '',
        color: categoryData[category]?.color || ''
      }));

      // Debug logları
      console.log("Processed Clothing Links:", JSON.stringify(processedClothingLinks, null, 2));

      // Kategori verilerini ekle
      if (processedClothingLinks.length > 0) {
        postData.append('clothingLinks', JSON.stringify(processedClothingLinks));
        console.log("Added processed clothingLinks:", processedClothingLinks.length);
      } else {
        console.warn("No category data - clothingLinks will be empty.");
      }

      // Ek alanlar
      if (selectedBrands && selectedBrands.length > 0) {
        postData.append('brands', selectedBrands.join(','));
      }

      if (selectedColors && selectedColors.length > 0) {
        postData.append('colors', selectedColors.join(','));
      }

      // Kategorileri de gönder (opsiyonel)
      postData.append('categories', selectedCategories.join(','));

      // Add filter information
      postData.append('filter', filterName);

      // Debug için
      console.log('FormData contents:');
      for (let [key, value] of postData._parts) {
        console.log(key, value);
      }

      // Post isteği gönderme
      await dispatch(createPost(postData)).unwrap();
      Alert.alert('Başarılı', 'Gönderi paylaşıldı!');

      // Başarılı olursa verileri temizle
      dispatch(resetProductLinks());
      dispatch(resetPostDetail());
      dispatch(clearSelectedBrands());
      dispatch(clearSelectedColors());
      dispatch(setPhotoUri(null));
      selectedCategories.forEach(category => {
        dispatch(toggleCategory(category));
      });

      // Navigate back to home screen with reset param to ensure PostScreen is reset
      // This will help ensure the PostScreen is completely reset when accessed again
      navigation.reset({
        index: 0,
        routes: [{ name: 'Main' }],
      });

    } catch (error) {
      console.error('Gönderi oluşturma hatası:', error);
      Alert.alert('Hata', 'Gönderi paylaşımı başarısız: ' + error.message);
    }
  };

  console.log('Debugging data:');
console.log('photoUri:', photoUri);
console.log('photoUri type:', typeof photoUri);
console.log('photoUri length:', photoUri ? photoUri.length : 0);
console.log('filterName:', filterName);
console.log('description:', description);
console.log('tags:', tags);
console.log('productLink:', productLink);
console.log('selectedBrands:', selectedBrands);
console.log('selectedColors:', selectedColors);




  return (
    <ScrollView style={{backgroundColor: '#FFFFFF', flex: 1}}>
      <TouchableOpacity onPress={closeScreen} style={styles.closeButton}>
        <SvgBack />
      </TouchableOpacity>

      <View style={styles.intContainer}>
        {photoUri ? (
          <Image
            source={{uri: photoUri}}
            style={styles.image}
            onError={(error) => console.error("Image loading error in PostConfirmScreen:", error.nativeEvent.error)}
          />
        ) : (
          <View style={[styles.image, {backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center'}]}>
            <Text style={{color: '#999'}}>No image available</Text>
          </View>
        )}

        <View style={styles.inputContainer}>
          <View>
            <TextInput
              style={styles.firstInput}
              placeholder="Bir başlık ekleyin"
              placeholderTextColor="#000000"
              value={titleInput}
              onChangeText={handleTitleChange}
               selectionColor="#D134AA"
            />

             <TextInput
              ref={descriptionInputRef}
              style={styles.secondInput}
              placeholderTextColor="#9D9C9C"
              multiline={true}
              numberOfLines={3}
              placeholder="Yarattığın tarz hakkında bir açıklama yaz veya anket ekle"
              value={description}
              onChangeText={handleDescriptionChange}
               selectionColor="#D134AA"
            />
          </View>
          <View style={styles.buttonContainer}>
            <TouchableOpacity activeOpacity={0.7} onPress={handleAddPoll}>
              <Text style={styles.button}>Anket</Text>
            </TouchableOpacity>
            <TouchableOpacity activeOpacity={0.7} onPress={handleAddTag}>
              <Text style={styles.button}>#Etiket</Text>
            </TouchableOpacity>
            <TouchableOpacity activeOpacity={0.7} onPress={handleAddMention}>
              <Text style={styles.button}>@Bahset</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <View style={styles.border} />


      <CategorySearchComponent/>

      <TouchableOpacity  style={styles.shareBtn} onPress={handleSharePost}>
          <Text style={styles.shareText}>Paylaş</Text>
        </TouchableOpacity>



    </ScrollView>
  );
};

const styles = StyleSheet.create({
  closeButton: {
    marginHorizontal: 20,
    marginBottom: 20,
    marginTop: 60,
  },
  image: {
    width: 105,
    height: 150,
    resizeMode: 'contain',
    aspectRatio:0.7,
    borderRadius: 4,
  },
  intContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    gap: 20,
  },
  border: {
    width: '100%',
    height: 1,
    backgroundColor: '#F1F1F1',
    marginTop: 45,
  },
  inputContainer: {
    marginRight: 20,
    justifyContent: 'space-between',
  },
  firstInput: {
    fontSize: 16,
    fontWeight: '600',
  },
  secondInput: {
    height: 70,
    marginRight: 20,
    width: 230,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  button: {
    backgroundColor: '#000000',
    color: 'white',
    width: 65,
    height: 22,
    textAlign: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    lineHeight: 22,
    borderRadius: 4,
    fontSize: 11,
    fontWeight: '500',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 35,
    paddingHorizontal: 10,
    height: 40,
    width: 353,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 23,
    marginTop: 15,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 5,
  },
  searchIcon: {
    width: 20,
    height: 20,
  },
  searchInput: {
    flex: 1,
    fontSize: 13,
    paddingLeft: 5,
  },
  shareBtn: {
    backgroundColor: "#000000",
    height: height * 0.05,
    // marginHorizontal: 23,
    // borderRadius: 4,
    marginTop: 10,
    justifyContent: "center",
    alignItems: "center",
  },

  shareText: {
    color: "#FFFFFF",
    textAlign: "center",
    fontSize: 16,
  },
});

export default PostConfirmScreen;
