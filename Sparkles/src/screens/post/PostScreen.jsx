import React, { useState, useRef, useEffect } from "react";
import { View, TouchableOpacity, Text, StyleSheet, Image, Platform, PermissionsAndroid, FlatList, ActivityIndicator } from "react-native";
import { Camera, CameraType } from "react-native-camera-kit";
import { useFocusEffect } from "@react-navigation/native";
import SvgFlash from "../../assets/flash";
import SvgTurnCam from "../../assets/turnCam";
import SvgTimer from "../../assets/timer";
import SvgTakePhoto from "../../assets/takePhoto";
import { launchImageLibrary } from "react-native-image-picker";
import SvgClose from "../../assets/closeLight";
import SvgFilter from "../../assets/filterPhoto";
import SvgNext from "../../assets/next";
import RNFS from "react-native-fs";
import { check, request, PERMISSIONS, RESULTS } from "react-native-permissions";
import { height } from "../../utils/helpers";
// Import filter components from react-native-color-matrix-image-filters
import {
  Grayscale,
  Sepia,
  Saturate,
  Brightness,
  Invert,
  Normal,
  ColorMatrix,
  Vintage,
  Polaroid,
  Warm,
  Cool,
  Night,
  LSD,
  // concatColorMatrices, // Unused import
  saturate,
  brightness,
  invert,
  sepia,
  grayscale,
  vintage,
  polaroid,
  warm,
  cool,
  night,
  lsd
} from 'react-native-color-matrix-image-filters';

// Check if filter components are properly imported
console.log("Filter components check:", {
  Grayscale: !!Grayscale,
  Sepia: !!Sepia,
  Saturate: !!Saturate,
  Brightness: !!Brightness,
  Invert: !!Invert,
  Normal: !!Normal,
  ColorMatrix: !!ColorMatrix,
  Vintage: !!Vintage,
  Polaroid: !!Polaroid,
  Warm: !!Warm,
  Cool: !!Cool,
  Night: !!Night,
  LSD: !!LSD
});
import { useDispatch } from "react-redux";
import { setPhotoUri, setFilterName } from "../../redux/slices/photoSlice";
import ViewShot from "react-native-view-shot";

// Check if ViewShot is properly imported
console.log("ViewShot component check:", !!ViewShot);


const PostScreen = ({ navigation }) => {
  const [cameraType, setCameraType] = useState(CameraType.Back);
  const [capturedPhoto, setCapturedPhoto] = useState(null);
  const [timer, setTimer] = useState(0);
  const [showTimerOptions, setShowTimerOptions] = useState(false);
  const [countdown, setCountdown] = useState(null);
  const [torchMode, setTorchMode] = useState("off");
  const [lastGalleryPhoto, setLastGalleryPhoto] = useState(null);
  const [filterActive, setFilterActive] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState(0); // Default to first filter (No Filter)
  const [isCapturingFilteredImage, setIsCapturingFilteredImage] = useState(false);
  const cameraRef = useRef(null);
  const viewShotRef = useRef(null);
  const dispatch = useDispatch();

  const goToPostConfirm = async () => {
    // Save the selected filter name to Redux
    const selectedFilterName = filters[selectedFilter].name;
    dispatch(setFilterName(selectedFilterName));
    console.log("Saving filter name:", selectedFilterName);

    if (selectedFilter === 0) {
      // If no filter is applied, just use the original photo
      console.log("No filter selected, using original photo");
      dispatch(setPhotoUri(capturedPhoto));
      navigation.navigate("PostConfirm");
      return;
    }

    // Show loading indicator
    setIsCapturingFilteredImage(true);

    try {
      // Check if viewShotRef is properly initialized
      if (!viewShotRef || !viewShotRef.current) {
        console.error("ViewShot reference is not available");
        throw new Error("ViewShot reference is not available");
      }

      console.log("Attempting to capture filtered image...");

      // Add a small delay to ensure the ViewShot component is fully rendered
      await new Promise(resolve => setTimeout(resolve, 300));

      // Capture the filtered image view
      const filteredImageUri = await viewShotRef.current.capture();
      console.log("Captured filtered image URI:", filteredImageUri);
      console.log("Filtered image URI type:", typeof filteredImageUri);
      console.log("Filtered image URI length:", filteredImageUri ? filteredImageUri.length : 0);

      if (!filteredImageUri) {
        console.error("Filtered image URI is empty or undefined");
        throw new Error("Failed to capture filtered image");
      }

      // Save the filtered image URI to Redux
      dispatch(setPhotoUri(filteredImageUri));
      console.log("Dispatched filtered image URI to Redux");

      // Navigate to the confirmation screen
      navigation.navigate("PostConfirm");
    } catch (error) {
      console.error("Error capturing filtered image:", error);
      console.error("Error stack:", error.stack);

      // Fallback to original image if capture fails
      console.log("Falling back to original image");
      dispatch(setPhotoUri(capturedPhoto));
      navigation.navigate("PostConfirm");
    } finally {
      setIsCapturingFilteredImage(false);
    }
  };


  const toggleCameraType = () => {
    setCameraType((prevType) =>
      prevType === CameraType.Back ? CameraType.Front : CameraType.Back
    );
  };

  const toggleFlash = () => {
    setTorchMode((flashMode) => (flashMode === "off" ? "on" : "off"));
  };

  const takePicture = async () => {
    if (timer > 0) {
      let count = timer;
      const interval = setInterval(() => {
        if (count === 1) {
          clearInterval(interval);
          capturePhoto();
        }
        count--;
      }, 1000);
    } else {
      capturePhoto();
      setCountdown(null)
    }
  };

  const capturePhoto = async () => {
    try {
      if (cameraRef.current) {
        console.log("Capturing photo...");
        const photo = await cameraRef.current.capture();
        console.log("Photo captured:", photo);

        if (photo && photo.uri) {
          console.log("Setting captured photo URI:", photo.uri);
          setCapturedPhoto(photo.uri);

          // Automatically show filter options when a photo is captured with camera
          setFilterActive(true);
        } else {
          console.error("Invalid photo object returned from camera:", photo);
        }
      } else {
        console.error("Camera reference is not available");
      }
    } catch (error) {
      console.error("Error capturing photo:", error);
    }
  };

  const startCountdown = () => {
    if (timer > 0) {
      setCountdown(timer);  // Başlangıçta countdown'u başlatıyoruz

      // Sayaç zamanlayıcısını başlatıyoruz
      const interval = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(interval); // Sayaç sıfırlanana kadar interval'ı temizle
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      // Fotoğrafı çekmek için timer süresi kadar bekliyoruz
      setTimeout(() => {
        takePicture(); // Timer süresi bitince fotoğraf çek
      }, timer * 1000); // Timer süresi (saniye) kadar bekleyip fotoğraf çek
    } else {
      takePicture(); // Timer 0 ise fotoğrafı hemen çek
    }
  };

  // Check camera permissions
  const checkCameraPermission = async () => {
    try {
      if (Platform.OS === "android") {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: "Camera Permission",
            message: "App needs camera permission to take pictures.",
            buttonNeutral: "Ask Me Later",
            buttonNegative: "Cancel",
            buttonPositive: "OK"
          }
        );
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          console.log("Camera permission denied");
        }
      } else {
        const permission = await check(PERMISSIONS.IOS.CAMERA);
        if (permission !== RESULTS.GRANTED) {
          await request(PERMISSIONS.IOS.CAMERA);
        }
      }
    } catch (error) {
      console.error("Error checking camera permission:", error);
    }
  };

  // Initialize component
  useEffect(() => {
    setCapturedPhoto(null);
    checkCameraPermission();
  }, []);

  // Reset all state when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      console.log("PostScreen focused - resetting state");
      // Reset all state variables
      setCapturedPhoto(null);
      setTimer(0);
      setShowTimerOptions(false);
      setCountdown(null);
      setFilterActive(false);
      setSelectedFilter(0);
      setIsCapturingFilteredImage(false);

      // Reset camera type to back camera
      setCameraType(CameraType.Back);

      // Reset torch mode to off
      setTorchMode("off");

      // Clear the photo URI in Redux
      dispatch(setPhotoUri(null));

      // Check camera permissions again
      checkCameraPermission();

      return () => {
        // Cleanup function if needed
      };
    }, [dispatch])
  );


  useEffect(() => {
    const getLastPhoto = async () => {
      try {
        if (Platform.OS === "android") {
          const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
          if (granted !== PermissionsAndroid.RESULTS.GRANTED) return;
        } else {
          const permission = await check(PERMISSIONS.IOS.PHOTO_LIBRARY);
          if (permission !== RESULTS.GRANTED) {
            await request(PERMISSIONS.IOS.PHOTO_LIBRARY);
          }
        }

        let photosDir = "";
        if (Platform.OS === "android") {
          photosDir = `${RNFS.ExternalStorageDirectoryPath}/DCIM/Camera`; // Android için
        } else {
          photosDir = `${RNFS.LibraryDirectoryPath}/DCIM`; // iOS için
        }

        const files = await RNFS.readDir(photosDir);

        const sortedFiles = files
          .filter(file => file.isFile() && (file.name.endsWith(".jpg") || file.name.endsWith(".png")))
          .sort((a, b) => b.mtime - a.mtime);

        if (sortedFiles.length > 0) {
          setLastGalleryPhoto(`file://${sortedFiles[0].path}`);
        }
      } catch (error) {
        // console.error("Son fotoğraf alınamadı:", error);
      }
    };

    getLastPhoto();
  }, []);



  const openGallery = () => {
    console.log("Opening gallery...");
    launchImageLibrary({ mediaType: "photo", quality: 0.5 }, (response) => {
      console.log("Gallery response:", response);

      if (response.didCancel) {
        console.log("User cancelled image picker");
        return;
      }

      if (response.errorCode) {
        console.error("ImagePicker Error: ", response.errorMessage);
        return;
      }

      if (response.assets && response.assets.length > 0) {
        const lastPhoto = response.assets[response.assets.length - 1];
        console.log("Selected photo:", lastPhoto);

        if (lastPhoto.uri) {
          console.log("Setting gallery photo URI:", lastPhoto.uri);
          // Set both states to the same URI to ensure consistency
          setLastGalleryPhoto(lastPhoto.uri);
          setCapturedPhoto(lastPhoto.uri); // Galeriden seçilen fotoğrafı da capturedPhoto'ya atıyoruz

          // Automatically show filter options when a gallery photo is selected
          setFilterActive(true);
        } else {
          console.error("Invalid URI in selected photo:", lastPhoto);
        }
      } else {
        console.error("No assets found in response:", response);
      }
    });
  };



  useEffect(() => {
    // This listener will run when navigating away from the screen
    const unsubscribe = navigation.addListener("beforeRemove", () => {
      console.log("PostScreen beforeRemove - cleaning up");
      setCapturedPhoto(null);
      // Also clear the photo URI in Redux
      dispatch(setPhotoUri(null));
    });

    return unsubscribe;
  }, [navigation, dispatch]);

  // Track filter changes
  useEffect(() => {
    if (capturedPhoto && selectedFilter !== undefined) {
      console.log("Selected filter changed to:", filters[selectedFilter].name);

      // Force re-render of the filtered image when filter changes
      if (viewShotRef.current) {
        console.log("Refreshing ViewShot for new filter");
      }
    }
  }, [selectedFilter, capturedPhoto]);

  // Check ViewShot reference and ensure it's ready
  useEffect(() => {
    if (capturedPhoto && selectedFilter !== undefined) {
      console.log("ViewShot reference check:", viewShotRef ? "exists" : "missing");
      console.log("ViewShot current check:", viewShotRef?.current ? "initialized" : "not initialized");

      // Force a re-render to ensure ViewShot is properly initialized
      if (viewShotRef && !viewShotRef.current) {
        console.log("Forcing re-render to initialize ViewShot");
        setSelectedFilter(selectedFilter);
      }
    }
  }, [capturedPhoto, selectedFilter]);


  const closeScreen = () => {
    console.log("PostScreen closeScreen - resetting state and navigating back");
    // Reset all state variables
    setCapturedPhoto(null);
    setTimer(0);
    setShowTimerOptions(false);
    setCountdown(null);
    setFilterActive(false);
    setSelectedFilter(0);
    setIsCapturingFilteredImage(false);

    // Clear the photo URI in Redux to prevent it from persisting
    dispatch(setPhotoUri(null));

    // Navigate back
    navigation.goBack();
  };

  // Define filter components using color matrix filters
  const filters = [
    { name: "No Filter", component: Normal, matrix: null },
    { name: "Invert", component: Invert, matrix: invert() },
    { name: "Saturate", component: Saturate, amount: 2.0, matrix: saturate(2.0) },
    { name: "Brightness", component: Brightness, amount: 1.5, matrix: brightness(1.5) },
    { name: "Grayscale", component: Grayscale, matrix: grayscale() },
    { name: "Sepia", component: Sepia, matrix: sepia() },
    { name: "Vintage", component: Vintage, matrix: vintage() },
    { name: "Polaroid", component: Polaroid, matrix: polaroid() },
    { name: "Warm", component: Warm, matrix: warm() },
    { name: "Cool", component: Cool, matrix: cool() },
    { name: "Night", component: Night, matrix: night(0.1) },
    { name: "LSD", component: LSD, matrix: lsd() }
  ];

  // Fotoğrafın görüntülenmesi için render fonksiyonu
  const renderPhoto = () => {
    try {
      if (!capturedPhoto) {
        console.log("No captured photo to render");
        return null;
      }

      console.log("Rendering photo with URI:", capturedPhoto);

      // Get the selected filter component
      const selectedFilterObj = filters[selectedFilter];
      console.log("Selected filter object:", selectedFilterObj);

      if (!selectedFilterObj) {
        console.error("Selected filter object is undefined for index:", selectedFilter);
        return (
          <View style={styles.filterContainer}>
            <Image
              source={{ uri: capturedPhoto }}
              style={styles.preview}
              resizeMode="cover"
              onError={(error) => console.error("Image loading error:", error.nativeEvent.error)}
            />

          </View>
        );
      }

      const FilterComponent = selectedFilterObj.component;

      if (!FilterComponent) {
        console.error("Filter component is undefined for:", selectedFilterObj.name);
        return (
          <View style={styles.filterContainer}>
            <Image
              source={{ uri: capturedPhoto }}
              style={styles.preview}
              resizeMode="cover"
              onError={(error) => console.error("Image loading error:", error.nativeEvent.error)}
            />

          </View>
        );
      }

      console.log("Applying filter:", selectedFilterObj.name);

      // For "No Filter" case (Normal filter)
      if (selectedFilter === 0) {
        console.log("Rendering with Normal filter (No Filter)");
        return (
          <View style={styles.filterContainer}>
            <ViewShot
              ref={viewShotRef}
              options={{ format: 'jpg', quality: 0.9, result: 'data-uri' }}
              style={styles.previewContainer}
            >
              <Normal>
                <Image
                  source={{ uri: capturedPhoto }}
                  style={styles.preview}
                  resizeMode="cover"
                  onError={(error) => console.error("Image loading error:", error.nativeEvent.error)}
                />

              </Normal>
            </ViewShot>
          </View>
        );
      }

      // For filters with matrix parameter
      if (selectedFilterObj.matrix) {
        console.log(`Applying ${selectedFilterObj.name} with color matrix`);
        return (
          <View style={styles.filterContainer}>
            <ViewShot
              ref={viewShotRef}
              options={{ format: 'jpg', quality: 0.9, result: 'data-uri' }}
              style={styles.previewContainer}
            >
              <ColorMatrix matrix={selectedFilterObj.matrix} key={`filter-${selectedFilter}-${Date.now()}`} // Key'i güncelleyin
  >
                <Image
                  source={{ uri: capturedPhoto }}
                  style={styles.preview}
                  resizeMode="cover"
                  onError={(error) => console.error("Image loading error:", error.nativeEvent.error)}
                />

              </ColorMatrix>
            </ViewShot>
          </View>
        );
      }

      // For component-based filters
      console.log(`Applying ${selectedFilterObj.name} component filter`);
      return (
        <View style={styles.filterContainer}>
          <ViewShot
            ref={viewShotRef}
            options={{ format: 'jpg', quality: 0.9, result: 'data-uri' }}
            style={styles.previewContainer}
          >
            <FilterComponent>
              <Image
                source={{ uri: capturedPhoto }}
                style={styles.preview}
                resizeMode="cover"
                onError={(error) => console.error("Image loading error:", error.nativeEvent.error)}
              />

            </FilterComponent>
          </ViewShot>
        </View>
      );
    } catch (error) {
      console.error("Error in renderPhoto:", error);
      console.error("Error stack:", error.stack);

      // Fallback to just showing the image without filters
      return (
        <View style={styles.filterContainer}>
          <Image
            source={{ uri: capturedPhoto }}
            style={styles.preview}
            resizeMode="cover"
          />

        </View>
      );
    }
  };

  return (
    <View style={styles.container}>
      {!capturedPhoto ? (
       <Camera
         ratioOverlay="16:9"
         ref={cameraRef}
         style={styles.camera}
         cameraType={cameraType}
         zoomMode="off"
         focusMode="off"
         flashMode={torchMode}
         torchMode={torchMode}
         laserColor="transparent"
         frameColor='transparent'
       />
      ) : (
        renderPhoto()
      )}

      {/* Sol üst köşeye close butonu */}
      <TouchableOpacity onPress={closeScreen} style={styles.closeButton}>
        <SvgClose />
      </TouchableOpacity>

      {/* Sağ üst köşeye diğer butonlar */}
      <View style={styles.topRightControls}>
        {!capturedPhoto ? (
          <>
            <TouchableOpacity onPress={toggleFlash} style={styles.iconButton}>
              <SvgFlash />
            </TouchableOpacity>

            <TouchableOpacity onPress={toggleCameraType} style={styles.iconButton}>
              <SvgTurnCam />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => setShowTimerOptions(!showTimerOptions)}
              style={styles.iconButton}
            >
              <SvgTimer />
              <Text style={styles.timerText}>{timer}s</Text>
            </TouchableOpacity>
          </>
        ) : (
          isCapturingFilteredImage ? (
            <View style={styles.iconButton}>
              <ActivityIndicator size="small" color="#FFFFFF" />
            </View>
          ) : (
            <TouchableOpacity onPress={goToPostConfirm} style={styles.iconButton}>
              <SvgNext />
            </TouchableOpacity>
          )
        )}
      </View>

      {/* Timer seçenekleri */}
      {showTimerOptions && (
        <View style={styles.timerDropdown}>
          {[0, 3, 5, 10].map((t) => (
            <TouchableOpacity
              key={t}
              onPress={() => {
                setTimer(t);
                setShowTimerOptions(false);
              }}
              style={styles.timerOption}
            >
              <Text style={styles.timerText}>{t}s</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

        {/* Geri Sayım Gösterimi */}
        {countdown !== null && (
        <View style={{ position: "absolute", top: "40%", left: "50%", transform: [{ translateX: -50 }] }}>
          <Text style={{ fontSize: 50, color: "black", fontWeight: "bold" }}>{countdown}</Text>
        </View>
      )}

      {/* Fotoğraf çekme ve galeri butonları */}
      <View style={styles.controls}>
        <TouchableOpacity onPress={() => setFilterActive(!filterActive)} style={styles.filterButton}>
          <SvgFilter/>
        </TouchableOpacity>

        <TouchableOpacity onPress={startCountdown} style={styles.captureButton}>
          <SvgTakePhoto />
        </TouchableOpacity>

        <TouchableOpacity onPress={openGallery} style={styles.galleryButton}>
        {lastGalleryPhoto ? (
          capturedPhoto === lastGalleryPhoto ? (
            // If the current photo is from gallery, show a simple gallery icon instead of thumbnail
            <View style={styles.galleryIconContainer}>
              <Text style={styles.galleryText}>Galeri</Text>
            </View>
          ) : (
            // Otherwise show the thumbnail
            <Image source={{ uri: lastGalleryPhoto }} style={styles.galleryImage} />
          )
        ) : (
          <Text style={styles.galleryText}>Galeri</Text>
        )}
      </TouchableOpacity>
      </View>
      {/* Filtre seçeneklerini aç - scrollable */}
      {filterActive && capturedPhoto && (
        <View style={styles.filterOptionsContainer}>
          <FlatList
            data={filters}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={(_, index) => index.toString()}
            renderItem={({ item, index }) => {
              const FilterComponent = item.component;
              return (
                <TouchableOpacity
                  onPress={() => {
                    console.log("Filter selected:", filters[index].name);
                    setSelectedFilter(index);
                  }}
                  style={[
                    styles.filterOption,
                    selectedFilter === index && styles.selectedFilterOption
                  ]}
                >
                  {/* Show filter preview on the thumbnail */}
                  <View style={styles.filterPreviewContainer}>
                    {item.matrix ? (
                      <ColorMatrix matrix={item.matrix}>
                        <Image
                          source={{ uri: capturedPhoto }}
                          style={styles.filterPreviewImage}
                          resizeMode="cover"
                        />
                      </ColorMatrix>
                    ) : (
                      <FilterComponent>
                        <Image
                          source={{ uri: capturedPhoto }}
                          style={styles.filterPreviewImage}
                          resizeMode="cover"
                        />
                      </FilterComponent>
                    )}
                  </View>
                  <Text style={styles.filterText}>{item.name}</Text>
                </TouchableOpacity>
              );
            }}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { width:"100%" ,
    height:"100%"
    // backgroundColor: 'transparent'
  },
  camera: { width:"100%" ,
  height:"100%",
  // backgroundColor: 'transparent'
},
  preview: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
    backgroundColor: "#000"
  },
  previewContainer: {
    width: "100%",
    height: "100%",
    backgroundColor: "transparent"
  },
  controls: {
    position: "absolute",
    bottom: 50,
    width: "100%",
    flexDirection: "row",
    justifyContent: "space-evenly",
    alignItems: "center",
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "white",
    borderWidth: 3,
    borderColor: "black",
    justifyContent: "center",
    alignItems: "center",
  },
  filterButton: {
    width: 59,
    height: 59,
    borderRadius: 5,
    backgroundColor: "white",
    borderWidth: 3,
    borderColor: "black",
    justifyContent: "center",
    alignItems: "center",
  },
  galleryButton: {
    width: 59,
    height: 59,
    borderRadius: 5,
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
  },
  galleryImage: {
    width: "100%",
    height: "100%",
    borderRadius: 5,
  },
  galleryIconContainer: {
    width: "100%",
    height: "100%",
    borderRadius: 5,
    backgroundColor: "#f0f0f0",
    justifyContent: "center",
    alignItems: "center",
  },
  galleryText: {
    fontSize: 12,
    color: "#333",
    textAlign: "center",
  },
  topRightControls: {
    position: "absolute",
    marginTop: 60,
    right: 20,
    alignItems: "center",
  },
  iconButton: {
    marginBottom: 15,
    alignItems: "center",
  },
  timerDropdown: {
    position: "absolute",
    top: height * 0.26,
    right: 15,
    backgroundColor: "rgba(0,0,0,0.7)",
    padding: 5,
    borderRadius: 5,
  },
  timerOption: {
    padding: 5,
  },
  timerText: {
    color: "white",
    fontSize: 16,
  },
  closeButton: {
    position: "absolute",
    marginTop: 60,
    left: 20,
  },
  filterOptionsContainer: {
    position: "absolute",
    bottom: 120,
    width: "100%",
    backgroundColor: "rgba(0,0,0,0.5)",
    padding: 10,
  },
  filterOption: {
    padding: 8,
    backgroundColor: "white",
    borderRadius: 5,
    marginHorizontal: 4,
    minWidth: 80,
    alignItems: "center",
  },
  selectedFilterOption: {
    backgroundColor: "#FFB6C1", // Light pink to indicate selection
    borderWidth: 2,
    borderColor: "#FF69B4", // Darker pink border
  },
  filterText: {
    color: "black",
    fontSize: 12,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 5,
  },
  filterPreviewContainer: {
    width: 60,
    height: 60,
    borderRadius: 5,
    overflow: "hidden",
    marginBottom: 5,
  },
  filterPreviewImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  filterContainer: {
    width: "100%",
    height: "100%",
    flex: 1,
    backgroundColor: "transparent"
  },
});

export default PostScreen;
