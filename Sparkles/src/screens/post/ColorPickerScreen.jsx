import { View, Text, TouchableOpacity, StyleSheet, FlatList, Dimensions } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { toggleColorSelection } from '../../redux/slices/colorSlice';
import { width } from '../../utils/helpers';
import SvgBack from "../../assets/back.js"
import { useNavigation } from '@react-navigation/native';
import { setColor } from '../../redux/slices/productLinkSlice.js';
import { useState } from 'react';

const ITEM_SIZE = width / 4;

const ColorPickerScreen = ({ route }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  
  // Route'dan gelen parametreler
  const { 
    category, 
    currentColor, 
    onColorSelect 
  } = route.params || {};
  
  const availableColors = useSelector(state => state.colors.availableColors);
  const [selectedColor, setSelectedColor] = useState(currentColor); // Yerel state

  // Renkleri 4'lü gruplara ayırma
  const groupedColors = [];
  for (let i = 0; i < availableColors.length; i += 4) {
    groupedColors.push(availableColors.slice(i, i + 4));
  }

  // Renk seçimini işleyen fonksiyon
  const handleColorSelection = (color) => {
    setSelectedColor(color.name); // Yerel state'i güncelle
    
    if (onColorSelect) {
      // Eğer onColorSelect fonksiyonu varsa (yeni yöntem)
      onColorSelect(color.name);
    } else {
      // Eski yöntemle çalışma (backward compatibility)
      dispatch(toggleColorSelection(color));
      dispatch(setColor(color.name));
      
      if (category) {
        dispatch(updateCategoryColor({ category, color: color.name }));
      }
    }
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity activeOpacity={0.7} onPress={handleGoBack}>
          <SvgBack />
        </TouchableOpacity>
        <Text style={styles.title}>Renkler</Text>
      </View>
      
      <FlatList
        data={groupedColors}
        keyExtractor={(_, index) => index.toString()}
        renderItem={({ item }) => (
          <View style={styles.row}>
            {item.map((color) => {
              const isActive = selectedColor === color.name;
              return (
                <View key={color.id} style={styles.colorWrapper}>
                  <TouchableOpacity
                    onPress={() => handleColorSelection(color)}
                    style={[
                      styles.circle,
                      {
                        backgroundColor: color.hex,
                        opacity: isActive ? 1 : 0.3,
                        borderWidth: isActive ? 2 : 0,
                        borderColor: '#000',
                      },
                    ]}
                  />
                  <Text style={styles.colorName}>{color.name}</Text>
                </View>
              );
            })}
          </View>
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default ColorPickerScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 16,
    paddingHorizontal: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 16,
    height: 60,
    marginTop:50,
  },
  title: {
    fontWeight: '500',
    fontSize: 16,
    marginLeft: 16, 
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'flex-start', 
    marginBottom: 24,
  },
  colorWrapper: {
    width: ITEM_SIZE - 16,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  circle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 6,
  },
  colorName: {
    fontSize: 14,
    textAlign: 'center',
  },
});