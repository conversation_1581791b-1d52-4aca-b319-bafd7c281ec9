import { View, Text, TouchableOpacity, StyleSheet, FlatList, TextInput } from 'react-native';
import { useDispatch } from 'react-redux';
import SvgBack from "../../assets/back.js";
import SvgSearch from "../../assets/searchpeople.js";
import { useNavigation } from '@react-navigation/native';
import { setYakaType } from '../../redux/slices/productLinkSlice.js';
import { updateCategoryCollarType } from '../../redux/slices/categoriesSlice';
import { useState } from 'react';

const COLLAR_TYPES = [
  { id: 1, name: '<PERSON>' },
  { id: 2, name: '<PERSON><PERSON><PERSON>' },
  { id: 3, name: '<PERSON><PERSON><PERSON><PERSON>' },
  { id: 4, name: '<PERSON>' },
  { id: 5, name: '<PERSON><PERSON>' },
  { id: 6, name: '<PERSON>bit <PERSON>' },
  { id: 7, name: '<PERSON><PERSON><PERSON>' },
  { id: 8, name: '<PERSON><PERSON><PERSON>' },
  { id: 9, name: '<PERSON><PERSON><PERSON>' },
  { id: 10, name: 'Asimetrik Ya<PERSON>' },
  { id: 11, name: '<PERSON><PERSON><PERSON>k<PERSON><PERSON> Yaka' },
  { id: 12, name: '<PERSON><PERSON>rfırlı Yaka' },
  { id: 13, name: 'Fermuarlı Yaka' },
  { id: 14, name: 'Dikme Yaka' },
  { id: 15, name: 'Kare Yaka' },
  { id: 16, name: 'Halter Yaka' },
];

const CollarTypeScreen = ({ route }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const {
    category,
    currentCollarType,
    onCollarTypeSelect
  } = route.params || {};

  const [selectedCollarType, setSelectedCollarType] = useState(currentCollarType);
  const [searchText, setSearchText] = useState('');

  const filteredCollarTypes = COLLAR_TYPES
    .filter(type => type.name.toLowerCase().includes(searchText.toLowerCase()))
    .sort((a, b) => {
      const aIndex = a.name.toLowerCase().indexOf(searchText.toLowerCase());
      const bIndex = b.name.toLowerCase().indexOf(searchText.toLowerCase());

      if (aIndex !== bIndex) return aIndex - bIndex;
      return a.name.length - b.name.length;
    });

  const handleCollarTypeSelection = (collarType) => {
    setSelectedCollarType(collarType.name);

    if (onCollarTypeSelect) {
      onCollarTypeSelect(collarType.name);
    } else {
      dispatch(updateCategoryCollarType({ category, collarType: collarType.name }));
      dispatch(setYakaType(collarType.name));
    }

  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <View style={styles.searchRow}>
        <TouchableOpacity activeOpacity={0.7} onPress={handleGoBack}>
          <SvgBack />
        </TouchableOpacity>
        
        <View style={styles.searchContainer}>
          <SvgSearch style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Yaka Tipi Ara"
            placeholderTextColor="#BBBBBB"
            onChangeText={setSearchText}
            value={searchText}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            selectionColor="#D134AA"
          />
        </View>
      </View>

      {/* Collar Type List */}
      <FlatList
        data={filteredCollarTypes}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => {
          const isSelected = selectedCollarType === item.name;
          return (
            <TouchableOpacity
              style={[
                styles.collarTypeItem,
                isSelected && styles.selectedItem
              ]}
              onPress={() => handleCollarTypeSelection(item)}
            >
              <Text style={styles.collarTypeName}>{item.name}</Text>
              <View style={[
                styles.selectionIndicator,
                isSelected && styles.selectedIndicator
              ]} />
            </TouchableOpacity>
          );
        }}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    
    paddingTop:20
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center', 
    width: '100%', 
    marginTop:50,
    paddingHorizontal:16,
    height:60,
    marginBottom:20
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    borderWidth: 1,
    borderColor: '#000',
    paddingHorizontal: 16, 
    height: 44, 
    marginLeft:10
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
    padding: 0, 
    margin: 0, 
    includeFontPadding: false, 
    textAlignVertical: 'center', 
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 20,
  },
  title: {
    fontSize: 14,
    fontWeight: 'regular',
    marginLeft: 20,
    color: '#000000',
  },
  collarTypeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal:16,
    borderBottomWidth: 1,
    borderBottomColor: '#FFFFFF',
    backgroundColor:'#F0F0F0'
  },
  selectedItem: {
    backgroundColor: '#F0F0F0',
  },
  collarTypeName: {
    fontSize: 14,
    color: '#000000',
    fontWeight:"300"
  },
  selectionIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#000000',
    backgroundColor:"#D9D9D9"
  },
  selectedIndicator: {
    backgroundColor: '#000000',
    borderColor: '#000000',
  }
});

export default CollarTypeScreen;