import {FlatList,Keyboard,StyleSheet,Text,TextInput,TouchableOpacity,TouchableWithoutFeedback,View,} from 'react-native';
import SvgSearch from '../../assets/searchpeople';
import {useEffect, useState} from 'react';
import SvgBack from '../../assets/back';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedBrands } from '../../redux/slices/brandSlice';
import { setProductBrand } from '../../redux/slices/productLinkSlice';
import { updateCategoryBrand } from '../../redux/slices/categoriesSlice';

const BrandSearchScreen = ({route}) => {
  const navigation = useNavigation();
  const [searchText, setSearchText] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const availableBrands = useSelector(state => state.brands.availableBrands);
  const dispatch = useDispatch();
  
  const { category, currentBrand: initialBrand, onBrandSelect } = route.params || {};
  const [currentBrand, setCurrentBrand] = useState(initialBrand); // Yerel state eklendi

  
  const filteredBrands = availableBrands
    .filter(brand => brand.name.toLowerCase().includes(searchText.toLowerCase()))
    .sort((a, b) => {
      const aIndex = a.name.toLowerCase().indexOf(searchText.toLowerCase());
      const bIndex = b.name.toLowerCase().indexOf(searchText.toLowerCase());
      
      if (aIndex !== bIndex) return aIndex - bIndex;
      return a.name.length - b.name.length;
    });
  
    useEffect(() => {
      if (route.params?.selectedBrands) {
        dispatch(setSelectedBrands(route.params.selectedBrands));
        
        if (route.params.selectedBrands.length > 0) {
          const lastSelectedBrand = route.params.selectedBrands[route.params.selectedBrands.length - 1];
          dispatch(setProductBrand(lastSelectedBrand.name)); 
        }
      }
    }, [dispatch, route.params?.selectedBrands]);


  const handleBrandSelection = (brand) => {
    const brandName = brand.name;
    console.log('Selected brand:', brandName);
    console.log('current brand:', currentBrand);
    
    setCurrentBrand(brandName);
    
    if (onBrandSelect) {
      onBrandSelect(brandName);
    } else {
      dispatch(updateCategoryBrand({category, brand: brandName}));
      dispatch(setProductBrand(brandName));
    }
    
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

 
  
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
    <View style={styles.container}>
      {/* Search Alanı */}
      <View style={styles.searchRow}>
        <TouchableOpacity activeOpacity={0.7} onPress={handleGoBack}>
          <SvgBack />
        </TouchableOpacity>
        
        <View style={styles.searchContainer}>
          <SvgSearch style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Marka ara"
            placeholderTextColor="#BBBBBB"
            onChangeText={setSearchText}
            value={searchText}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            selectionColor="#D134AA"
          />
        </View>
      </View>
  
      {/* Marka Listesi */}
      <FlatList
        data={filteredBrands}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <TouchableOpacity 
            style={[
              styles.brandItem,
              currentBrand === item.name && styles.selectedBrandItem
            ]}
            onPress={() => handleBrandSelection(item)}
          >
            <Text
              style={[
                styles.brandText,
                currentBrand === item.name && styles.selectedBrandText
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.listContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  </TouchableWithoutFeedback>
  );
};

export default BrandSearchScreen;


  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#FFFFFF',
      paddingTop: 20,
    },
    searchRow: {
      flexDirection: 'row',
      alignItems: 'center', 
      width: '100%', 
      marginTop:50,
      paddingHorizontal:16,
      height:60
    },
    searchContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#FFFFFF',
      borderRadius: 30,
      borderWidth: 1,
      borderColor: '#000',
      paddingHorizontal: 16, 
      height: 44, 
      marginLeft:10
    },
    searchIcon: {
      marginRight: 8,
    },
    searchInput: {
      flex: 1,
      fontSize: 14,
      color: '#000',
      fontWeight: '500',
      padding: 0, 
      margin: 0, 
      includeFontPadding: false, 
      textAlignVertical: 'center', 
    },
    listContainer: {
      marginTop: 25,
    },
    brandItemContainer: {
      width: '100%',
    },
    brandItem: {
      paddingVertical: 15,
      backgroundColor: '#F0F0F0', 
      height: 47,
      width: '100%',
      paddingHorizontal: 23, 
    },
    brandText: {
      fontWeight: '300',
      fontSize: 14,
      color: '#000000',
    },
    separator: {
      height: 2,
      backgroundColor: 'transparent',
    },
    selectedBrandItem: {
      backgroundColor: '#000000', 
    },
    selectedBrandText: {
      color: '#FFFFFF', 
      fontWeight: '500', 
    },
  });

