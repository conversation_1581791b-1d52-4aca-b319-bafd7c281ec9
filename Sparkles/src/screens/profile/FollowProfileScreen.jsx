import { View, StyleSheet, TouchableOpacity, SafeAreaView, Alert, ActivityIndicator } from 'react-native';
import SvgBack from "../../assets/back"
import SvgMenu from "../../assets/hamburgerMenu"
import { users } from '../../utils/helpers';
import { useEffect, useMemo, useState } from 'react';
import { ScrollView, Text } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { fetchFollowers, fetchFollowing, followUser, unfollowUser, isUserFollowing } from '../../redux/slices/followSlice';
import { getUserProfile } from '../../redux/actions/userActions';
import store from '../../redux/store';
import { getSafeUserId } from '../../api/api';
import ProfileInfo from '../../components/Profile/ProfileInfo';
import SearchProfileHidden from '../../components/Search/SearchProfileHidden';
import FollowCard from '../../components/Home/FollowCard';
import SearchProfileDetail from '../../components/Search/SearchProfileDetail';
import { useNavigation } from '@react-navigation/native';


const FollowProfileScreen = ({ route, closeModal }) => {
  const { user } = route.params;
  console.log("follow profile gelen user", user)
    const dispatch = useDispatch();
    const { isLogin } = useSelector(state => state.auth);
    const { userInfo: loggedInUserInfo, currentProfile } = useSelector((state) => state.user);
    const followers = useSelector(state => state.follow.followers);
    const following = useSelector(state => state.follow.following);
    const followStatus = useSelector(state => state.follow.status);
    const loading = useSelector(state => state.follow.loading);
    const navigation = useNavigation()


    const [isLoading, setIsLoading] = useState(false);
    const userId = getSafeUserId(user);

    // Kullanıcı profil bilgilerini çek
    useEffect(() => {
      if (userId) {
        console.log("FollowProfileScreen - Kullanıcı profili çekiliyor, userId:", userId);
        dispatch(getUserProfile(userId))
          .unwrap()
          .then(profileData => {
            console.log("FollowProfileScreen - Kullanıcı profili başarıyla çekildi:", {
              id: profileData.id,
              username: profileData.username,
              followerCount: profileData.followerCount,
              followingCount: profileData.followingCount
            });
          })
          .catch(error => {
            console.error("FollowProfileScreen - Kullanıcı profili çekilirken hata:", error);
          });
      }
    }, [dispatch, userId]);

    // Simplify state management - use separate state variables for better reactivity
    const [isFollowing, setIsFollowing] = useState(false);
    const [followersCount, setFollowersCount] = useState(user?.followersCount || 0);
    const [followingCount, setFollowingCount] = useState(user?.followingCount || 0);

    // Keep a reference to the optimistic state for compatibility with existing code
    const optimisticState = {
      isFollowing,
      followersCount,
      followingCount,
      profileId: userId,
      userId: getSafeUserId(user)
    };

    // Debug
    console.log("follow profile Redux auth state:", {
      user,
      isLogin,
      hasUserId: !!getSafeUserId(user)
    });
    console.log("Current follow state:", {
      followersCount: followers.length,
      followingCount: following.length,
      optimisticState
    });

    const randomUser = useMemo(() => {
      return users[Math.floor(Math.random() * users.length)];
    }, []);

    // Veri yükleme efektleri
    useEffect(() => {
      // Mevcut giriş yapmış kullanıcı bilgisini al
      const { user: currentUser } = store.getState().auth;
      const currentUserId = currentUser?._id || currentUser?.id;

      if (!currentUserId) {
        console.log("Takip listesi yüklenemedi: Giriş yapılmamış");
        return;
      }

      // Profil sahibinin ID'si yerine giriş yapmış kullanıcının takip listesini çek
      console.log("Giriş yapmış kullanıcının takip listesi yükleniyor:", currentUserId);
      dispatch(fetchFollowing(currentUserId))
        .then((result) => {
          if (result.payload) {
            console.log("Takip listesi yüklendi. Eleman sayısı:", result.payload.length);

            // Takip durumunu hemen kontrol et
            if (userId) {
              const isFollowing = isUserFollowing(result.payload, userId);
              console.log(`${userId} kullanıcısını takip ediyor mu:`, isFollowing);

              setOptimisticState(prev => ({
                ...prev,
                isFollowing: isFollowing
              }));
            }
          }
        })
        .catch(error => {
          console.error("Takip listesi yüklenirken hata:", error);
        });
    }, [dispatch, userId]);

    useEffect(() => {
      if (!userId) return;

      // Profil sahibinin takipçilerini yükle
      console.log("Profil sahibinin takipçileri yükleniyor:", userId);
      dispatch(fetchFollowers(userId));
    }, [dispatch, userId]);

    // Takip durumu güncelleme
    useEffect(() => {
      if (!userId || !following) return;

      // Mevcut kullanıcının takip listesini ve kullanıcının ID'sini alıyoruz
      const { user: currentUser } = store.getState().auth;
      const currentUserId = currentUser?._id || currentUser?.id;

      console.log('FollowProfileScreen - Takip durumu kontrol ediliyor:', {
        currentUserId,
        targetUserId: userId,
        followingLength: following.length,
        followersLength: followers.length
      });

      // Takip durumunu kontrol ederken giriş yapmış kullanıcının takip listesini kontrol ediyoruz
      const followingStatus = isUserFollowing(following, userId);
      console.log(`FollowProfileScreen - Takip durumu sonucu: ${followingStatus ? 'Takip ediyor' : 'Takip etmiyor'}`);

      // Takipçi ve takip edilen sayılarını güncelle
      // Öncelik sırası: currentProfile > user prop > followers/following arrays
      const followerCount =
        (currentProfile?.followerCount || currentProfile?.followersCount) ||
        (user?.followerCount || user?.followersCount) ||
        followers.length ||
        0;

      const followingCount =
        currentProfile?.followingCount ||
        user?.followingCount ||
        following.length ||
        0;

      console.log("FollowProfileScreen - Takipçi/Takip sayıları:", {
        followerCount,
        followingCount
      });

      // Update state variables directly for better reactivity
      setIsFollowing(followingStatus);
      setFollowersCount(followerCount);
      setFollowingCount(followingCount);

      console.log("State updated - UI should refresh immediately");
    }, [userId, following, followers, user, currentProfile]);




    const handleFollowToggle = async () => {
      console.log('1. Following toggle başlatılıyor');

      // Redux store'dan mevcut giriş yapmış kullanıcı bilgisini al
      const { user: currentUser } = store.getState().auth;
      const currentUserId = currentUser?._id || currentUser?.id;

      console.log('2. Giriş yapmış kullanıcı ID:', currentUserId);
      console.log('3. Hedef kullanıcı ID:', userId);
      console.log('4. Yükleme durumu:', isLoading);
      console.log('4a. Mevcut takip durumu:', isFollowing);

      if (!userId || !currentUserId || isLoading) {
        console.log('5. İşlem iptal - ID eksik veya yükleme devam ediyor', {
          userId,
          currentUserId,
          isLoading
        });
        return;
      }

      // Set loading state immediately
      setIsLoading(true);
      console.log('6. Yükleme durumu true olarak ayarlandı');

      const wasFollowing = isFollowing;
      console.log('7. Önceki takip durumu:', wasFollowing);

      try {
        // Apply optimistic UI update immediately
        console.log('8. İyimser güncelleme başlatılıyor');

        // If we're currently not following, we want to follow
        if (!wasFollowing) {
          // Update state immediately for better UX
          setIsFollowing(true);
          setFollowersCount(prev => prev + 1);
          console.log('9. Takip durumu güncellendi - UI hemen değişmeli');

          console.log('10. Takip etme eylemi gönderiliyor');
          const result = await dispatch(followUser(userId)).unwrap();
          console.log('11. Takip etme eylemi başarıyla tamamlandı:', result);

          // Force a re-render to ensure component replacement
          // This is a key step to ensure the UI updates correctly
          requestAnimationFrame(() => {
            console.log('11a. Forcing immediate UI update after follow');
            setIsFollowing(true);
          });
        }
        // If we're currently following, we want to unfollow
        else {
          // Update state immediately for better UX
          setIsFollowing(false);
          setFollowersCount(prev => prev - 1);
          console.log('9. Takip durumu güncellendi - UI hemen değişmeli');

          console.log('10. Takibi bırakma eylemi gönderiliyor');
          const result = await dispatch(unfollowUser(userId)).unwrap();
          console.log('11. Takibi bırakma eylemi başarıyla tamamlandı:', result);

          // Force a re-render to ensure component replacement
          // This is a key step to ensure the UI updates correctly
          requestAnimationFrame(() => {
            console.log('11a. Forcing immediate UI update after unfollow');
            setIsFollowing(false);
          });
        }

        // The Redux state should already be updated by the action
        // We can verify the follow status from the Redux store
        const updatedFollowing = store.getState().follow.following;
        const updatedFollowStatus = isUserFollowing(updatedFollowing, userId);

        console.log('12. Doğrulanmış takip durumu:', {
          isFollowing: updatedFollowStatus,
          followingLength: updatedFollowing.length
        });

        // Make sure the UI reflects the actual follow status from the Redux store
        console.log('13. UI durumu Redux store ile senkronize ediliyor');

        // Force update state with Redux store data
        setIsFollowing(updatedFollowStatus);

        // Update follower count based on Redux store data
        const currentFollowers = store.getState().follow.followers;
        setFollowersCount(currentFollowers.length);

        console.log('14. Takip durumu güncellendi:', updatedFollowStatus);

      } catch (error) {
        console.log('15. Hata oluştu:', error);

        // "Zaten takip ediliyor" hatası için özel işlem
        const errorMessage = typeof error === 'object' ? error.message : String(error);
        const isAlreadyFollowingError =
          errorMessage.includes("Bu kullanıcıyı zaten takip ediyorsunuz") ||
          errorMessage.includes("You are already following this user") ||
          errorMessage.includes("already following");

        if (isAlreadyFollowingError) {
          console.log('15a. "Zaten takip ediliyor" hatası algılandı');

          // Takip durumunu true olarak ayarla
          setIsFollowing(true);
          console.log('15b. Takip durumu true olarak ayarlandı');

          // Force a re-render to ensure component replacement
          requestAnimationFrame(() => {
            console.log('15c. Forcing component re-render after error');
            setIsFollowing(true);
          });
        } else {
          // Diğer hatalar için optimistik durumu geri al
          console.log('16. Optimistik durum geri alınıyor');
          setIsFollowing(wasFollowing);
          setFollowersCount(prev => wasFollowing ? prev + 1 : prev - 1);
          console.log('17. Takip durumu geri alındı:', wasFollowing);

          Alert.alert('Error', 'Takip işlemi sırasında bir hata oluştu');
        }
      } finally {
        // Always set loading to false when done
        console.log('18. Son temizlik - yükleme durumu false olarak ayarlanıyor');
        setIsLoading(false);
        console.log('19. Takip işlemi tamamlandı, son durum:', isFollowing ? 'Takip ediliyor' : 'Takip edilmiyor');
      }
    };

    // Konsola takip durumunu yazdır ve UI güncellemelerini izle
    useEffect(() => {
      console.log('Follow status changed - UI should update:', {
        isFollowing,
        followersCount,
        followingCount,
        profileId: userId,
        timestamp: Date.now()
      });

      // Log which component should be shown based on follow status
      if (isFollowing) {
        console.log('Should show SearchProfileDetail');
      } else {
        console.log('Should show SearchProfileHidden');
      }

      // No need for force update hack anymore
    }, [isFollowing, followersCount, followingCount, userId]);

    // Yükleme durumu ve user kontrolü - moved after all hooks
    if (loading || isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
        </View>
      );
    }

    if (!user) {
      return null;
    }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <TouchableOpacity onPress={navigation.goBack}>
          <SvgBack/>
        </TouchableOpacity>
        <TouchableOpacity>
          <SvgMenu/>
        </TouchableOpacity>
      </View>

      <ProfileInfo
        user={{
          id: userId,
          username: user?.username,
          fullName: user?.fullName,
          bio: user?.bio,
          profilePicture: user?.profilePicture,
          followersCount: followersCount,
          followingCount: followingCount
        }}
        handleFollowToggle={handleFollowToggle}
        isFollowingUser={isFollowing}
        isLoading={isLoading || loading}
      />

      {/* Render different components based on follow status */}
      <View style={{width: '100%'}}>
        {isFollowing ? (
          // If following, show the user's profile details
          <SearchProfileDetail
            user={{...user, id: userId}}
          />
        ) : (
          // If not following, show the hidden profile message and follow button
          <>
            <SearchProfileHidden
              user={user}
              handleFollowToggle={handleFollowToggle}
            />
            <FollowCard
              randomUser={randomUser}
            />
          </>
        )}
      </View>
    </ScrollView>
  );
};

export default FollowProfileScreen;

const styles = StyleSheet.create({
  loadingContainer: {
    flex:1,
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
    backgroundColor:"#FFFFFF",
  },
  header:{
    flexDirection:"row",
    justifyContent:"space-between",
    marginHorizontal:20,
    marginTop:60
  },
  username: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  userHandle: {
    fontSize: 18,
    color: 'gray',
  },
})