import React, { useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, ActivityIndicator, Image } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { fetchFollowers, fetchFollowing } from '../../redux/slices/followSlice';
import SvgBack from "../../assets/back.js"
import { useFocusEffect, useNavigation } from '@react-navigation/native';


const FollowListScreen = ({ onClose,route }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation()
  const { activeTab, userId } = route.params;
  
  console.log("userınfo",userId)
  const { followers, following, loading } = useSelector(state => state.follow);
  
  useFocusEffect(
    React.useCallback(() => {
      // Önce mevcut verileri temizle (geçici olarak boş dizi yap)
      if (activeTab === 'followers') {
        dispatch({ type: 'followers/reset' }); // Redux'ta followers'ı sıfırla
      } else {
        dispatch({ type: 'following/reset' }); // Redux'ta following'i sıfırla
      }
  
      // Sonra yeni verileri çek
      if (activeTab === 'followers') {
        dispatch(fetchFollowers(userId));
      } else {
        dispatch(fetchFollowing(userId));
      }
    }, [activeTab, userId])
  );
//   if (loading) {
//     return (
//         <View style={styles.loadingContainer}>
//         <ActivityIndicator size="large" />
//       </View>
//     );
//   }

  const currentData = activeTab === 'followers' ? followers : following;
  console.log("Current Data:", currentData);
  if (loading || currentData.loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (currentData.error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Hata: {currentData.error.message || 'Beklenmeyen bir hata oluştu'}</Text>
      </View>
    );
  }

  return (
    <View style={styles.mainContainer}>
    <View style={styles.container}>
     <View style={styles.header}>
  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
    <TouchableOpacity onPress={navigation.goBack}>
      <SvgBack/>
    </TouchableOpacity>
    <Text style={[styles.title, { marginLeft: 15 }]}>
      {activeTab === 'followers' ? 'Takipçiler' : 'Takip Edilenler'}
    </Text>
  </View>
</View>

      <FlatList
        data={currentData}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.flatListContent}
        renderItem={({ item }) => (
          <TouchableOpacity 
            style={styles.userItem}
            onPress={() => navigation.navigate('FollowProfile', { user: item })}
          >
            <Image 
              source={{ uri: item.profilePicture }} 
              style={styles.profileImage}
            />
            <View style={styles.userInfo}>
              <Text style={styles.fullName}>{item.fullName}</Text>
              <Text style={styles.username}>{item.username}</Text>
              
            </View>
          </TouchableOpacity>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {activeTab === 'followers' ? 'Takipçi bulunamadı' : 'Takip edilen kullanıcı bulunamadı'}
            </Text>
          </View>
        }
      />
    </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#fff', // Ana arka plan rengi
  },
  container: {
    flex: 1,
    paddingVertical: 15, // Sadece dikey padding
    backgroundColor: '#fff',
    marginHorizontal: 20 // Tüm ekrana yatay margin
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 10,
    marginTop: 70,
    paddingLeft: 0, // Sol padding'i kaldır
  },
  flatListContent: {
    paddingLeft: 0, // FlatList içeriğinin sol padding'ini kaldır
  },
  title: {
    marginLeft: 10, // İkon ile yazı arasına boşluk ekle
    fontSize: 16,
    fontWeight: '600',
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  profileImage: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginRight: 15
  },
  userInfo: {
    flex: 1
  },
  fullName:{
    fontWeight: '600',
    fontSize: 14,
    marginBottom: 3
  },
  username: {
    fontWeight: '400',
    fontSize: 12,
    marginBottom: 3,
    color:"#9D9C9C"
  },
  bio: {
    color: '#666',
    fontSize: 14
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  errorText: {
    color: 'red',
    textAlign: 'center'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  emptyText: {
    color: '#888',
    fontSize: 16
  }
});

export default FollowListScreen;