import {useState, useEffect, useMemo, useRef} from 'react';
import {
  View,
  Image,
  StyleSheet,
  TouchableOpacity,
  Text,
  TouchableWithoutFeedback,
  FlatList,
  Dimensions,
} from 'react-native';
import SvgCloseLight from '../../assets/closeLight';
import SvgHeart from '../../assets/heart';
import SvgHeartFill from '../../assets/heartFill';
import SvgComments from '../../assets/comments';
import SvgBookmark from '../../assets/bookmark';
import SvgShare from '../../assets/share';
import SvgHanger from '../../assets/hanger';
import SvgPlusPinkB from '../../assets/plusPinkB';
import LinearGradient from 'react-native-linear-gradient';
import ShareModal from '../../components/Home/ShareModal';
import CommentModal from '../../components/Home/CommentModal';
import CollectionsModal from '../../components/Home/CollectionsModal';
import HangerModal from '../../components/Home/HangerModal';
import { useDispatch, useSelector } from 'react-redux';
import { savePost, unsavePost } from '../../redux/slices/savedPostSlice';
import { likePost, toggleLike } from '../../redux/actions/postActions';
import { fetchPostDetail } from '../../redux/actions/getPostDetailActions';
import { useNavigation, useRoute } from '@react-navigation/native';

// Temporary placeholder for addComment function
const addComment = (comment) => {
  console.log('Adding comment:', comment);
  return { type: 'ADD_COMMENT', payload: comment };
};

const FullPostScreen = ({ image, onClose, postId, allPosts }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const savedPosts = useSelector((state) => state.savedPosts.savedPosts);
  const feedPosts = useSelector(state => state.posts.feedPosts);

  const [modalVisible, setModalVisible] = useState(false);
  const [commentModal, setCommentModal] = useState(false);
  const [collectionModal, setCollectionModal] = useState(false);
  const [hangerModal, setHangerModal] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [currentVisibleIndex, setCurrentVisibleIndex] = useState(0);
  const likes = useSelector((state) => state.likes.likes);
  const commentCount = useSelector((state) => state.comments.commentCount);
  const savedCounts = useSelector(state => state.savedPosts.savedCounts);
  const saveCount = savedCounts[image?.id] || 0;
  const route = useRoute();

  // Get postId from either props or route params
  const effectivePostId = postId || (route.params && route.params.postId);
  const { currentPost } = useSelector(state => state.getPostDetail);

  // Get current user ID
  const currentUser = useSelector(state => state.auth.user);
  const currentUserId = currentUser?.id || currentUser?._id;

  // Local state for optimistic UI updates
  const [optimisticLikeCount, setOptimisticLikeCount] = useState(null);
  const [optimisticIsLiked, setOptimisticIsLiked] = useState(null);
  const [optimisticCommentCount, setOptimisticCommentCount] = useState(null);

  // Get the actual like status from Redux store
  const isLiked = optimisticIsLiked !== null
    ? optimisticIsLiked
    : (currentPost?.likes?.includes(currentUserId) || false);

  // Get the actual like count from Redux store or use optimistic value
  const displayLikeCount = optimisticLikeCount !== null
    ? optimisticLikeCount
    : (currentPost?.likeCount || 0);

  // Get the actual comment count from Redux store or use optimistic value
  const displayCommentCount = optimisticCommentCount !== null
    ? optimisticCommentCount
    : (currentPost?.commentCount || 0);

  // Get all posts for the scrollable section and ensure they're valid
  const postsToDisplay = useMemo(() => {
    const posts = allPosts || feedPosts || [];
    // Filter out any invalid posts
    return posts.filter(post =>
      post &&
      (post.image || post.uri || (post.originalData && post.originalData.image))
    );
  }, [allPosts, feedPosts]);

  // Get the currently visible post based on scroll position
  const currentVisiblePost = useMemo(() => {
    if (postsToDisplay && postsToDisplay.length > 0 && currentVisibleIndex < postsToDisplay.length) {
      return postsToDisplay[currentVisibleIndex];
    }
    return null;
  }, [postsToDisplay, currentVisibleIndex]);

  // Get the current post ID (either from the visible post or the initial post)
  const currentPostId = useMemo(() => {
    if (currentVisiblePost) {
      return currentVisiblePost._id || currentVisiblePost.id;
    }
    return effectivePostId;
  }, [currentVisiblePost, effectivePostId]);

  // Fetch post details when component mounts or currentPostId changes
  useEffect(() => {
    if (currentPostId) {
      // Only fetch post detail if we don't already have it in Redux or if it's a different post
      if (!currentPost || (currentPost._id !== currentPostId && currentPost.id !== currentPostId)) {
        console.log("Fetching post details with ID:", currentPostId);
        dispatch(fetchPostDetail(currentPostId));
      } else {
        console.log("Post detail already available in Redux, skipping fetch");
      }
    } else {
      console.warn("No postId available to fetch post details");
    }
  }, [currentPostId, dispatch, currentPost]);

  // Reset optimistic values when Redux state is updated
  useEffect(() => {
    if (currentPost) {
      // When Redux state changes and we have optimistic values for likes
      if (optimisticLikeCount !== null || optimisticIsLiked !== null) {
        // Check if the Redux state matches our optimistic prediction
        const reduxIsLiked = currentPost?.likes?.includes(currentUserId) || false;

        console.log('Comparing like states:', {
          optimisticLikeCount,
          reduxLikeCount: currentPost?.likeCount,
          optimisticIsLiked,
          reduxIsLiked
        });

        // If the Redux state has been updated to match our optimistic prediction,
        // we can safely reset the optimistic values
        if (optimisticLikeCount === currentPost?.likeCount &&
            optimisticIsLiked === reduxIsLiked) {
          console.log('Redux like state matches optimistic prediction, resetting optimistic values');
          setOptimisticLikeCount(null);
          setOptimisticIsLiked(null);
        }
      }

      // When Redux state changes and we have optimistic values for comments
      if (optimisticCommentCount !== null) {
        console.log('Comparing comment states:', {
          optimisticCommentCount,
          reduxCommentCount: currentPost?.commentCount
        });

        // If the Redux state has been updated to match our optimistic prediction,
        // we can safely reset the optimistic values
        if (optimisticCommentCount === currentPost?.commentCount) {
          console.log('Redux comment state matches optimistic prediction, resetting optimistic values');
          setOptimisticCommentCount(null);
        }
      }
    }
  }, [currentPost, optimisticLikeCount, optimisticIsLiked, optimisticCommentCount, currentUserId]);

  const handleLike = async () => {
    const postIdToLike = currentPost?.id || currentPost?._id || currentPostId;

    if (postIdToLike) {
      console.log(`[LikeButton] Butona tıklandı. Post ID: ${postIdToLike}`);

      // Calculate new optimistic values
      const newIsLiked = !isLiked;
      const newLikeCount = newIsLiked
        ? (currentPost?.likeCount || 0) + 1
        : Math.max((currentPost?.likeCount || 0) - 1, 0);

      // Log current state for debugging
      console.log('Before like action:', {
        currentLikeCount: currentPost?.likeCount,
        currentIsLiked: isLiked,
        newIsLiked,
        newLikeCount
      });

      // Update UI optimistically
      setOptimisticIsLiked(newIsLiked);
      setOptimisticLikeCount(newLikeCount);

      // Dispatch the action
      try {
        await dispatch(toggleLike(postIdToLike));
      } catch (error) {
        console.error('Error toggling like:', error);
        // Revert optimistic updates on error
        setOptimisticIsLiked(null);
        setOptimisticLikeCount(null);
      }
    } else {
      console.warn("Cannot like post: No post ID available");
    }
  };

  // Yorum butonuna tıklandığında
  const handleComment = () => {
    setCommentModal(true);
  };

  // Yorum gönderildiğinde
  const handleSubmitComment = (commentText) => {
    if (!currentPostId) {
      console.warn("Cannot add comment: No post ID available");
      return;
    }

    // Update comment count optimistically
    const currentCount = optimisticCommentCount !== null
      ? optimisticCommentCount
      : (currentPost?.commentCount || 0);
    setOptimisticCommentCount(currentCount + 1);

    // Create and dispatch the comment
    dispatch(addComment({
      postId: currentPostId,
      text: commentText
    }));

    setCommentModal(false);
  };

  const handleSave = () => {
    setCollectionModal(true);
    const postIdToSave = currentPost?.id || (image && image.id);

    if (postIdToSave) {
      if (savedPosts.includes(postIdToSave)) {
        dispatch(unsavePost(postIdToSave));
      } else {
        dispatch(savePost(postIdToSave));
      }
    } else {
      console.warn("Cannot save post: No post ID available");
    }
  };

  // Paylaş butonuna tıklandığında
  const handleShare = () => {
    setModalVisible(true);
  };

  // Handle share modal close - clear selected users when modal is closed
  const handleShareModalClose = () => {
    console.log('FullPostScreen: Share modal closing, clearing selected users:', selectedUsers);
    setSelectedUsers([]);
    setModalVisible(false);
  };

  const toggleHangerModal = () => {
    setHangerModal(!hangerModal);
  };

  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      navigation.goBack();
    }
  };
  // Function to render a post item
  const renderPostItem = ({ item }) => {
    if (!item) {
      console.log("Null or undefined item in renderPostItem");
      return null;
    }

    // Determine the image source
    const imageUri = item.image || item.uri || (item.originalData && item.originalData.image);
    if (!imageUri) {
      console.log("No image URI found for item:", item);
      return null;
    }

    // Get user data
    const user = item.user || (item.originalData && item.originalData.user) || {};
    const username = user.username || "user";
    const profilePicture = user.profilePicture || "https://via.placeholder.com/55";

    // Get post description and tags
    const description = item.description || (item.originalData && item.originalData.description) || "";
    const tags = item.tags || (item.originalData && item.originalData.tags) || [];

    return (
      <View style={styles.postContainer} pointerEvents="box-none">
        {/* Post Image */}
        <View style={styles.imageContainer} pointerEvents="box-none">
          <Image
            source={{ uri: imageUri }}
            style={styles.postImage}
            resizeMode="contain"
          />

          {/* Gradients */}
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0)']}
            style={styles.topGradient}
            pointerEvents="none"
          />
          <LinearGradient
            colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.7)']}
            style={styles.bottomGradient}
            pointerEvents="none"
          />

          {/* Close button - always visible even when hanger modal is open */}
          <TouchableOpacity
            onPress={handleClose}
            style={[styles.closeBtn, hangerModal && styles.visibleOnModal]}
          >
            <SvgCloseLight />
          </TouchableOpacity>

          {/* Action Buttons - Hidden when hanger modal is open */}
          {!hangerModal && (
            <View style={styles.action} pointerEvents="box-none">
              {/* Beğeni Butonu */}
              <TouchableOpacity style={styles.actionBtn} onPress={handleLike}>
                {isLiked ? <SvgHeartFill /> : <SvgHeart />}
                <Text style={styles.actionText}>{displayLikeCount}</Text>
              </TouchableOpacity>

              {/* Yorum Butonu */}
              <View>
                <TouchableOpacity style={styles.actionBtn} onPress={handleComment}>
                  <SvgComments />
                  <Text style={styles.actionText}>{displayCommentCount}</Text>
                </TouchableOpacity>
              </View>

              {/* Kaydet Butonu */}
              <View>
                <TouchableOpacity style={styles.actionBtn} onPress={handleSave}>
                  <SvgBookmark />
                  <Text style={styles.actionText}>{saveCount}</Text>
                </TouchableOpacity>
              </View>

              {/* Paylaş Butonu */}
              <View>
                <TouchableOpacity style={styles.actionBtn} onPress={handleShare}>
                  <SvgShare />
                  <Text style={styles.actionText}></Text>
                </TouchableOpacity>
              </View>

              {/* Hanger Button */}
              <View>
                <TouchableOpacity
                  style={styles.actionBtn}
                  onPress={toggleHangerModal}>
                  <SvgHanger />
                </TouchableOpacity>
              </View>
            </View>
          )}

          {/* User Profile - Hidden when hanger modal is open */}
          {!hangerModal && (
            <View style={styles.profile} pointerEvents="box-none">
              <TouchableOpacity
                style={styles.profileTouchable}
                onPress={() => {
                  // Get post user data
                  const postUser = user;
                  const postUserId = postUser?.id || postUser?._id;

                  // Convert IDs to strings for reliable comparison (handles ObjectId vs string)
                  const postUserIdStr = postUserId?.toString();
                  const currentUserIdStr = currentUserId?.toString();

                  // Compare IDs and navigate accordingly
                  console.log("Profile navigation - Post user ID:", postUserIdStr);
                  console.log("Profile navigation - Current user ID:", currentUserIdStr);
                  console.log("Profile navigation - Is same user:", currentUserIdStr === postUserIdStr);

                  // Close the modal first
                  if (onClose) {
                    onClose();
                  }

                  // Navigate after a short delay to ensure modal closes properly
                  setTimeout(() => {
                    if (currentUserIdStr && postUserIdStr && (currentUserIdStr === postUserIdStr)) {
                      // If it's the logged-in user, navigate to Profile screen
                      console.log("Navigating to own Profile screen");
                      navigation.navigate('Profile');
                    } else {
                      // If it's another user, navigate to SearchProfile screen
                      console.log("Navigating to SearchProfile screen for user:", postUser?.username);
                      navigation.navigate('SearchProfile', { user: postUser });
                    }
                  }, 300); // 300ms delay to allow modal close animation
                }}
                activeOpacity={0.7}
              >
                <View style={styles.profileContainer}>
                  <View style={styles.imageWrapper}>
                    <Image
                      source={{ uri: profilePicture }}
                      style={styles.profileImage}
                    />
                  </View>
                  <TouchableOpacity style={styles.plusButton}>
                    <SvgPlusPinkB />
                  </TouchableOpacity>
                </View>

                <View style={styles.title}>
                  <Text style={styles.username}>
                    @<Text style={styles.boldUsername}>
                      {username}
                    </Text>
                  </Text>
                  <Text style={styles.caption}>
                    {/* Parse description and style hashtags with pink color */}
                    {description.split(/(\s+)/).map((word, index) => {
                      if (word.startsWith('#')) {
                        return <Text key={index} style={styles.hashtag}>{word} </Text>;
                      }
                      return word;
                    })}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  };

  // Modals
  const renderModals = () => (
    <View pointerEvents="box-none" style={styles.modalContainer}>
      <CommentModal
        commentModal={commentModal}
        setCommentModal={setCommentModal}
        onSubmitComment={handleSubmitComment}
        postId={currentPostId}
        onCommentCountChange={(change) => {
          // Update optimistic comment count
          const currentCount = optimisticCommentCount !== null
            ? optimisticCommentCount
            : (currentPost?.commentCount || 0);
          setOptimisticCommentCount(Math.max(0, currentCount + change));
        }}
      />
      <CollectionsModal
        collectionModal={collectionModal}
        setCollectionModal={setCollectionModal}
      />
      <ShareModal
        modalVisible={modalVisible}
        setModalVisible={handleShareModalClose}
        selectedUsers={selectedUsers}
        setSelectedUsers={setSelectedUsers}
      />
      <HangerModal
        hangerModal={hangerModal}
        setHangerModal={toggleHangerModal}
        post={currentPost || image}
      />
    </View>
  );

  // Create a ref for the FlatList
  const flatListRef = useRef(null);

  // Find the index of the selected post
  const selectedIndex = useMemo(() => {
    if (!image || !postsToDisplay || postsToDisplay.length === 0) return 0;

    const imageId = image._id || image.id;
    console.log("Looking for post with ID:", imageId);

    const index = postsToDisplay.findIndex(post => {
      if (!post) return false;

      const postId = post._id || post.id;
      const originalPostId = post.originalData && (post.originalData._id || post.originalData.id);

      // Try to match with any of the possible IDs
      const match =
        postId === imageId ||
        originalPostId === imageId ||
        String(postId) === String(imageId) ||
        String(originalPostId) === String(imageId);

      if (match) console.log("Found matching post at index:", postsToDisplay.indexOf(post));
      return match;
    });

    return index >= 0 ? index : 0;
  }, [image, postsToDisplay]);

  // Initialize currentVisibleIndex with selectedIndex
  useEffect(() => {
    setCurrentVisibleIndex(selectedIndex);
  }, [selectedIndex]);

  // Reset optimistic values when the visible post changes
  useEffect(() => {
    // Reset optimistic values when scrolling to a different post
    setOptimisticLikeCount(null);
    setOptimisticIsLiked(null);
    setOptimisticCommentCount(null);
  }, [currentPostId]);

  // Scroll to the selected post when the component mounts
  useEffect(() => {
    if (flatListRef.current && selectedIndex > 0 && postsToDisplay.length > 0) {
      console.log("Scrolling to index:", selectedIndex);
      setTimeout(() => {
        // Double check that the ref is still valid and the index is within bounds
        if (flatListRef.current && selectedIndex < postsToDisplay.length) {
          try {
            flatListRef.current.scrollToIndex({
              index: selectedIndex,
              animated: false,
              viewPosition: 0
            });
          } catch (error) {
            console.warn('Error scrolling to index:', error);
            // Fallback: try scrolling with a longer delay
            setTimeout(() => {
              if (flatListRef.current && selectedIndex < postsToDisplay.length) {
                try {
                  flatListRef.current.scrollToIndex({
                    index: selectedIndex,
                    animated: false,
                    viewPosition: 0
                  });
                } catch (retryError) {
                  console.warn('Retry scroll failed:', retryError);
                }
              }
            }, 500);
          }
        }
      }, 100);
    }
  }, [selectedIndex, postsToDisplay.length]);

  // Handle viewable items change to track current visible post
  const onViewableItemsChanged = useRef(({ viewableItems }) => {
    if (viewableItems && viewableItems.length > 0) {
      const visibleItem = viewableItems[0];
      if (visibleItem && visibleItem.index !== null && visibleItem.index !== undefined) {
        console.log("Visible item changed to index:", visibleItem.index);
        setCurrentVisibleIndex(visibleItem.index);
      }
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50, // Item is considered visible when 50% is visible
  }).current;

  // Log data for debugging
  console.log("FullPostScreen postsToDisplay length:", postsToDisplay?.length);
  console.log("FullPostScreen image:", image?._id || image?.id);
  console.log("FullPostScreen selectedIndex:", selectedIndex);
  console.log("FullPostScreen currentVisibleIndex:", currentVisibleIndex);
  console.log("FullPostScreen currentPostId:", currentPostId);

  return (
    <View style={styles.mainContainer}>
      <FlatList
        ref={flatListRef}
        data={postsToDisplay}
        keyExtractor={(item) => item?._id?.toString() || item?.id?.toString() || Math.random().toString()}
        renderItem={renderPostItem}
        showsVerticalScrollIndicator={false}
        snapToInterval={Dimensions.get('window').height}
        decelerationRate="fast"
        snapToAlignment="start"
        pagingEnabled
        initialNumToRender={3}
        maxToRenderPerBatch={3}
        windowSize={3}
        updateCellsBatchingPeriod={50}
        removeClippedSubviews={true}
        initialScrollIndex={selectedIndex}
        scrollEnabled={true}
        bounces={true}
        onViewableItemsChanged={onViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        getItemLayout={(_, index) => ({
          length: Dimensions.get('window').height,
          offset: Dimensions.get('window').height * index,
          index,
        })}
        onScrollToIndexFailed={(info) => {
          console.log("Scroll to index failed:", info);
          // Try again with a delay, but with better error handling
          setTimeout(() => {
            if (flatListRef.current && info.index < postsToDisplay.length) {
              try {
                flatListRef.current.scrollToIndex({
                  index: info.index,
                  animated: false,
                  viewPosition: 0
                });
              } catch (retryError) {
                console.warn("Retry scroll to index failed:", retryError);
                // As a last resort, try scrolling to offset
                try {
                  const offset = info.index * Dimensions.get('window').height;
                  flatListRef.current.scrollToOffset({
                    offset: offset,
                    animated: false
                  });
                } catch (offsetError) {
                  console.warn("Scroll to offset also failed:", offsetError);
                }
              }
            }
          }, 500);
        }}
      />
      {renderModals()}
    </View>
  );
};

export default FullPostScreen;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: '#000',
    position: 'relative',
  },
  postContainer: {
    height: Dimensions.get('window').height,
    width: Dimensions.get('window').width,
    position: 'relative',
  },
  imageContainer: {
    flex: 1,
    position: 'relative',
  },
  postImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  topGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 180,
  },
  bottomGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 180,
  },
  closeBtn: {
    position: 'absolute',
    top: 80,
    right: 30,
    zIndex: 10,
  },
  visibleOnModal: {
    zIndex: 150, // Higher zIndex to ensure it's above the modal
  },
  action: {
    position: 'absolute',
    zIndex: 10,
    bottom: 60,
    right: 20,
    gap: 16,
  },
  actionBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  profile: {
    position: 'absolute',
    zIndex: 10,
    bottom: 60,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  imageWrapper: {
    position: 'relative',
    width: 55,
    height: 55,
    borderRadius: 50,
    overflow: 'hidden',
  },
  profileImage: {
    width: 55,
    height: 55,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: '#FFF',
  },
  plusButton: {
    position: 'absolute',
    top: -3,
    right: -3,
    width: 19,
    height: 19,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    maxWidth: 220,
    flexDirection: 'column',
  },
  username: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '600',
  },
  boldUsername: {
    fontWeight: '700',
  },
  caption: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '400',
  },
  hashtag: {
    color: '#FFC2F0',
    fontSize: 14,
    fontWeight: '400',
  },
  modalContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 100,
    pointerEvents: 'box-none',
  },
});