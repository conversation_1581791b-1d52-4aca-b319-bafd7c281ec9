import {useState, useEffect, useMemo, useCallback, useRef} from 'react';
import {
  View,
  StyleSheet,
  Modal,
  RefreshControl,
  ScrollView,
  FlatList,
  Dimensions,
  TouchableWithoutFeedback,
  useWindowDimensions,
  Text,
  TouchableOpacity,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import MasonryList from 'react-native-masonry-list';
import {height, imageData, width} from '../../utils/helpers';
import ImageCarousel from '../../components/Home/ImageCarousel';
import HomeSkeleton from '../../components/Home/HomeSkeleton';
import Header from '../../components/Home/Header';
import FullPostScreen from './FullPostScreen';
import Loader from '../../components/Loader';
import { useDispatch, useSelector } from 'react-redux';
import { getUserInfo } from '../../redux/actions/userActions';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ScrollPostScreen from './ScrollPostScreen';
import { useNavigation, useRoute } from '@react-navigation/native';
import { fetchFeedPosts } from '../../redux/actions/postActions';
import { fetchDiscoverPosts } from '../../redux/actions/discoverActions';
import { fetchPostDetail } from '../../redux/actions/getPostDetailActions';
import UserPost from '../../components/Profile/UserPost';
import { prefetchImages } from '../../utils/imagePrefetch';

// const getImageSize = async image => {
//   return {
//     width: Math.floor((width - 30) / 2),
//     height: Math.floor(Math.random() * 100) + 250,
//   };
// };

const getImageSize = async image => {
  return {
    width: Math.floor((width - 30) / 2),
    height: Math.floor(Math.random() * 100) + 250,
  };
};



const HomeScreen = () => {
  const {isLogin}=useSelector(state=>state.auth)
  const dispatch=useDispatch()
  const [formattedImages, setFormattedImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [activeTab, setActiveTab] = useState('Takip'); // Track active tab



  // const handleRefresh = () => {
  //   setFirstRefresh(false);
  //   onRefresh();
  // };
  // const handleRefresh = async () => {
  //   setRefreshing(true);
  //   try {
  //     await dispatch(fetchFeedPosts()).unwrap();
  //   } catch (error) {
  //     console.error('Refresh error:', error);
  //   } finally {
  //     setRefreshing(false);
  //   }
  // };
  // Get posts from Redux store
  const { feedPosts } = useSelector(state => state.posts);
  const { discoverPosts } = useSelector(state => state.discover);


  // const loadImages = async () => {
  //   setLoading(true);
  //   try {
  //     const resultAction = await dispatch(fetchFeedPosts());

  //     if (fetchFeedPosts.fulfilled.match(resultAction)) {
  //       const feedPosts = resultAction.payload;

  //       const updatedImages = await Promise.all(
  //         feedPosts.map(async (post) => {
  //           const imageUrl = post.image || (post.images?.[0]);

  //           const {width, height} = await getImageSize(imageUrl);

  //           return {
  //             id: post._id || post.id,
  //             uri: imageUrl,
  //             width,
  //             height,
  //             images: post.images || [post.image].filter(Boolean),
  //             username: post.user?.username || 'Unknown',
  //             description: post.description || '',
  //             tags: post.tags || [],
  //             userProfilePicture: post.user?.profilePicture,
  //           };
  //         })
  //       );

  //       setFormattedImages(updatedImages);
  //     }
  //   } catch (error) {
  //     console.error('Error loading images:', error);
  //   } finally {
  //     setLoading(false);
  //     setRefreshing(false);
  //   }
  // };

  // useEffect(() => {
  //   loadImages();
  // }, []);

  const getToken=async ()=>{
    const token =await AsyncStorage.getItem("token")
    console.log("tokenim",token)
  }

useEffect(() => {
  const checkToken = async () => {
    const token = await AsyncStorage.getItem("token");
    console.log("Current token:", token ? "Available" : "Not available");
  };

  checkToken();

  // Only fetch user info if logged in
  if (isLogin) {
    dispatch(getUserInfo({}));
  }
}, [isLogin, dispatch])

  // Define loadImages function to handle data fetching based on active tab
  const loadImages = useCallback(async () => {
    if (refreshing) return; // Prevent multiple simultaneous refreshes

    setRefreshing(true);
    try {
      if (activeTab === 'Takip') {
        await dispatch(fetchFeedPosts()).unwrap();
      } else if (activeTab === 'Sizin İçin') {
        await dispatch(fetchDiscoverPosts()).unwrap();
      }
    } catch (error) {
      console.error('Error loading images:', error);
    } finally {
      setRefreshing(false);
    }
  }, [dispatch, refreshing, activeTab]);

  const onRefresh = useCallback(() => {
    if (!refreshing) {
      loadImages();
    }
  }, [loadImages, refreshing]);

  // Simple scroll handler without triggering refresh
  const onScroll = useCallback(() => {
    // We're just keeping this for the scrollEventThrottle
    // No need to update any state based on scroll position
  }, []);

  // const handleOpenImage = (image) => {
  //   // 1. Önce FlatList'i sıfırla (scroll'u en başa al)
  //   flatListRef.current?.scrollToOffset({ offset: 0, animated: false });

  //   // 2. Modal'ı kapat ve hemen yeni resmi aç (50ms gecikmeyle)
  //   setSelectedImage(null);
  //   setTimeout(() => {
  //     setSelectedImage(image);
  //   }, 50);
  // };

  // const handleOpenImage = (image) => {
  //   console.log('handleOpenImage tetiklendi', image);
  //   flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
  //   setSelectedImage(image);
  //   console.log('selectedImage güncellendi:', image);
  // };
  const handleOpenImage = (item) => {
    setSelectedImage(item);
  };

  const navigation = useNavigation()
  // const handleOpenImage = (image) => {
  //   navigation.navigate('ScrollPost', {
  //     allPosts,
  //     formattedImages,
  //     selectedImage: image,
  //     extraData: dataToScroll
  //   });
  // };
  // useEffect(() => {
  //   console.log("useEffect çalıştı!");
  // }, [dispatch]);

  // useEffect(() => {
  //   dispatch(fetchFeedPosts());
  // }, []);

  // const formattedPosts = feedPosts.map(post => ({
  //   id: post._id || post.id,
  //   image: {
  //     uri: post.image, // API'den gelen direkt image alanı
  //     width: 300,      // Varsayılan değerler
  //     height: 400
  //   },
  //   user: {
  //     username: post.user?.username,
  //     profilePicture: post.user?.profilePicture
  //   }
  // }));




  // Kullanım:
  // const formattedData = formattedPosts(feedPosts);
  // console.log('Formatlanmış veri:', formattedData[0]); // Kontrol edin
  // console.log("FormattedData:", formattedData);
//   console.log("feedposts", feedPosts)
// console.log("formatted", formattedPosts)
  // const customImageComponent = useMemo(
  //   () => ({ item }) => {
  //     // If item is undefined, return null or a placeholder
  //     if (!item) {
  //       return (
  //         <View style={{ width: 150, height: 150, backgroundColor: '#f0f0f0' }} />
  //       );
  //     }

  //     const handlePress = () => {
  //       handleOpenImage(item);
  //     };

  //     return (
  //       <ImageCarousel
  //         posts={feedPosts}
  //         onPress={handlePress}
  //       />
  //     );
  //   },
  //   [handleOpenImage],
  // );

  const customImageComponent = useCallback(
    ({ item }) => {
      console.log("Custom image component item:", item);

      return (
        <ImageCarousel
          item={item}
          onPress={() => handleOpenImage(item)}
        />
      );
    },
    []
  );
 //       <View style={styles.container}>
  //       <View style={styles.header}>
  //         <View style={styles.userInfo}>
  //         <Image
  //             source={
  //               {uri: item.user.profilePicture}
  //             }
  //             style={styles.userImage}
  //           />
  //           <Text style={{fontSize: 12}}>{item.user.username || 'Unknown'}</Text>
  //         </View>
  //         <View>
  //           <TouchableOpacity
  //             onPress={() => setModalVisible(true)}
  //             activeOpacity={0.7}>
  //             <SvgMore />
  //           </TouchableOpacity>
  //           <ShareBottomSheet
  //             modalVisible={modalVisible}
  //             setModalVisible={setModalVisible}
  //           />
  //         </View>
  //       </View>

  //       <ScrollView
  //         horizontal
  //         showsHorizontalScrollIndicator={false}
  //         showsVerticalScrollIndicator={false}
  //         pagingEnabled={true}
  //         style={{marginBottom: 5, width: item.width}}>
  //         {(item.images || []).map((img, index) => (
  //           <TouchableOpacity
  //             key={`${item.id}_${index}`}
  //             onPress={() => onPress(item, index)}
  //             activeOpacity={1}>
  //             <Image
  //               // source={getImageSource(img)}
  // source={{uri: item.image.uri}}
  //               style={{
  //                 // width: item.width,
  //                 // height: item.height,
  //                 width:item.image.width,
  //                 height:item.image.height,
  //                 borderRadius: 10,
  //               }}
  //               resizeMode="cover"
  //             />
  //           </TouchableOpacity>
  //         ))}
  //       </ScrollView>
  //     </View>
  // useEffect(() => {
  //   console.log('Formatted images:', formattedImages);
  // }, [formattedImages]);



  const dataToScroll = formattedImages.map(image => ({
    username: image.username,
    description: image.description,
    tags: image.tags ? image.tags.join(', ') : '',
  }));
  const masonryRef = useRef(null);
  const [previousScrollY, setPreviousScrollY] = useState(0);
  const handleScrollCloseModal = (event) => {
    const contentOffsetY = event.nativeEvent.contentOffset.y;

    // Eğer aşağı kaydırılıyorsa ve 100 px geçmişse modalı kapat
    if (contentOffsetY < previousScrollY && contentOffsetY > 100) {
      setSelectedImage(null);
    }

    setPreviousScrollY(contentOffsetY);
  };


  const posts = useSelector(state => state.posts.posts);

  useEffect(() => {
    console.log("Redux'taki postlar:", posts);

    // Prefetch images when posts are loaded
    if (posts && posts.length > 0) {
      const imageUrls = posts
        .filter(post => post && post.image)
        .map(post => post.image);

      const profileImageUrls = posts
        .filter(post => post && post.user && post.user.profilePicture)
        .map(post => post.user.profilePicture);

      // Prefetch post images with normal priority
      prefetchImages(imageUrls, 'normal');

      // Prefetch profile images with low priority
      prefetchImages(profileImageUrls, 'low');
    }
  }, [posts]);

  // const allPosts = useMemo(() => {
  //   return [...formattedImages, ...posts];
  // }, [formattedImages, posts]);


  useEffect(() => {
    console.log("Tüm Postlar (formattedImages + posts):", allPosts);
  }, [allPosts]);

  const { height: screenHeight, width: screenWidth } = useWindowDimensions();

  const flatListRef = useRef(null);

  useEffect(() => {
    if (selectedImage) {
      const index = formattedImages.findIndex(img => img.id === selectedImage.id);
      flatListRef.current?.scrollToIndex({ index, animated: false });
    }
  }, [selectedImage]);



  const handleCloseModal = () => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
    setSelectedImage(null);
  };
  const route= useRoute()

  const safeScrollToIndex = (index) => {
    if (!flatListRef.current || index < 0 || !allPosts || allPosts.length === 0) return;

    // Ensure the index is within bounds
    const safeIndex = Math.max(0, Math.min(index, allPosts.length - 1));

    flatListRef.current.scrollToIndex({
      index: safeIndex,
      animated: true,
      viewPosition: 0.5 // Scroll to middle of the item
    });
  };
  useEffect(() => {
    if (selectedImage && allPosts && allPosts.length > 0) {
      const index = allPosts.findIndex(post =>
        post.id === selectedImage.id ||
        post._id === selectedImage.id
      );

      if (index !== -1) {
        safeScrollToIndex(index);
      } else {
        console.warn("Selected image not found in allPosts");
      }
    }
  }, [selectedImage, allPosts]);





// const formattedPosts = feedPosts.map(post => ({
//   // MasonryList'in ZORUNLU alanları
//   uri: post.image, // Resim URL'si doğrudan root'ta
//   dimensions: {
//     width: Math.floor((width - 30) / 2),
//     height: Math.floor(Math.random() * 100) + 250,
//   },
//   id: post._id,

//   // Kullanıcı bilgilerini doğrudan root'a alıyoruz
//   username: post.user?.username,
//   profilePicture: post.user?.profilePicture,

//   // Orijinal veriyi korumak için (opsiyonel)
//   originalData: post
// }));
// Format posts based on active tab
const formattedPosts = useMemo(() => {
  // Determine which posts to use based on active tab
  const postsToFormat = activeTab === 'Takip' ? feedPosts : discoverPosts;

  return postsToFormat
    .filter(post => post && post._id && post.image && post.user)
    .map(post => ({
      uri: post.image,
      dimensions: {
        width: Math.floor((width - 30) / 2),
        height: Math.floor(Math.random() * 100) + 250,
      },
      id: post._id,
      username: post.user?.username,
      profilePicture: post.user?.profilePicture,
      originalData: post
    }));
}, [feedPosts, discoverPosts, activeTab]);

  const allPosts = useMemo(() => {
    // Filter out any null or undefined items
    const validFormattedImages = formattedImages.filter(item => item && (item.image || item.uri));
    const validFormattedPosts = formattedPosts.filter(item => item && (item.image || item.uri));

    // Combine and return
    return [...validFormattedImages, ...validFormattedPosts];
  }, [formattedImages, formattedPosts]);

  console.log("HOMESCREEN POST", allPosts)
  console.log("HOMESCREEN FORMATTED IMAGE", formattedImages)
  console.log("HOMESCREEN FORMATTED POST", formattedPosts)

  const handleImagePress = (postData, imageIndex) => {
    if (!postData || !allPosts.length) return;

    const targetPost = allPosts.find(post =>
      post._id === postData._id ||
      post.id === postData.id
    );

    if (!targetPost) {
      console.warn("Post bulunamadı:", postData);
      return;
    }
   console.log("resim index", imageIndex);
   console.log("target post", targetPost);

   // Fetch post details before showing the modal
   dispatch(fetchPostDetail(targetPost._id || targetPost.id));

    // Make sure we have both id and _id for consistency
    const selectedPost = {
      ...targetPost,
      id: targetPost.id || targetPost._id,
      _id: targetPost._id || targetPost.id,
      currentImageIndex: imageIndex,
      images: Array.isArray(targetPost.images)
        ? targetPost.images
        : [targetPost.image || targetPost.uri]
    };

    console.log("Setting selected image with ID:", selectedPost.id || selectedPost._id);
    setSelectedImage(selectedPost);
  };
  // const handleImagePress = (postData, imageIndex) => {
  //   navigation.navigate('FullPost', {
  //     postId: postData._id || postData.id,
  //     initialIndex: imageIndex,

  //     allPosts: allPosts // Tüm postları geçirin
  //   });
  // };

  // const handleImagePress = async (item) => {
  //   try {
  //     if (!userId) {
  //       throw new Error("User ID is missing in the post data");
  //     }

  //     // Find the index of the clicked post in the existing posts array
  //     const postIndex = posts.findIndex(post => post._id === item._id);

  //     if (postIndex === -1) {
  //       throw new Error("Post not found in the current posts array");
  //     }

  //     navigation.navigate('UserPost', {
  //       postId: item._id,
  //       selectedImage: item,
  //       initialIndex: postIndex,
  //       userPosts: posts // Pass the entire posts array if needed in UserPost screen
  //     });
  //   } catch (error) {
  //     console.error('Failed to navigate to post details:', error.message);
  //   }
  // };
  console.log("Tüm ID'ler:", allPosts.map(p => p._id || p.id));
  // console.log("Aranan ID:", selectedImage?._id || selectedImage?.id);
  // console.log("Bulunan Index:", allPosts.findIndex(post =>
  //   String(post._id) === String(selectedImage?._id) ||
  //   String(post.id) === String(selectedImage?.id))
  // );
  useEffect(() => {
    if (selectedImage && flatListRef.current && allPosts.length > 0) {
      const index = allPosts.findIndex(post =>
        String(post._id) === String(selectedImage._id)
      );

      if (index >= 0) {
        setTimeout(() => {
          flatListRef.current?.scrollToIndex({
            index,
            animated: false,
            viewPosition: 0.5
          });
        }, 50);
      }
    }
  }, [selectedImage, allPosts]);



console.log("allPosts:", allPosts);


  // Initial data loading - only once when component mounts
  useEffect(() => {
    let isMounted = true;

    const initialLoad = async () => {
      if (loading || refreshing) return; // Prevent duplicate loading

      setLoading(true);
      try {
        // Load data based on active tab
        if (activeTab === 'Takip') {
          await dispatch(fetchFeedPosts()).unwrap();
        } else if (activeTab === 'Sizin İçin') {
          await dispatch(fetchDiscoverPosts()).unwrap();
        }
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        // Only update state if component is still mounted
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    initialLoad();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [activeTab]); // Re-run when active tab changes

  useEffect(() => {
    if (flatListRef.current && selectedImage) {
      console.log("FlatList referansı hazır");
    }
  }, [selectedImage]);
//   console.log("Render anı - allPosts:", allPosts.length);
// console.log("selectedImage durumu:", !!selectedImage);


return (
<View style={styles.container}>
  <Header onTabChange={setActiveTab} />
  {loading ? (
    <HomeSkeleton />
  ) : (
    <>
      {/* Remove the loader that appears during refresh since we're showing HomeSkeleton in the ScrollView */}

      <ScrollView
        nestedScrollEnabled
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={onScroll}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#9E9E9E']}
            tintColor={'#9E9E9E'}
            progressBackgroundColor={'#fff'}
          />
        }
      >
        {refreshing ? (
          <HomeSkeleton />
        ) : formattedPosts?.length > 0 ? (
          <MasonryList
            images={formattedPosts}
            initialScrollIndex={0}
            keyExtractor={(item) => item.id}
            columns={2}
            spacing={2}
            masonryFlatListColProps={{
              showsVerticalScrollIndicator: false,
              scrollEventThrottle: 16,
              removeClippedSubviews: true,
              initialNumToRender: 8,
              maxToRenderPerBatch: 8,
              windowSize: 5,
              updateCellsBatchingPeriod: 50,
              onEndReachedThreshold: 0.5,
            }}
            completeCustomComponent={({ data, style }) => (
              <ImageCarousel
                item={data}
                onPress={handleImagePress}
                style={style}
              />
            )}
          />
        ) : activeTab === 'Takip' ? (
          <View style={styles.emptyStateContainer}>
            <Text style={styles.emptyStateText}>Henüz kimseyi takip etmiyorsunuz</Text>
          </View>
        ) : (
          <Loader />
        )}
      </ScrollView>
    </>
  )}

  {selectedImage && (
    <Modal
      key={selectedImage?.id || "default"}
      visible={!!selectedImage}
      animationType="slide"
      onRequestClose={() => setSelectedImage(null)}
      transparent={true}
      statusBarTranslucent={true}
    >
      <View style={{ flex: 1 }}>
        <FullPostScreen
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
          postId={selectedImage._id || selectedImage.id}
          allPosts={allPosts}
        />
      </View>
    </Modal>
  )}
</View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    // paddingHorizontal: 5,
    paddingTop: Platform.OS === 'android' && 5,
  },
  masonry: {
    flex: 1,
    paddingHorizontal: 50,
    paddingVertical: 20,
  },
  loader: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  loaderContainer: {
    position: 'absolute',
    top: 10,
    left: 0,
    right: 0,
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: Math.floor((width - 30) / 2),
    marginBottom: 8,
    marginTop: 15,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 7,
  },
  userImage: {
    width: 24,
    height: 24,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
    paddingHorizontal: 20,
    minHeight: 300,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    fontFamily: 'Poppins-Regular',
  },
});

{
  /*

  <Modal
          visible={!!selectedImage}
          animationType="slide"
          onRequestClose={() => setSelectedImage(null)}>
          <FullPostScreen
            image={selectedImage}
            onClose={() => setSelectedImage(null)}
            extraData={dataToScroll}
          />
        </Modal>
  */
}


// {formattedPosts.length > 0 ? (
//   <MasonryList
//   images={formattedPosts} // 'images' yerine 'data' kullanıyoruz
//   renderItem={({item}) => {
//     console.log("Render Item:", item); // Artık çalışacak

//     return (
//       <View style={{margin: 5}}>
//         {/* Kullanıcı bilgileri */}
//         <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 8}}>
//           <Image
//             source={{uri: item.profilePicture}}
//             style={{width: 30, height: 30, borderRadius: 15}}
//           />
//           <Text style={{marginLeft: 8}}>{item.username}</Text>
//         </View>

//         {/* Resim */}
//         <Image
//           source={{uri: item.uri}}
//           style={{
//             width: '100%',
//             height: undefined,
//             aspectRatio: item.dimensions.width / item.dimensions.height,
//             borderRadius: 8
//           }}
//           resizeMode="cover"
//         />

//         {/* Opsiyonel: Açıklama */}
//         {item.originalData?.description && (
//           <Text style={{marginTop: 5}}>{item.originalData.description}</Text>
//         )}
//       </View>
//     );
//   }}
//   numColumns={2} // 'columns' değil
//   keyExtractor={(item) => item.id}
//   showsVerticalScrollIndicator={false}
//  />
//  ) : (
//    <Text>Gönderi bulunamadı</Text>
//  )}




//     <View style={styles.container}>
//       <Header />
//       <>
//         {loading ? (
//           <HomeSkeleton />
//         ) : (
//           <>
//             {refreshing && !firstRefresh ? (
//               <View style={styles.loaderContainer}>
//                 <Loader />
//               </View>
//             ) : null}

//             <ScrollView
//               nestedScrollEnabled={true}
//               keyboardShouldPersistTaps="handled"
//               scrollEventThrottle={16}
//               onScroll={onScroll}
//               showsVerticalScrollIndicator={false}
//               refreshControl={
//                 <RefreshControl
//                   refreshing={refreshing}
//                   // onRefresh={handleRefresh}
//                   tintColor="transparent"
//                   colors={['transparent']}
//                 />
//               }>

// {formattedPosts && formattedPosts.length > 0 ? (
//   <MasonryList
//     images={formattedPosts}
//     initialScrollIndex={0}
//     keyExtractor={(item) => item.id}
//     columns={2}
//     completeCustomComponent={({ data, style }) => (
//       <ImageCarousel
//         item={data}
//         onPress={handleImagePress}
//         style={style}
//       />
//     )}
//   />
// ) : (
//   <Loader/>
// )}
//             </ScrollView>
//           </>
//         )}
//       </>
//       {shouldRenderModal &&  (
//   <Modal
//     key={selectedImage?.id || "default"}
//     visible={!!selectedImage}
//     animationType="slide"
//     onRequestClose={handleCloseModal}
//     transparent={true}
//   >
//     {allPosts && allPosts.length > 0 ? (
//       <View style={{ flex: 1 }}>
//       <FlatList
//   data={allPosts}
//   keyExtractor={(item) => item._id || item.id}
//   onScroll={handleScrollCloseModal}
//   showsVerticalScrollIndicator={false}
//   pagingEnabled
//   style={{ flex: 1 }}
//   snapToAlignment="start"
//   decelerationRate="fast"
//   getItemLayout={(data, index) => ({
//     length: screenHeight,
//     offset: screenHeight * index,
//     index,
//   })}
//   ref={flatListRef}
//   initialScrollIndex={0} // Artık önemsiz, useEffect bunu yönetecek
//   onScrollToIndexFailed={() => {}}
//   renderItem={({ item }) => (
//     <View style={{ height: screenHeight }}>
//       <FullPostScreen
//         image={item}
//         onClose={() => setSelectedImage(null)}
//         extraData={dataToScroll}
//         postId={item._id || item.id}
//       />
//     </View>
//   )}
// />
//       </View>
//     ) : (
//       <Text style={{ textAlign: "center", marginTop: 20 }}>Yükleniyor...</Text>
//     )}
//   </Modal>
// )}
//      {/* {selectedImage && (
//   console.log('ScrollPostScreen render ediliyor, selectedImage:', selectedImage),
//   <View style={{
//     position: 'absolute',
//     top: 0,
//     left: 0,
//     right: 0,
//     bottom: 0,
//     zIndex: 100,
//     flex:1
//   }}>
//   <ScrollPostScreen
//     allPosts={allPosts}
//     formattedImages={formattedImages}
//     selectedImage={selectedImage}
//     onClose={() => {
//       console.log('ScrollPostScreen kapatıldı');
//       setSelectedImage(null);
//     }}
//     extraData={dataToScroll}
//   />
//   </View>
// )} */}
//     </View>


{/* <View style={styles.container}>
<Header />
{loading ? (
  <HomeSkeleton />
) : (
  <>
    {refreshing && !firstRefresh && (
      <View style={styles.loaderContainer}>
        <Loader />
      </View>
    )}

    <ScrollView
      nestedScrollEnabled
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      {formattedPosts?.length > 0 ? (
         <MasonryList
             images={formattedPosts}
             initialScrollIndex={0}
             keyExtractor={(item) => item.id}
             columns={2}
             completeCustomComponent={({ data, style }) => (
               <ImageCarousel
                 item={data}
                 onPress={handleImagePress}
                 style={style}
               />
             )}
           />
      ) : (
        <Loader />
      )}
    </ScrollView>
  </>
)}
</View> */}