import React, {useContext, useEffect, useRef, useState, useMemo, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Platform,
} from 'react-native';
import {messageList} from '../../utils/helpers';
import {useNavigation} from '@react-navigation/native';
import Swipeable from 'react-native-gesture-handler/Swipeable';
import SvgTrash from '../../assets/trash';
import Loader from '../../components/Loader';
import { MessageContext } from '../../components/Message/MessageContext';
import LinearGradient from 'react-native-linear-gradient';
import { fetchConversations, deleteConversation } from '../../redux/actions/conversationsActions';
import { deleteMessage } from '../../redux/actions/messagesActions';
import { useDispatch, useSelector } from 'react-redux';
import { getReadMessages, markMessageAsRead } from '../../utils/readMessageStorage';
import api from '../../api/api';

const formatTimeAgo = timestamp => {
  const now = new Date();
  const messageDate = new Date(timestamp);
  const diffInSeconds = Math.floor((now - messageDate) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInDays > 0) {
    return `${diffInDays}g`;
  } else if (diffInHours > 0) {
    return `${diffInHours}s`;
  } else if (diffInMinutes > 0) {
    return `${diffInMinutes}d`;
  } else {
    return `Şimdi`;
  }
};

// const getDisplayMessage = (item, isOpened) => {
//   console.log("MESSAGELIST ITEM",item)
//   // Handle different data structures between local messages and API conversations
//   // if (!item) return '1 yeni mesaj';

//   // For local messages (chatbot)
//   if (item.messages && Array.isArray(item.messages)) {
//     if (item.messages.length === 0) return '1 yeni mesaj';

//     // const lastMessage = item.lastMessage.text;
//     const messageCount = item.messageCount;
//     const messageText = item.lastMessage.text;
//     const timestamp = item.updatedAt || new Date();
//     const timeAgo = formatTimeAgo(timestamp);

//     let textPreview;

//     if (isOpened) {
//       // If messages have been read/opened, show the last message content
//       textPreview = messageText.length > 30
//         ? messageText.substring(0, 30) + '...'
//         : messageText;
//     } else {
//       // If messages are unread, show the number of unread messages
//       if (messageCount === 1) {
//         return '1 yeni mesaj';
//       } else if (messageCount <= 3) {
//         return `${messageCount} yeni mesaj`;
//       } else {
//         return '4+ yeni mesaj';
//       }
//     }

//     return (
//       <View style={styles.messageWrapper}>
//         <Text style={[styles.messageText, isOpened && styles.readMessage]}>
//           {textPreview}
//         </Text>
//         {!isOpened && (
//           <>
//             <Text style={styles.dot}> • </Text>
//             <Text style={styles.timeAgo}>{timeAgo}</Text>
//           </>
//         )}
//       </View>
//     );
//   }

//   // Debug the item structure
//   console.log('getDisplayMessage item:', {
//     hasLastMessage: !!item.lastMessage,
//     lastMessageType: item.lastMessage ? typeof item.lastMessage : 'none',
//     lastMessageValue: item.lastMessage
//   });

//   // For API conversations
//   // Check if lastMessage exists in the conversation
//   if (item.lastMessage) {
//     // Try to get the message text - this might be in different formats depending on API
//     let messageText = '';
//     let timestamp = new Date();

//     // If lastMessage is an object with text property
//     if (typeof item.lastMessage === 'object' && item.lastMessage !== null) {
//       messageText = item.lastMessage.text || '1 yeni mesaj';
//       timestamp = item.lastMessage.date || item.lastMessageDate || item.updatedAt || new Date();
//     } else if (typeof item.lastMessage === 'string') {
//       // If lastMessage is a string (message ID), use it as is
//       messageText = item.lastMessage || '1 yeni mesaj';
//       timestamp = item.lastMessageDate || item.updatedAt || new Date();
//     }

//     // Process the message if we have one
//     if (true) {
//       const timeAgo = formatTimeAgo(timestamp);

//       // Get the message count from the API conversation
//       // Try to use unreadCount or messageCount if available, otherwise use messages array length, or default to 1
//       const messageCount = item.unreadCount || item.messageCount || (item.messages && Array.isArray(item.messages) ? item.messages.length : 1);

//       // Debug log for message count
//       console.log('Message count for conversation:', {
//         conversationId: item._id,
//         unreadCount: item.unreadCount,
//         messageCount: item.messageCount,
//         messagesArrayLength: item.messages && Array.isArray(item.messages) ? item.messages.length : 'N/A',
//         finalCount: messageCount,
//         isOpened: isOpened
//       });

//       let textPreview = '';

//       if (isOpened) {
//         // If the conversation is opened/read, always show the last message content
//         textPreview = messageText.length > 30
//           ? messageText.substring(0, 30) + '...'
//           : messageText;
//       } else {
//         // If the conversation is not opened/unread, show the number of unread messages
//         if (messageCount === 1) {
//           return '1 yeni mesaj';
//         } else if (messageCount <= 3) {
//           return `${messageCount} yeni mesaj`;
//         } else {
//           return '4+ yeni mesaj';
//         }
//       }

//       return (
//         <View style={styles.messageWrapper}>
//           <Text style={[styles.messageText, isOpened && styles.readMessage]}>
//             {textPreview}
//           </Text>
//           {!isOpened && (
//             <>
//               <Text style={styles.dot}> • </Text>
//               <Text style={styles.timeAgo}>{timeAgo}</Text>
//             </>
//           )}
//         </View>
//       );
//     }
//   }

//   // Default fallback - check if there are messages
//   if (item.messages && Array.isArray(item.messages) && item.messages.length > 0) {
//     // If the conversation is opened/read, show the last message
//     if (isOpened) {
//       const lastMessage = item.messages[item.messages.length - 1];
//       const messageText = lastMessage.text || '1 yeni mesaj';
//       return messageText.length > 30
//         ? messageText.substring(0, 30) + '...'
//         : messageText;
//     } else {
//       // If the conversation is not opened/unread, show the count
//       const messageCount = item.messages.length;
//       if (messageCount === 1) {
//         return '1 yeni mesaj';
//       } else if (messageCount <= 3) {
//         return `${messageCount} yeni mesaj`;
//       } else {
//         return '4+ yeni mesaj';
//       }
//     }
//   }

//   // If there are no messages, check if we have unreadCount or messageCount
//   if (item.unreadCount || item.messageCount) {
//     // If the conversation is opened/read, try to show the last message if available
//     if (isOpened && item.lastMessage) {
//       const messageText = typeof item.lastMessage === 'object'
//         ? (item.lastMessage.text || '1 yeni mesaj')
//         : (item.lastMessage || '1 yeni mesaj');

//       return messageText.length > 30
//         ? messageText.substring(0, 30) + '...'
//         : messageText;
//     } else {
//       // If the conversation is not opened/unread, show the count
//       const count = item.unreadCount || item.messageCount;
//       if (count === 1) {
//         return '1 yeni mesaj';
//       } else if (count <= 3) {
//         return `${count} yeni mesaj`;
//       } else {
//         return '4+ yeni mesaj';
//       }
//     }
//   }

//   // If we still don't have any message information
//   return '1 yeni mesaj'; // Default to 1 new message instead of "No message"
// };

const MAX_PREVIEW_LENGTH = 30;
const getDisplayMessage = (item, isOpened) => {
  // Check if this is the chatbot (Eda Style Up) - identified by having username "Eda Style Up" and being from local data
  const isChatbot = item.username === "Eda Style Up" && item.id && !item._id;

  // For the chatbot (Eda Style Up), always show "2 yeni mesaj"
  if (isChatbot) {
    return "2 yeni mesaj";
  }

  // 1. Mesaj sayısını belirle (unreadCount > messageCount > messages.length)
  const unreadCount = item.unreadCount || 0;
  const totalMessageCount = item.messageCount || (item.messages?.length || 0);

  // Check if the message is read locally (from our persisted state)
  const isReadLocally = item._isReadLocally === true || isOpened;

  // Debug log for message display decision
  console.log('MESSAGE DISPLAY DECISION:', {
    conversationId: item._id || item.id,
    unreadCount,
    totalMessageCount,
    isOpened,
    _isReadLocally: item._isReadLocally,
    isReadLocally,
    lastMessage: item.lastMessage ?
      (typeof item.lastMessage === 'object' ?
        item.lastMessage.text :
        item.lastMessage) :
      'none'
  });

  // 2. Okunmamışsa (isOpened=false ve _isReadLocally=false), mesaj sayısını veya içeriğini göster
  if (!isReadLocally) {
    if (unreadCount >= 1) {
      // If there's exactly 1 unread message, show the message content
      if (unreadCount === 1) {
        const lastMessageText = getLastMessageText(item);
        console.log(`Showing last message content for 1 unread: "${lastMessageText}" for conversation ${item._id || item.id}`);
        return truncateMessage(lastMessageText);
      } else {
        // If there are 2+ unread messages, show the count
        const message = formatMessageCount(unreadCount);
        console.log(`Showing unread count: ${message} for conversation ${item._id || item.id}`);
        return message;
      }
    } else if (totalMessageCount >= 1) {
      // If there's exactly 1 total message, show the message content
      if (totalMessageCount === 1) {
        const lastMessageText = getLastMessageText(item);
        console.log(`Showing last message content for 1 total: "${lastMessageText}" for conversation ${item._id || item.id}`);
        return truncateMessage(lastMessageText);
      } else {
        // If there are 2+ total messages, show the count
        const message = formatMessageCount(totalMessageCount);
        console.log(`Showing total message count: ${message} for conversation ${item._id || item.id}`);
        return message;
      }
    }
    // Hiç mesaj yoksa "Sohbet başlat" göster
    console.log(`No messages, showing "Sohbet başlat" for conversation ${item._id || item.id}`);
    return "Sohbet başlat";
  }

  // 3. Okunduysa (isOpened=true veya _isReadLocally=true), son mesajın içeriğini göster
  const lastMessageText = getLastMessageText(item);
  console.log(`Showing last message: "${lastMessageText}" for conversation ${item._id || item.id}`);
  return (
    <View style={styles.messageWrapper}>
      <Text style={[styles.messageText, styles.readMessage]}>
        {truncateMessage(lastMessageText)}
      </Text>
    </View>
  );
};

// Yardımcı fonksiyonlar
const formatMessageCount = (count) => {
  if (count === 1) return "1 yeni mesaj";
  if (count <= 3) return `${count} yeni mesaj`;
  return "4+ yeni mesaj";
};

const truncateMessage = (text) => {
  if (!text) return "Sohbet başlat";
  return text.length > MAX_PREVIEW_LENGTH
    ? `${text.substring(0, MAX_PREVIEW_LENGTH)}...`
    : text;
};

const getLastMessageText = (item) => {
  if (item.type === 'group' && item.lastMessage?.text) {
    const senderName = item.lastMessage.sender?.username || '';
    if (senderName) {
      return `${senderName}: ${item.lastMessage.text}`;
    }
    return item.lastMessage.text;
  }

  if (item.lastMessage?.text) return item.lastMessage.text;
  if (item.messages?.length > 0) return item.messages[item.messages.length - 1].text;
  if (typeof item.lastMessage === "string") return item.lastMessage;
  return "Sohbet başlat";
};


const MessageListScreen = () => {
  const [openedMessages, setOpenedMessages] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [localMessages, setLocalMessages] = useState(messageList);
  const swipeableRefs = {};
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const navigation = useNavigation();
  const { markAsRead } = useContext(MessageContext);

  useEffect(() => {
    const loadReadMessages = async () => {
      try {
        const readMessages = await getReadMessages();
        setOpenedMessages(readMessages);
      } catch (error) {
        console.error('Error loading read messages:', error);
      }
    };

    loadReadMessages();
  }, []);

  const fetchDirectGroupData = (groupId, conversationId, groupName, user) => {
    api.get(`/messages/groups/${groupId}`)
      .then(response => {
        console.log('Fetched group data:', response.data);

        const groupData = response.data.data.group;

        navigation.navigate('Chat', {
          messageId: conversationId,
          conversationId: conversationId,
          isApiConversation: true,
          isGroupChat: true,
          groupInfo: {
            _id: groupData._id || groupId,
            groupId: groupData._id || groupId,
            name: groupData.name || groupName,
            groupName: groupData.name || groupName,
            participants: groupData.participants || user.participants || [],
            creator: groupData.creator,
            admins: groupData.admins,
            description: groupData.description
          }
        });
      })
      .catch(error => {
        console.error('Error fetching group data:', error);

        navigation.navigate('Chat', {
          messageId: conversationId,
          conversationId: conversationId,
          isApiConversation: true,
          isGroupChat: true,
          groupInfo: {
            _id: groupId,
            groupId: groupId,
            name: user.name || groupName,
            groupName: user.name || groupName,
            participants: user.participants || []
          }
        });
      });
  };

  const handlePress = (user) => {
    const isApiConversation = user._id !== undefined;
    const conversationId = isApiConversation ? user._id : user.id;

    console.log('Handling press for conversation:', {
      conversationId,
      isApiConversation,
      wasOpened: openedMessages[conversationId] || false
    });

    setOpenedMessages(prev => ({
      ...prev,
      [conversationId]: true,
    }));

    markMessageAsRead(conversationId).catch(error => {
      console.error('Error persisting read status:', error);
    });

    if (!isApiConversation) {
      markAsRead(conversationId);

      navigation.navigate('Chat', {
        messageId: conversationId,
        userInfo: {
          userImage: user.userImage,
          name: user.name,
          username: user.username,
          followers: user.followers,
          post: user.post,
        }
      });
    }
    else {
      const isGroupChat = user.type === 'group';
      console.log('Conversation check:', {
        id: user._id,
        type: user.type,
        isGroupChat,
        participantsCount: user.participants ? user.participants.length : 0,
        hasOtherParticipant: !!user.otherParticipant
      });

      if (isGroupChat) {
        console.log('Opening group chat:', {
          groupId: user._id,
          participants: user.participants,
          lastMessage: user.lastMessage
        });

        // Create a group name from participant usernames (if available)
        let groupName = "Group Chat";
        if (user.name) {
          groupName = user.name;
        } else if (user.participants && user.participants.length > 0) {
          // This would need to be improved with actual participant data
          groupName = `Group (${user.participants.length} participants)`;
        }

        // First try to get group messages, which should include group information
        api.get(`/messages/groups/${user._id}/messages`)
          .then(response => {
            console.log('Fetched group messages:', response.data);

            // Check if we have conversation data
            if (response.data?.data?.conversation) {
              const groupData = response.data.data.conversation;

              // Navigate with the group information from messages endpoint
              navigation.navigate('Chat', {
                messageId: conversationId,
                conversationId: conversationId,
                isApiConversation: true,
                isGroupChat: true,
                groupInfo: {
                  _id: groupData._id || user._id,
                  groupId: groupData._id || user._id,
                  name: groupData.name || groupName,
                  groupName: groupData.name || groupName,
                  participants: groupData.participants || user.participants || [],
                  // Include any other relevant group information
                  creator: groupData.creator,
                  admins: groupData.admins,
                  description: groupData.description
                }
              });
            } else {
              // Fallback to the direct group endpoint
              fetchDirectGroupData(user._id, conversationId, groupName, user);
            }
          })
          .catch(error => {
            console.error('Error fetching group messages:', error);
            // Fallback to the direct group endpoint
            fetchDirectGroupData(user._id, conversationId, groupName, user);
          });
      } else {
        navigation.navigate('Chat', {
          messageId: conversationId,
          conversationId: conversationId,
          isApiConversation: true,
          recipientId: user.otherParticipant._id,
          userInfo: {
            userId: user.otherParticipant._id,
            userImage: { uri: user.otherParticipant.profilePicture },
            name: user.otherParticipant.fullName || user.otherParticipant.username,
            username: user.otherParticipant.username,
            followers: user.otherParticipant.followerCount || 0,
            post: user.otherParticipant.postCount || 0,
          }
        });
      }
    }
  };

  const dispatch = useDispatch();
  const { conversations, loading } = useSelector((state) => state.conversations);

  // Fetch conversations when component mounts
  useEffect(() => {
    dispatch(fetchConversations({ page: 1, limit: 20 }))
      .catch(error => {
        console.error('Error fetching conversations:', error);
      });
  }, [dispatch]);

  // Note: Removed automatic refresh on focus to prevent constant data refreshing
  // Data will only refresh on initial mount and manual pull-to-refresh
  // The Redux state will automatically update when new messages are sent/received

  // Create a mutable copy of conversations with adjusted read status
  const [adjustedConversations, setAdjustedConversations] = useState([]);

  // Update UI when conversations change
  useEffect(() => {
    console.log('CONVERSATIONS UPDATE:', {
      conversationsCount: conversations?.length || 0,
      openedMessagesCount: Object.keys(openedMessages).length
    });

    if (conversations && conversations.length > 0) {
      // Get the current openedMessages state
      const currentOpenedMessages = {...openedMessages};

      console.log('OPENED MESSAGES:', JSON.stringify(currentOpenedMessages));

      // Create a new array with adjusted read status
      const adjusted = conversations.map(conversation => {
        // Skip null or undefined conversations
        if (!conversation) {
          return null;
        }

        // Create a shallow copy of the conversation
        const conversationCopy = {...conversation};

        // Ensure messages array exists
        if (!conversationCopy.messages) {
          conversationCopy.messages = [];
        }

        // If the conversation has an ID and was previously marked as read
        // BUT only consider it read locally if the API doesn't show unread messages
        if (conversation._id && currentOpenedMessages[conversation._id] === true) {
          console.log(`Conversation ${conversation._id} was previously marked as read locally`);

          // If the API shows unread messages, respect that over local read status
          // This handles the case where new messages arrived after the conversation was read
          if (conversation.unreadCount > 0) {
            console.log(`Conversation ${conversation._id} has ${conversation.unreadCount} unread messages from API, ignoring local read status`);
            return conversationCopy; // Don't mark as read locally if API shows unread messages
          }

          // Only mark as read locally if API shows no unread messages
          return {
            ...conversationCopy,
            _isReadLocally: true // Add a flag to indicate it's read locally
          };
        }

        console.log(`Conversation ${conversation._id} read status:`, {
          hasLocalReadFlag: conversation._isReadLocally === true,
          isMarkedReadInStorage: currentOpenedMessages[conversation._id] === true,
          unreadCount: conversation.unreadCount || 0
        });

        return conversationCopy;
      }).filter(Boolean); // Remove any null entries

      console.log(`Adjusted ${adjusted.length} conversations with read status`);

      // Update the adjustedConversations state
      setAdjustedConversations(adjusted);
    } else {
      setAdjustedConversations([]);
    }
  }, [conversations, openedMessages]);

// Removed console.log for better performance
  const handleDeletePress = id => {
    setSelectedMessage(id);
    setModalVisible(true);
    Animated.timing(opacityAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
    });
  };

  const confirmDelete = () => {
    if (selectedMessage !== null) {
      // Check if it's the chatbot (id is a number) or an API conversation (id is a MongoDB ObjectId)
      if (typeof selectedMessage === 'number') {
        // It's a local message (chatbot)
        setLocalMessages(prevMessages =>
          prevMessages.filter(item => item.id !== selectedMessage),
        );
      } else {
        // Find the conversation in the combined data to determine if it's a group chat
        const conversation = combinedData.find(item => item._id === selectedMessage);
        const isGroupChat = conversation?.type === 'group';

        // It's an API conversation - call the API to delete the conversation
        console.log('Deleting conversation:', selectedMessage, 'isGroupChat:', isGroupChat);
        dispatch(deleteConversation({
          conversationId: selectedMessage,
          isGroupChat: isGroupChat
        }))
          .unwrap()
          .then(() => {
            console.log('Conversation deleted successfully');
            // No need to refresh as the reducer will update the state
          })
          .catch(error => {
            console.error('Error deleting conversation:', error);
            // Refresh the conversations list in case of error to ensure UI is in sync
            dispatch(fetchConversations({ page: 1, limit: 20 }));
          });
      }
    }
    setModalVisible(false);
    setSelectedMessage(null);
  };

  const cancelDelete = () => {
    setModalVisible(false);
    setSelectedMessage(null);

    if (selectedMessage && swipeableRefs[selectedMessage]) {
      swipeableRefs[selectedMessage]?.close();
    }
  };


  const renderRightActions = id => (
    <TouchableOpacity
      style={styles.deleteButton}
      onPress={() => handleDeletePress(id)}>
      <SvgTrash style={styles.trash} />
    </TouchableOpacity>
  );
  // No additional state needed for refreshing

  const onScroll = (event) => {
    // Only process this event if we're not on Android
    if (Platform.OS === 'android') {
      return;
    }

    const contentOffsetY = event.nativeEvent.contentOffset.y;
    if (contentOffsetY < -100 && !loading) {
      // Refresh conversations from API
      // The useEffect hook will handle respecting the persisted read status
      dispatch(fetchConversations({ page: 1, limit: 20 }));
    }
  };

  // Removed console.log statements for better performance

  // Combine chatbot from local data with API conversations
  const combinedData = useMemo(() => {
    // Get the chatbot from local data
    const chatbot = localMessages[0];

    // Filter out any undefined or null conversations AND conversations without messages
    const validConversations = (adjustedConversations || []).filter(conv => {
      // Skip null or undefined conversations
      if (conv === null || conv === undefined) {
        return false;
      }

      // Skip conversations without messages or lastMessage
      const hasMessages = conv.messages && conv.messages.length > 0;
      const hasLastMessage = conv.lastMessage !== undefined && conv.lastMessage !== null;

      if (!hasMessages && !hasLastMessage) {
        console.log(`Skipping conversation ${conv._id} - no messages or lastMessage`);
        return false;
      }

      return true;
    });

    console.log('COMBINED DATA:', {
      chatbotPresent: !!chatbot,
      validConversationsCount: validConversations.length,
      totalCombinedCount: validConversations.length + 1 // +1 for chatbot
    });

    // Create the combined array with chatbot first, then adjusted API conversations
    return [chatbot, ...validConversations];
  }, [localMessages, adjustedConversations]);

  return (
    <>

    <FlatList
  data={combinedData}
  keyExtractor={(item, index) => {
    if (index === 0) return "chatbot"; // Special ID for chatbot
    const key = item?._id?.toString() || `message-${index}`;
    return key;
  }}
  onScroll={onScroll}
  scrollEventThrottle={16}
  refreshControl={null}
  windowSize={10}
  maxToRenderPerBatch={5}
  initialNumToRender={8}
  removeClippedSubviews={true}
  updateCellsBatchingPeriod={100}
  maintainVisibleContentPosition={{
    minIndexForVisible: 0,
    autoscrollToTopThreshold: 10,
  }}
  ListHeaderComponent={
    loading ? (
      <View style={styles.loaderContainer}>
        <Loader isVisible={true} speed="normal" />
      </View>
    ) : null
  }
  ListEmptyComponent={() => (
    <View style={{ padding: 20, alignItems: 'center' }}>
      <Text>No messages found. Pull down to refresh.</Text>
    </View>
  )}
  showsVerticalScrollIndicator={false}
  renderItem={useMemo(() => {
    // Memoize the renderItem function to prevent unnecessary re-renders
    return ({ item, index }) => {
      // Different handling for chatbot (index 0) vs API conversations
      if (index === 0) {
        // Chatbot item from local data
        const isOpened = openedMessages[item.id] || false;
        const displayMessage = getDisplayMessage(item, isOpened);

        return (
          <Swipeable
            ref={ref => (swipeableRefs[item.id] = ref)}
            renderRightActions={() => renderRightActions(item.id)}
            friction={2}
            overshootRight={false}
          >
            {/* Chatbot with gradient background */}
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 1.25, y: 0 }}
              colors={['#000000', '#D134AA']}
              style={styles.chatbotContainer}
            >
              <View
                style={styles.innerChatbotCont}
              >
                <Image source={item.userImage} style={styles.chatuserImage} />
                <View style={styles.chatbotTextContainer}>
                  <Text style={styles.chatbotusername}>{item.username}</Text>
                  <View style={styles.chatbotMessageRow}>
                    <Text style={{ color: "white" }}>
                      {displayMessage}
                    </Text>
                  </View>
                </View>
              </View>
            </LinearGradient>
          </Swipeable>
        );
      }

      // For API conversations
      // Check if this is a group conversation based on the type field
      const isGroupConversation = item.type === 'group';

      // If this is a group conversation, handle it differently
      if (isGroupConversation) {
        // Create a group name from the conversation data
        let groupName = "Group Chat";
        if (item.name) {
          groupName = item.name;
        } else if (item.participants && item.participants.length > 0) {
          groupName = `Group (${item.participants.length} participants)`;
        }

        const isOpened = openedMessages[item._id] || false;
        const displayMessage = getDisplayMessage(item, isOpened);

        return (
          <Swipeable
            ref={ref => (swipeableRefs[item._id] = ref)}
            renderRightActions={() => renderRightActions(item._id)}
            friction={2}
            overshootRight={false}
          >
            <TouchableOpacity
              onPress={() => handlePress(item)}
              style={styles.messageContainer}
            >
              {/* Group chat icon/image */}
              <View style={styles.groupImageContainer}>
                <Text style={styles.groupImageText}>
                  {groupName.substring(0, 2).toUpperCase()}
                </Text>
              </View>

              <View style={styles.textContainer}>
                {/* Group name */}
                <Text style={styles.username}>
                  {groupName}
                </Text>

                <View style={styles.messageRow}>
                  {/* Message content */}
                  <Text style={[styles.messageText, isOpened && styles.readMessage]}>
                    {displayMessage}
                  </Text>

                  {/* Unread message indicator - show only when there are 2+ unread messages */}
                  {!isOpened && !item._isReadLocally && (item.unreadCount > 1) && <View style={styles.unreadDot} />}
                </View>
              </View>
            </TouchableOpacity>
          </Swipeable>
        );
      }

      // For individual conversations
      // If otherParticipant is missing, use fallback values
      if (!item.otherParticipant) {
        // Create a fallback otherParticipant object
        item.otherParticipant = {
          _id: item._id || 'unknown',
          username: 'Kullanıcı',
          profilePicture: 'https://via.placeholder.com/50'
        };
      }

      const isOpened = openedMessages[item._id] || false;
      const displayMessage = getDisplayMessage(item, isOpened);

      return (
        <Swipeable
          ref={ref => (swipeableRefs[item._id] = ref)}
          renderRightActions={() => renderRightActions(item._id)}
          friction={2}
          overshootRight={false}
        >
            <TouchableOpacity
              onPress={() => handlePress(item)}
              style={styles.messageContainer}
            >
              {/* Kullanıcı profil fotoğrafı */}
              <Image
                source={{ uri: item.otherParticipant.profilePicture }}
                style={styles.userImage}
              />

              <View style={styles.textContainer}>
                {/* Kullanıcı adı */}
                <Text style={styles.username}>
                  {item.otherParticipant?.username || 'Bilinmeyen Kullanıcı'}
                </Text>

                <View style={styles.messageRow}>
                  {/* Mesaj içeriği */}
                  <Text style={[styles.messageText, isOpened && styles.readMessage]}>
                    {displayMessage}
                  </Text>

                  {/* Okunmamış mesaj göstergesi - show only when there are 2+ unread messages */}
                  {!isOpened && !item._isReadLocally && (item.unreadCount > 1) && <View style={styles.unreadDot} />}
                </View>
              </View>
            </TouchableOpacity>
        </Swipeable>
      );
    };
  }, [openedMessages, handlePress, renderRightActions])}
/>

      <Modal visible={modalVisible} transparent animationType="none">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalText}>Sohbeti kalıcı olarak sil?</Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={confirmDelete}>
                <Text style={styles.deleteButtonText}>Sil</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={cancelDelete}>
                <Text style={styles.cancelButtonText}>İptal</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F1F1',
    backgroundColor: '#FFFF',
    marginHorizontal: 20,
  },
  chatbotContainer: {
    borderRadius: 59,
    marginVertical: 5,
    overflow: 'hidden',
    marginHorizontal:20
  },
  groupImageContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#D134AA",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  groupImageText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "600",
  },
  innerChatbotCont: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  chatuserImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    top:"2%",
    left: 10
  },
  chatbotTextContainer: {
    flex: 1,
    justifyContent: 'center',
    top:"2%",
    left:5
  },
  chatbotusername: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E987D0',
  },
  chatbotMessageRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatbotmessage: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  chatbotunreadMessage: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },

  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
  },
  textContainer: {
    flex:1

  },
  username: {
    fontWeight: '600',
    fontSize: 14,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    position: 'relative',
  },
  messageText: {
    fontSize: 14,
    fontWeight: '600',
    color: 'black',
  },
  readMessage: {
    color: '#9D9C9C',
    fontWeight: '500',
  },
  dot: {
    fontSize: 14,
    color: '#9D9C9C',
    marginHorizontal: 3,
  },
  timeAgo: {
    fontSize: 14,
    color: '#9D9C9C',
  },
  messageWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 5,
    backgroundColor: '#D134AA',
    position: 'absolute',
    right: -5,
    top: '50%',
    transform: [{translateY: -10}],
  },
  deleteButton: {
    backgroundColor: '#E33629',
    justifyContent: 'center',
    alignItems: 'center',
    width: 73,
    height: 80,
  },
  trash: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -7,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#F9F9F9',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    alignItems: 'center',
  },
  modalText: {
    fontSize: 16,
    marginBottom: 15,
    fontWeight: '600',
  },
  modalButtons: {
    justifyContent: 'space-around',
    width: '100%',
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginHorizontal: 10,
    alignItems: 'center',
    borderTopWidth: 1,
    borderColor: '#F1F1F1',
  },
  deleteButtonText: {
    color: '#E33629',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButtonText: {
    fontSize: 16,
  },
});

export default MessageListScreen;