import { View, StyleSheet, TouchableOpacity, SafeAreaView, Alert, ActivityIndicator, RefreshControl } from 'react-native';
import SvgBack from "../../assets/back"
import SvgMenu from "../../assets/hamburgerMenu"
import { users } from '../../utils/helpers';
import { useEffect, useMemo, useState, useCallback } from 'react';
import { ScrollView, Text } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { fetchFollowers, fetchFollowing, followUser, unfollowUser, isUserFollowing } from '../../redux/slices/followSlice';
import { getUserProfile } from '../../redux/actions/userActions';
import store from '../../redux/store';
import { getSafeUserId } from '../../api/api';
import ProfileInfo from '../../components/Profile/ProfileInfo';
import SearchProfileHidden from '../../components/Search/SearchProfileHidden';
import FollowCard from '../../components/Home/FollowCard';
import SearchProfileDetail from '../../components/Search/SearchProfileDetail';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import Loader from '../../components/Loader';
import { clearUserPosts } from '../../redux/slices/postSlice';


const SearchProfileScreen = ({ route }) => {
  const { user } = route.params;
  console.log("SearchProfileScreen - gelen user:", user);

  const dispatch = useDispatch();
  const { isLogin, user: currentAuthUser } = useSelector(state => state.auth);
  const followers = useSelector(state => state.follow.followers);
  const following = useSelector(state => state.follow.following);
  const loading = useSelector(state => state.follow.loading);
  const { currentProfile } = useSelector(state => state.user);
  const navigation = useNavigation();

  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Function to refresh profile data
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    console.log("Refreshing profile data...");

    try {
      if (!targetUserId) {
        console.log("No user ID available for refresh");
        return;
      }

      // Get current user ID for fetching their following list
      const currentUserId = getAuthenticatedUserId();
      if (!currentUserId) {
        console.log("No current user ID available for refresh");
        setRefreshing(false);
        return;
      }

      console.log("Refreshing data - Target user:", targetUserId, "Current user:", currentUserId);

      // Fetch updated profile, followers and CURRENT USER'S following data
      // This is important to maintain follow status
      await Promise.all([
        dispatch(getUserProfile(targetUserId)),
        dispatch(fetchFollowers(targetUserId)),
        dispatch(fetchFollowing(currentUserId)) // Fetch current user's following list instead
      ]);

      console.log("Profile data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing profile data:", error);
      Alert.alert("Error", "Failed to refresh profile data");
    } finally {
      setRefreshing(false);
    }
  }, [dispatch, targetUserId]);

  // Handle scroll events to trigger refresh when scrolling down
  const onScroll = useCallback((event) => {
    const contentOffsetY = event.nativeEvent.contentOffset.y;

    // Trigger refresh when scrolling down past a threshold
    if (contentOffsetY < -100 && !refreshing && !isLoading) {
      console.log("Manual refresh triggered by scroll");
      onRefresh();
    }
  }, [refreshing, isLoading, onRefresh]);

  // Component mount olduğunda tam Redux store'u logla
  useEffect(() => {
    const fullState = store.getState();
    console.log("=========== CURRENT REDUX STATE ===========");
    console.log("Auth State:", JSON.stringify(fullState.auth));
    console.log("Follow State:", JSON.stringify(fullState.follow));
    console.log("===========================================");

    // Clear user posts when component mounts to prevent showing old posts
    dispatch(clearUserPosts());

    // AsyncStorage'dan token'ı kontrol et
    const checkToken = async () => {
      const token = await AsyncStorage.getItem('token');
      console.log("AsyncStorage Token:", token ? "VAR" : "YOK");
    };

    checkToken();
  }, [dispatch]);

  // Hedef kullanıcı ID'si (profil sahibi)
  const targetUserId = getSafeUserId(user);

  // Kullanıcı profil bilgilerini çek
  useEffect(() => {
    if (targetUserId) {
      console.log("SearchProfileScreen - Kullanıcı profili çekiliyor, userId:", targetUserId);
      dispatch(getUserProfile(targetUserId))
        .unwrap()
        .then(profileData => {
          console.log("SearchProfileScreen - Kullanıcı profili başarıyla çekildi:", {
            id: profileData.id,
            username: profileData.username,
            followerCount: profileData.followerCount,
            followingCount: profileData.followingCount
          });
        })
        .catch(error => {
          console.error("SearchProfileScreen - Kullanıcı profili çekilirken hata:", error);
        });
    }
  }, [dispatch, targetUserId]);

  // Daha güvenilir kullanıcı kimliği elde etme
  const getAuthenticatedUserId = () => {
    // 1. Redux store'dan direkt alma
    const authUser = store.getState().auth.user;
    const userId = authUser?.id || authUser?._id;

    if (userId) {
      console.log("User ID Redux'tan alındı:", userId);
      return userId;
    }

    // 2. Selector hook'tan alma
    if (currentAuthUser?.id || currentAuthUser?._id) {
      const id = currentAuthUser.id || currentAuthUser._id;
      console.log("User ID Selector'dan alındı:", id);
      return id;
    }

    console.error("UYARI: Kimlik doğrulanmış kullanıcı ID'si bulunamadı!");
    return null;
  };

  // Mevcut giriş yapmış kullanıcı ID'si
  const currentUserId = getAuthenticatedUserId();

  // Bilgileri logla
  useEffect(() => {
    console.log("SearchProfileScreen - Kullanıcı ID Kontrolü:", {
      targetUserId,
      currentUserId,
      isSameUser: currentUserId === targetUserId,
      profileUsername: user?.username,
      currentUsername: currentAuthUser?.username
    });
  }, [targetUserId, currentUserId, user, currentAuthUser]);

  // Simplify state management - use separate state variables for better reactivity
  const [isFollowing, setIsFollowing] = useState(false);
  const [followersCount, setFollowersCount] = useState(user?.followersCount || 0);
  const [followingCount, setFollowingCount] = useState(user?.followingCount || 0);

  // Keep a reference to the optimistic state for compatibility with existing code
  const optimisticState = { isFollowing, followersCount, followingCount };

  // Debug
  console.log("Redux auth state (should be current logged in user):", {
    currentUser: currentAuthUser ? { id: currentAuthUser.id || currentAuthUser._id, username: currentAuthUser.username } : null,
    isLogin,
    token: store.getState().auth.token ? 'Var' : 'Yok'
  });
  console.log("Current follow state:", {
    followersCount: followers.length,
    followingCount: following.length,
    optimisticState
  });

  const randomUser = useMemo(() => {
    return users[Math.floor(Math.random() * users.length)];
  }, []);

  // Consolidated data loading effect - Fetch all necessary data in one effect
  useEffect(() => {
    const loadAllData = async () => {
      try {
        // Get the current logged-in user's ID
        const currentUserId = getAuthenticatedUserId();
        if (!currentUserId) {
          console.log("No authenticated user ID found for fetching data");
          return;
        }

        // Only proceed if we have a target user ID
        if (!targetUserId) {
          console.log("No target user ID available for fetching data");
          return;
        }

        console.log("Loading all profile data:", {
          currentUserId,
          targetUserId,
          timestamp: new Date().toISOString()
        });

        // Fetch all required data in parallel
        await Promise.all([
          // Get current user's following list (to determine follow status)
          dispatch(fetchFollowing(currentUserId)),
          // Get target user's followers
          dispatch(fetchFollowers(targetUserId)),
          // Get target user's profile
          dispatch(getUserProfile(targetUserId))
        ]);

        console.log("All profile data loaded successfully");
      } catch (error) {
        console.error("Error loading profile data:", error);
      }
    };

    // Only load data if we're not already refreshing
    if (!refreshing && !isLoading) {
      loadAllData();
    }
  }, [dispatch, targetUserId, refreshing, isLoading]);

  // Takip durumu güncelleme
  useEffect(() => {
    if (!targetUserId || !following) return;

    // Store'dan güncel kullanıcı bilgilerini al
    const currentAuthUser = store.getState().auth.user;
    const currentUserId = currentAuthUser?.id || currentAuthUser?._id;

    if (!currentUserId) {
      console.log("No current user ID available for follow status check");
      return;
    }

    // Check if the current user is following the target user
    // This is the critical part - we need to check if the target user ID is in the current user's following list
    const followingStatus = isUserFollowing(following, targetUserId);

    // Takipçi ve takip edilen sayılarını güncelle
    // Öncelik sırası: currentProfile > user prop > followers/following arrays
    const followerCount =
      (currentProfile?.followerCount || currentProfile?.followersCount) ||
      (user?.followerCount || user?.followersCount) ||
      followers.length ||
      0;

    const followingCount =
      currentProfile?.followingCount ||
      user?.followingCount ||
      following.length ||
      0;

    console.log("SearchProfileScreen - Takip durumu güncelleniyor:", {
      isFollowing: followingStatus,
      followerCount,
      followingCount,
      currentUserId,
      targetUserId,
      followingLength: following.length
    });

    // Always update the follow status to ensure UI is in sync with server state
    // Update individual state variables for better reactivity
    console.log("Updating follow status:", followingStatus);

    // Update state variables directly
    setIsFollowing(followingStatus);
    setFollowersCount(followerCount);
    setFollowingCount(followingCount);

    console.log("State updated - UI should refresh immediately");
  }, [targetUserId, following, followers, user, currentProfile]);

  // API test fonksiyonu - Removed automatic execution to reduce API calls
  const testAPI = async () => {
    try {
      console.log('[API TEST] Başlatılıyor...');
      const token = await AsyncStorage.getItem('token');

      if (!token) {
        console.error('[API TEST] Token bulunamadı!');
        return;
      }

      // API temel URL'i al
      const baseURL = 'http://192.168.0.30:5000'; // .env'den alınan değer

      // 1. Kullanıcı bilgilerini al
      try {
        console.log('[API TEST] Kullanıcı bilgileri alınıyor...');
        const userResponse = await axios.get(`${baseURL}/api/auth/me`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        console.log('[API TEST] Kullanıcı bilgileri başarıyla alındı:', userResponse.data);
      } catch (userError) {
        console.error('[API TEST] Kullanıcı bilgisi hatası:', userError.message);
      }

      // 2. Takip API testleri
      if (targetUserId) {
        try {
          console.log(`[API TEST] Takipçi listesi alınıyor (${targetUserId})...`);
          const followersResponse = await axios.get(`${baseURL}/api/follow/users/${targetUserId}/followers`, {
            headers: { Authorization: `Bearer ${token}` }
          });
          console.log('[API TEST] Takipçi listesi başarıyla alındı:', followersResponse.data);
        } catch (followError) {
          console.error('[API TEST] Takipçi listesi hatası:', followError.message);
        }
      }

      console.log('[API TEST] Tamamlandı');
    } catch (error) {
      console.error('[API TEST] Genel hata:', error.message);
    }
  };

  // Removed automatic API test execution to prevent excessive API calls
  // The testAPI function is still available but not automatically called

  const handleFollowToggle = async () => {
    console.log('1. Takip işlemi başlatılıyor');

    // Authenticate olmuş kullanıcıyı kontrol et
    const userId = getAuthenticatedUserId();

    console.log('2. Mevcut kullanıcı ID:', userId);
    console.log('3. Hedef kullanıcı ID:', targetUserId);
    console.log('4. Yükleme durumu:', isLoading);
    console.log('4a. Mevcut takip durumu:', isFollowing);

    // Redux auth state'i yeniden kontrol et
    const authState = store.getState().auth;
    console.log('5. Redux Auth State:', {
      isLogin: authState.isLogin,
      token: authState.token ? "Var" : "Yok",
      user: authState.user ? `${authState.user.username} (${authState.user.id || authState.user._id})` : "Yok"
    });

    // Aynı kullanıcı mı kontrol et
    if (userId === targetUserId) {
      console.error('Kendinizi takip edemezsiniz');
      Alert.alert('Error', 'Kendinizi takip edemezsiniz');
      return;
    }

    if (!targetUserId || !userId || isLoading) {
      console.log('6. Erken çıkış - eksik ID\'ler veya zaten yükleniyor', {
        targetUserId,
        userId,
        isLoading
      });
      return;
    }

    // Set loading state immediately
    setIsLoading(true);
    console.log('7. Yükleme durumu: true');

    const wasFollowing = isFollowing;
    console.log('8. Önceki takip durumu:', wasFollowing);

    try {
      // Apply optimistic UI update immediately
      console.log('9. İyimser güncelleme başlatılıyor');

      // If we're currently not following, we want to follow
      if (!wasFollowing) {
        // Update state immediately for better UX
        setIsFollowing(true);
        setFollowersCount(prev => prev + 1);
        console.log('10. Takip durumu güncellendi - UI hemen değişmeli');

        console.log('11. Takip etme eylemi gönderiliyor');
        const result = await dispatch(followUser(targetUserId)).unwrap();
        console.log('12. Takip etme eylemi başarıyla tamamlandı:', result);

        // Force a re-render to ensure component replacement
        // This is a key step to ensure the UI updates correctly
        requestAnimationFrame(() => {
          console.log('12a. Forcing immediate UI update after follow');
          setIsFollowing(true);
        });
      }
      // If we're currently following, we want to unfollow
      else {
        // Update state immediately for better UX
        setIsFollowing(false);
        setFollowersCount(prev => prev - 1);
        console.log('10. Takip durumu güncellendi - UI hemen değişmeli');

        console.log('11. Takibi bırakma eylemi gönderiliyor');
        const result = await dispatch(unfollowUser(targetUserId)).unwrap();
        console.log('12. Takibi bırakma eylemi başarıyla tamamlandı:', result);

        // Force a re-render to ensure component replacement
        // This is a key step to ensure the UI updates correctly
        requestAnimationFrame(() => {
          console.log('12a. Forcing immediate UI update after unfollow');
          setIsFollowing(false);
        });
      }

      // The Redux state should already be updated by the action
      // We can verify the follow status from the Redux store
      const updatedFollowing = store.getState().follow.following;
      const updatedFollowStatus = isUserFollowing(updatedFollowing, targetUserId);

      console.log('13. Doğrulanmış takip durumu:', {
        isFollowing: updatedFollowStatus,
        followingLength: updatedFollowing.length
      });

      // Make sure the UI reflects the actual follow status from the Redux store
      console.log('14. UI durumu Redux store ile senkronize ediliyor');

      // Force update state with Redux store data
      setIsFollowing(updatedFollowStatus);

      // Update follower count based on Redux store data
      const currentFollowers = store.getState().follow.followers;
      setFollowersCount(currentFollowers.length);

      console.log('15. Takip durumu güncellendi:', updatedFollowStatus);

    } catch (error) {
      console.log('16. Hata oluştu:', error);

      // Check if the error contains the "already following" message
      const errorMessage = typeof error === 'object' ? error.message : String(error);
      const isAlreadyFollowingError =
        errorMessage.includes("Bu kullanıcıyı zaten takip ediyorsunuz") ||
        errorMessage.includes("You are already following this user") ||
        errorMessage.includes("already following");

      if (isAlreadyFollowingError) {
        console.log('16a. "Zaten takip ediliyor" hatası algılandı');

        // Force set the follow status to true
        console.log('16b. Takip durumu true olarak ayarlanıyor');
        setIsFollowing(true);

        // Force a re-render to ensure component replacement
        requestAnimationFrame(() => {
          console.log('16c. Forcing component re-render after error');
          setIsFollowing(true);
        });
      } else {
        // For other errors, revert the optimistic update
        console.log('17. İyimser durum geri alınıyor');
        setIsFollowing(wasFollowing);
        setFollowersCount(prev => wasFollowing ? prev + 1 : prev - 1);

        Alert.alert('Error', 'Takip işlemi sırasında bir hata oluştu');
      }

    } finally {
      // Always set loading to false when done
      console.log('18. Son temizlik - yükleme durumu: false');
      setIsLoading(false);
      console.log('19. Takip işlemi tamamlandı, son durum:', isFollowing ? 'Takip ediliyor' : 'Takip edilmiyor');
    }
  };

// Konsola takip durumunu yazdır ve UI güncellemelerini izle
useEffect(() => {
  console.log('Follow status changed - UI should update:', {
    isFollowing,
    followersCount,
    followingCount,
    profileId: targetUserId,
    currentUserId: currentUserId,
    timestamp: Date.now()
  });

  // Log which component should be shown based on follow status
  if (isFollowing) {
    console.log('Should show SearchProfileDetail');
  } else {
    console.log('Should show SearchProfileHidden');
  }

  // No need for force update hack anymore
}, [isFollowing, followersCount, followingCount, targetUserId, currentUserId]);

  // Yükleme durumu ve user kontrolü
  if (loading || isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!user) {
    return null;
  }

return (
  <ScrollView
    style={styles.container}
    showsVerticalScrollIndicator={false}
    onScroll={onScroll}
    scrollEventThrottle={16}
    refreshControl={
      <RefreshControl
        refreshing={refreshing}
        onRefresh={onRefresh}
        colors={["#D134AA"]} // Pink color to match app theme
        tintColor="#D134AA"
      />
    }
  >
    {refreshing && (
      <View style={styles.loadingContainer}>
        <Loader />
      </View>
    )}
    <View style={styles.header}>
      <TouchableOpacity onPress={navigation.goBack}>
        <SvgBack/>
      </TouchableOpacity>
      <TouchableOpacity>
        <SvgMenu/>
      </TouchableOpacity>
    </View>

    <ProfileInfo
      user={{
        ...(user || {}),
        id: targetUserId,
        followersCount: followersCount,
        followingCount: followingCount,
        fullName: user?.fullName || user?.name || '',
      }}
      handleFollowToggle={handleFollowToggle}
      isFollowingUser={isFollowing}
      isLoading={isLoading || loading}
    />

    {/* Render different components based on follow status */}
    <View style={{width: '100%'}}>
      {isFollowing ? (
        // If following, show the user's profile details
        <SearchProfileDetail
          user={{...user, id: targetUserId}}
        />
      ) : (
        // If not following, show the hidden profile message and follow button
        <>
          <SearchProfileHidden
            user={user}
            handleFollowToggle={handleFollowToggle}
          />
          <FollowCard
            randomUser={randomUser}
          />
        </>
      )}
    </View>
  </ScrollView>
);
};

export default SearchProfileScreen;

const styles = StyleSheet.create({
  loadingContainer: {
    flex:1,
    alignItems: "center",
    justifyContent: "center",
  },
  container: {
    flex: 1,
    backgroundColor:"#FFFFFF",
  },
  header:{
    flexDirection:"row",
    justifyContent:"space-between",
    marginHorizontal:20,
    marginTop:60

  },
  username: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  userHandle: {
    fontSize: 18,
    color: 'gray',
  },
})



// const SearchProfileScreen = ({ user, closeModal }) => {
//   const dispatch = useDispatch();
//   const { user: currentUser, isLogin } = useSelector(state => state.auth);
//   const followers = useSelector(state => state.follow.followers);
//   const following = useSelector(state => state.follow.following);
//   const followStatus = useSelector(state => state.follow.status);
//   const loading = useSelector(state => state.follow.loading || state.follow.status === "loading");
// //   const currentUser = useSelector(state => state.auth.user); // Giriş yapan kullanıcı
//   const [isLoading, setIsLoading] = useState(false);
//   const userId = getSafeUserId(user);
//   const [optimisticState, setOptimisticState] = useState({
//     isFollowing: false,
//     followersCount: 0,
//     followingCount: 0
//   });
// // Auth state'inizi kontrol edin
// console.log("Redux auth state:", useSelector(state => state.auth));
//   // const userId = getSafeUserId(user);
//   const randomUser = useMemo(() => {
//     return users[Math.floor(Math.random() * users.length)];
//   }, []);

//   // Kullanıcının kendi takip ettiği kullanıcıları yükle
//   useEffect(() => {
//     if (currentUser?._id || currentUser?.id) {
//       const currentUserId = currentUser._id || currentUser.id;
//       console.log("Mevcut kullanıcının takip listesini yüklüyorum:", currentUserId);
//       dispatch(fetchFollowing(currentUserId));
//     } else {
//       console.log("Mevcut kullanıcı bilgisi bulunamadı");
//     }
//   }, [dispatch, currentUser]);

//   // Profil sahibinin takipçilerini yükle
//   useEffect(() => {
//     if (userId) {
//       console.log("Profil sahibinin takipçilerini yüklüyorum:", userId);
//       // Profil sahibinin takipçilerini getir
//       dispatch(fetchFollowers(userId));
//     }
//   }, [userId, dispatch]);

//   // Başlangıç durumunu ve değişiklikleri yöneten useEffect
//   useEffect(() => {
//     if (!userId || !currentUser) return;

//     const currentUserId = currentUser._id || currentUser.id;
//     const followingStatus = isUserFollowing(following, userId);

//     console.log('Takip durumu kontrolü:', {
//       profileId: userId,
//       currentUserId,
//       isFollowing: followingStatus
//     });

//     // Optimistik state'i güncelle
//     setOptimisticState(prev => {
//       // Eğer değişiklik varsa güncelle
//       if (prev.isFollowing !== followingStatus ||
//           prev.followersCount !== followers.length) {
//         return {
//           isFollowing: followingStatus,
//           followersCount: followers.length,
//           followingCount: following.length
//         };
//       }
//       return prev;
//     });

//   }, [userId, following, followers, currentUser]);

//   const handleFollowToggle = async () => {
//     if (!userId || !currentUser || isLoading || loading) return;
//     const currentUserId = currentUser._id || currentUser.id;

//     setIsLoading(true);
//     const wasFollowing = optimisticState.isFollowing;

//     try {
//       if (wasFollowing) {
//         const result = await dispatch(unfollowUser(userId)).unwrap();
//         console.log("Takibi bırakma işlemi sonucu:", result);
//       } else {
//         const result = await dispatch(followUser(userId)).unwrap();
//         console.log("Takip etme işlemi sonucu:", result);
//       }

//       // İşlem başarılı olursa güncel takip durumunu yeniden kontrol et
//       dispatch(fetchFollowing(currentUserId));
//       dispatch(fetchFollowers(userId));

//     } catch (error) {
//       console.error("İşlem başarısız:", error);
//       // Kullanıcıya hata mesajı göster
//       Alert.alert("Hata", error || "Takip işlemi başarısız oldu");

//       // Takip listesini yeniden yükle (hata durumunda bile doğru durumu görmek için)
//       dispatch(fetchFollowing(currentUserId));
//     } finally {
//       setIsLoading(false);
//     }
//   };