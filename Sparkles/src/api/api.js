import axios from 'axios';
import Config from 'react-native-config';
import store from '../redux/store';
import {refreshToken, logout} from '../redux/slices/authSlice';
import { GLOBAL_RESET } from '../redux/store';
import { clearAllUserData } from '../utils/clearUserData';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Log the base URL for debugging
console.log('API Base URL:', Config.BASE_URL);

// Log environment variables for debugging
console.log('Environment variables:', {
  BASE_URL: Config.BASE_URL,
  LOGIN_URL: Config.LOGIN_URL,
  USER_INFO_URL: Config.USER_INFO_URL,
});

// Fallback URL if Config.BASE_URL is undefined
const baseURL = Config.BASE_URL || 'http://127.0.0.1:4000/api/';
console.log('Using baseURL:', baseURL);

// Token refresh state management
let isRefreshing = false;
let refreshCooldown = false;
let failedQueue = [];
let lastRefreshAttempt = 0;
const REFRESH_COOLDOWN_MS = 30000; // 30 seconds cooldown after failed refresh

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

const api = axios.create({
  baseURL: baseURL,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'User-Agent': 'axios/ReactNative',
  },
  timeout: 10000, // 10 second timeout
});

// Track request timestamps to implement client-side rate limiting
const requestTimestamps = {};
const MAX_REQUESTS_PER_ENDPOINT = 5;
const RATE_LIMIT_WINDOW_MS = 10000; // 10 seconds

// Simple in-memory cache for GET requests
const cache = {};
const CACHE_DURATION = 60000; // 1 minute cache duration

/**
 * Clear all cached API responses
 * This should be called when a user logs out to prevent serving cached data from previous user
 */
export const clearApiCache = () => {
  console.log('[API CACHE] Clearing all cached responses...');
  const cacheKeys = Object.keys(cache);
  console.log('[API CACHE] Clearing', cacheKeys.length, 'cached responses');

  // Clear all cache entries
  for (const key in cache) {
    delete cache[key];
  }

  console.log('[API CACHE] Cache cleared successfully');
};

/**
 * Clear cache for user-specific endpoints
 * This should be called when a new user logs in to ensure fresh data
 */
export const clearUserSpecificCache = () => {
  console.log('[API CACHE] Clearing user-specific cached responses...');

  // List of user-specific endpoints that should be cleared on login
  const userSpecificEndpoints = [
    'auth/me',
    'posts/feed',
    'posts/user',
    'conversations',
    'messages',
    'users/profile',
    'users/following',
    'users/followers'
  ];

  let clearedCount = 0;
  for (const key in cache) {
    // Check if this cache key contains any user-specific endpoint
    const shouldClear = userSpecificEndpoints.some(endpoint => key.includes(endpoint));
    if (shouldClear) {
      delete cache[key];
      clearedCount++;
    }
  }

  console.log('[API CACHE] Cleared', clearedCount, 'user-specific cached responses');
};

/**
 * Clear cache for a specific post
 * This should be called when a post is liked, commented on, or updated
 */
export const clearPostCache = (postId) => {
  console.log('[API CACHE] Clearing cache for post:', postId);

  let clearedCount = 0;
  for (const key in cache) {
    // Clear cache for:
    // - Specific post detail: posts/{postId}
    // - Feed posts that might contain this post
    // - User posts that might contain this post
    // - Comments for this post
    if (key.includes(`posts/${postId}`) ||
        key.includes('posts/feed') ||
        key.includes('posts/user') ||
        key.includes('users/') && key.includes('/posts')) {
      delete cache[key];
      clearedCount++;
    }
  }

  console.log('[API CACHE] Cleared', clearedCount, 'cached responses for post', postId);
};

/**
 * Clear cache for user profile data
 * This should be called when a user's profile is updated (including profile picture)
 */
export const clearUserProfileCache = (userId = null) => {
  console.log('[API CACHE] Clearing user profile cache for userId:', userId || 'current user');

  let clearedCount = 0;
  for (const key in cache) {
    // Clear cache for:
    // - Current user profile: auth/me
    // - Specific user profile: users/{userId}
    // - User posts that might show updated profile info
    // - Feed posts that might contain this user's profile
    // - Conversations that might show this user's profile
    // - Messages that might show this user's profile
    const shouldClear =
      key.includes('auth/me') ||
      (userId && key.includes(`users/${userId}`)) ||
      key.includes('posts/feed') ||
      key.includes('posts/user') ||
      key.includes('conversations') ||
      key.includes('messages') ||
      key.includes('users/search');

    if (shouldClear) {
      delete cache[key];
      clearedCount++;
    }
  }

  console.log('[API CACHE] Cleared', clearedCount, 'cached responses for user profile');
};

// Public routes that don't require authentication
const publicRoutes = [
  '/auth/login',
  '/auth/signup',
  '/auth/signup/step1',
  '/auth/signup/step2',
  '/auth/google',
  '/auth/apple',
  '/auth/forgot-password',
  '/auth/reset-password'
];

// token ekleme
api.interceptors.request.use(
  async function (config) {
    // Check cache for GET requests
    if (config.method === 'get') {
      const cacheKey = `${config.url}${JSON.stringify(config.params || {})}`;
      const cachedResponse = cache[cacheKey];

      if (cachedResponse && (Date.now() - cachedResponse.timestamp < CACHE_DURATION)) {
        // Return cached response by setting a custom flag
        config.adapter = async () => {
          return {
            data: cachedResponse.data,
            status: 200,
            statusText: 'OK',
            headers: cachedResponse.headers,
            config: config,
            request: {}
          };
        };
        return config;
      }
    }

    // Implement client-side rate limiting to prevent 429 errors
    const endpoint = `${config.method}:${config.url}`;
    const now = Date.now();

    // Initialize or clean up old timestamps
    if (!requestTimestamps[endpoint]) {
      requestTimestamps[endpoint] = [];
    } else {
      // Remove timestamps older than the window
      requestTimestamps[endpoint] = requestTimestamps[endpoint].filter(
        timestamp => now - timestamp < RATE_LIMIT_WINDOW_MS
      );
    }

    // Check if we've exceeded the rate limit
    if (requestTimestamps[endpoint].length >= MAX_REQUESTS_PER_ENDPOINT) {
      console.warn(`Client-side rate limit exceeded for ${endpoint}. Delaying request.`);

      // Wait until we're under the limit again
      await new Promise(resolve => {
        setTimeout(resolve, RATE_LIMIT_WINDOW_MS - (now - requestTimestamps[endpoint][0]));
      });
    }

    // Add current timestamp to the list
    requestTimestamps[endpoint].push(now);

    // Check if the request is for a public route
    const isPublicRoute = publicRoutes.some(route => config.url.includes(route));

    const token = await AsyncStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    } else if (!isPublicRoute) {
      // Only show warning for non-public routes
      console.warn('No token found for authenticated request to:', config.url);
    }

    return config;
  },
  function (error) {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  },
);

// token yenileme
api.interceptors.response.use(
  response => {
    // Cache successful GET responses
    if (response.config.method === 'get' && response.status === 200) {
      const cacheKey = `${response.config.url}${JSON.stringify(response.config.params || {})}`;
      cache[cacheKey] = {
        data: response.data,
        headers: response.headers,
        timestamp: Date.now()
      };
    }
    return response;
  },
  async error => {
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message
    });

    const originalRequest = error.config;

    // Handle rate limiting (429 Too Many Requests)
    if (error.response?.status === 429) {
      console.warn('Rate limit exceeded (429). Implementing backoff strategy.');

      // If this is a token refresh request that hit rate limit
      if (originalRequest.url === '/auth/refresh-token') {
        console.error('Token renewal failed due to rate limiting');
        refreshCooldown = true;
        setTimeout(() => {
          refreshCooldown = false;
        }, REFRESH_COOLDOWN_MS);

        return Promise.reject(error);
      }

      // For other rate-limited requests, wait and retry with exponential backoff
      const retryCount = originalRequest._retryCount || 0;
      if (retryCount < 3) { // Maximum 3 retry attempts
        originalRequest._retryCount = retryCount + 1;

        // Exponential backoff: 1s, 2s, 4s
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Retrying request after ${delay}ms (attempt ${retryCount + 1}/3)`);

        return new Promise(resolve => {
          setTimeout(() => resolve(api(originalRequest)), delay);
        });
      }
    }

    // 401 hatası ve token yenileme denenmemişse
    if (error.response?.status === 401 && !originalRequest._retry) {
      console.log('Attempting to refresh token due to 401 error');
      originalRequest._retry = true;

      // Check if we're in cooldown period after a failed refresh
      if (refreshCooldown) {
        console.warn('Token refresh in cooldown period. Skipping refresh attempt.');
        return Promise.reject(error);
      }

      // If a refresh is already in progress, queue this request
      if (isRefreshing) {
        console.log('Token refresh already in progress, queuing request');
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(token => {
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return api(originalRequest);
          })
          .catch(err => {
            return Promise.reject(err);
          });
      }

      // Check for minimum time between refresh attempts
      const now = Date.now();
      if (now - lastRefreshAttempt < 5000) { // Minimum 5 seconds between attempts
        console.warn('Too many refresh attempts in short period. Throttling.');
        return Promise.reject(error);
      }

      isRefreshing = true;
      lastRefreshAttempt = now;

      try {
        // Yeni token al
        const refreshToken = await AsyncStorage.getItem('refreshToken');
        if (!refreshToken) {
          console.error('No refresh token available');
          isRefreshing = false;
          processQueue(error);
          return Promise.reject(error);
        }

        console.log('Calling refresh token endpoint with refresh token:', refreshToken);
        const response = await axios.post(`${Config.BASE_URL}auth/refresh-token`, { refreshToken });
        console.log('Refresh token response:', response.data);
        const newToken = response.data.token || response.data.accessToken;

        if (newToken) {
          console.log('Token refreshed successfully');
          // AsyncStorage'e yeni token'ı kaydet
          await AsyncStorage.setItem('token', newToken);

          // Yeni token ile isteği tekrarla
          api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;

          // Process any queued requests with the new token
          processQueue(null, newToken);
          isRefreshing = false;

          return api(originalRequest);
        } else {
          console.error('Refresh token response did not contain a new token');
          processQueue(new Error('Failed to get new token'));
          isRefreshing = false;
          return Promise.reject(error);
        }
      } catch (refreshError) {
        console.error('Token yenileme başarısız:', refreshError);

        // Set cooldown if we hit rate limit during refresh
        if (refreshError.response?.status === 429) {
          console.warn('Token refresh rate limited. Setting cooldown period.');
          refreshCooldown = true;
          setTimeout(() => {
            refreshCooldown = false;
          }, REFRESH_COOLDOWN_MS);
        }

        // Use comprehensive logout to clear all data
        try {
          console.log('[API INTERCEPTOR] Performing comprehensive logout due to token refresh failure');
          await clearAllUserData();

          // Clear API cache to prevent serving stale data
          clearApiCache();

          // Dispatch global reset to clear all Redux state
          if (typeof store.dispatch === 'function') {
            store.dispatch({ type: GLOBAL_RESET });
          }
        } catch (logoutError) {
          console.error('[API INTERCEPTOR] Error during comprehensive logout:', logoutError);

          // Fallback: at least clear tokens, cache, and dispatch basic logout
          await AsyncStorage.removeItem('token');
          await AsyncStorage.removeItem('refreshToken');
          clearApiCache();

          if (typeof store.dispatch === 'function' && typeof logout === 'function') {
            store.dispatch(logout());
          }
        }

        processQueue(refreshError);
        isRefreshing = false;
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default api;

export const getSafeUserId = user => {
  if (!user) return null;

  // ID alanlarına erişmeyi dene (farklı formatlar için)
  const userId = user.id || user._id;

  // Debug için
  console.log('getSafeUserId - user:', user);
  console.log('getSafeUserId - extracted userId:', userId);

  // ID yoksa veya geçersizse null döndür
  if (!userId) return null;

  // Eğer ID bir string değilse, string'e çevir
  return typeof userId === 'string' ? userId : String(userId);
};