import api from './api';

export async function getRequest(URL, params = {}) {
  try {
    const response = await api.get(URL, { params });
    return response;
  } catch (error) {
    // Hata yönetimini merkezileştirmek için
    console.error(`GET Request Hatası [${URL}]:`, error);
    throw error; // Caller'ın hatayı yakalamasını sağlar
  }
}
export async function postRequest(URL, payload) {
  try {
    console.log(`[API] POST Request to ${URL}`, { payload });
    const response = await api.post(URL, payload);
    console.log(`[API] POST Response from ${URL}:`, {
      status: response.status,
      hasData: !!response.data,
      dataKeys: response.data ? Object.keys(response.data) : []
    });
    return response;
  } catch (error) {
    console.error(`[API] POST Request Error to ${URL}:`, {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      config: error.config
    });
    throw error;
  }
}

export async function patchRequest(URL, payload, params = {}) {
  const response = await api.patch(URL, payload, {
    params,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response;
}