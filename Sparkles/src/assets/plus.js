import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON>lip<PERSON><PERSON>, Rect } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_79_1173)">
      <Path
        d="M12.1519 0C5.52448 0 0.151855 5.37262 0.151855 12C0.151855 18.6278 5.52448 24 12.1519 24C18.7796 24 24.1519 18.6278 24.1519 12C24.1519 5.37262 18.7796 0 12.1519 0ZM12.1519 22.5236C6.36223 22.5236 1.65186 17.7896 1.65186 12C1.65186 6.21033 6.36223 1.49995 12.1519 1.49995C17.9415 1.49995 22.6519 6.21035 22.6519 12C22.6519 17.7896 17.9415 22.5236 12.1519 22.5236ZM17.4019 11.25H12.9019V6.75C12.9019 6.336 12.5659 6 12.1519 6C11.7379 6 11.4019 6.336 11.4019 6.75V11.25H6.90186C6.48786 11.25 6.15186 11.586 6.15186 12C6.15186 12.414 6.48786 12.75 6.90186 12.75H11.4019V17.25C11.4019 17.664 11.7379 18 12.1519 18C12.5659 18 12.9019 17.664 12.9019 17.25V12.75H17.4019C17.8159 12.75 18.1519 12.414 18.1519 12C18.1519 11.586 17.8159 11.25 17.4019 11.25Z"
        fill="black"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_79_1173">
        <Rect
          width={24}
          height={24}
          fill="white"
          transform="translate(0.151855)"
        />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
