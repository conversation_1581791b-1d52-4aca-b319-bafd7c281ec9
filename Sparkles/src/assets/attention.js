import * as React from "react";
import Svg, { Path } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={32}
    height={32}
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M16.1667 27.3335C17.6334 27.3353 19.0861 27.0473 20.4411 26.486C21.7962 25.9247 23.0269 25.1012 24.0627 24.0627C25.1012 23.0269 25.9247 21.7962 26.486 20.4411C27.0473 19.0861 27.3353 17.6334 27.3335 16.1667C27.3353 14.7 27.0473 13.2474 26.486 11.8924C25.9247 10.5373 25.1012 9.30656 24.0627 8.27075C23.0269 7.23233 21.7962 6.40881 20.4411 5.84752C19.0861 5.28622 17.6334 4.9982 16.1667 5.00001C14.7 4.9982 13.2474 5.28622 11.8924 5.84752C10.5373 6.40881 9.30656 7.23233 8.27075 8.27075C7.23233 9.30656 6.40881 10.5373 5.84752 11.8924C5.28622 13.2474 4.9982 14.7 5.00001 16.1667C4.9982 17.6334 5.28622 19.0861 5.84752 20.4411C6.40881 21.7962 7.23233 23.0269 8.27075 24.0627C9.30656 25.1012 10.5373 25.9247 11.8924 26.486C13.2474 27.0473 14.7 27.3353 16.1667 27.3335Z"
      stroke="#E33629"
      strokeWidth={2}
      strokeLinejoin="round"
    />
    <Path
      d="M16.9771 22.8395C16.7622 23.0544 16.4707 23.1751 16.1668 23.1751C15.8629 23.1751 15.5715 23.0544 15.3566 22.8395C15.1417 22.6246 15.021 22.3332 15.021 22.0293C15.021 21.7254 15.1417 21.4339 15.3566 21.219C15.5715 21.0041 15.8629 20.8834 16.1668 20.8834C16.4707 20.8834 16.7622 21.0041 16.9771 21.219C17.192 21.4339 17.3127 21.7254 17.3127 22.0293C17.3127 22.3332 17.192 22.6246 16.9771 22.8395Z"
      fill="#E33629"
      stroke="#E33629"
      strokeWidth={0.5}
    />
    <Path
      d="M16.167 9.46667V18.4001"
      stroke="#E33629"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default SVGComponent;
