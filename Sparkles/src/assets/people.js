import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rect } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={32}
    height={24}
    viewBox="0 0 32 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_327_2001)">
      <Path
        d="M20.0374 5.84615C20.0374 6.86621 19.6322 7.84449 18.9109 8.56579C18.1896 9.28708 17.2113 9.6923 16.1913 9.6923C15.1712 9.6923 14.1929 9.28708 13.4716 8.56579C12.7503 7.84449 12.3451 6.86621 12.3451 5.84615C12.3451 4.82609 12.7503 3.8478 13.4716 3.12651C14.1929 2.40522 15.1712 2 16.1913 2C17.2113 2 18.1896 2.40522 18.9109 3.12651C19.6322 3.8478 20.0374 4.82609 20.0374 5.84615ZM8.5 20.3261C8.53296 18.308 9.35778 16.3837 10.7966 14.9682C12.2354 13.5527 14.1729 12.7594 16.1913 12.7594C18.2096 12.7594 20.1471 13.5527 21.586 14.9682C23.0248 16.3837 23.8496 18.308 23.8825 20.3261C21.47 21.4336 18.8459 22.0046 16.1913 22C13.4467 22 10.8415 21.401 8.5 20.3261Z"
        stroke="white"
        strokeWidth={1.53846}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M28.2503 6.2503C28.2503 6.84712 28.0132 7.41949 27.5912 7.8415C27.1692 8.26352 26.5968 8.5006 26 8.5006C25.4032 8.5006 24.8308 8.26352 24.4088 7.8415C23.9868 7.41949 23.7497 6.84712 23.7497 6.2503C23.7497 5.65348 23.9868 5.08111 24.4088 4.6591C24.8308 4.23708 25.4032 4 26 4C26.5968 4 27.1692 4.23708 27.5912 4.6591C28.0132 5.08111 28.2503 5.65348 28.2503 6.2503ZM21.5 14.7222C21.5193 13.5415 22.0019 12.4156 22.8437 11.5874C23.6855 10.7593 24.8191 10.2951 26 10.2951C27.1809 10.2951 28.3145 10.7593 29.1563 11.5874C29.9981 12.4156 30.4807 13.5415 30.5 14.7222C29.0884 15.3702 27.5532 15.7043 26 15.7016C24.3942 15.7016 22.87 15.3511 21.5 14.7222Z"
        stroke="white"
        strokeWidth={1.4}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M8.2503 6.2503C8.2503 6.84712 8.01321 7.41949 7.5912 7.8415C7.16919 8.26352 6.59682 8.5006 6 8.5006C5.40318 8.5006 4.83081 8.26352 4.4088 7.8415C3.98678 7.41949 3.7497 6.84712 3.7497 6.2503C3.7497 5.65348 3.98678 5.08111 4.4088 4.6591C4.83081 4.23708 5.40318 4 6 4C6.59682 4 7.16919 4.23708 7.5912 4.6591C8.01321 5.08111 8.2503 5.65348 8.2503 6.2503ZM1.5 14.7222C1.51928 13.5415 2.00187 12.4156 2.84369 11.5874C3.6855 10.7593 4.8191 10.2951 6 10.2951C7.1809 10.2951 8.3145 10.7593 9.15631 11.5874C9.99813 12.4156 10.4807 13.5415 10.5 14.7222C9.08844 15.3702 7.55316 15.7043 6 15.7016C4.39419 15.7016 2.86998 15.3511 1.5 14.7222Z"
        stroke="white"
        strokeWidth={1.4}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_327_2001">
        <Rect width={31} height={24} fill="white" transform="translate(0.5)" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
