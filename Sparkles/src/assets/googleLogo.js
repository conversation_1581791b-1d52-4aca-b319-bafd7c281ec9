import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rect } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={18}
    height={18}
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_24_1806)">
      <Path
        d="M6.27054 0.592074C4.47206 1.21598 2.92105 2.40018 1.84534 3.97073C0.769623 5.54128 0.2259 7.4154 0.294034 9.31781C0.362168 11.2202 1.03857 13.0506 2.22388 14.5402C3.40919 16.0298 5.04094 17.1 6.87944 17.5936C8.36996 17.9782 9.93158 17.9951 11.4301 17.6429C12.7875 17.3379 14.0426 16.6857 15.0723 15.75C16.1439 14.7465 16.9218 13.4698 17.3223 12.0572C17.7575 10.5211 17.835 8.90573 17.5487 7.33504H9.17866V10.8071H14.026C13.9291 11.3608 13.7215 11.8894 13.4156 12.361C13.1097 12.8327 12.7118 13.2378 12.2457 13.5521C11.6538 13.9436 10.9865 14.2071 10.2868 14.3255C9.58499 14.456 8.86515 14.456 8.16335 14.3255C7.45205 14.1785 6.77918 13.8849 6.18757 13.4635C5.23715 12.7907 4.52352 11.8349 4.14851 10.7325C3.76715 9.6095 3.76715 8.39199 4.14851 7.26895C4.41545 6.48175 4.85674 5.76501 5.43944 5.17223C6.10628 4.48141 6.9505 3.9876 7.87951 3.74499C8.80851 3.50239 9.78639 3.52035 10.7058 3.79692C11.4241 4.01741 12.0809 4.40264 12.624 4.92192C13.1705 4.37817 13.7162 3.83301 14.2609 3.28645C14.5421 2.99254 14.8487 2.7127 15.1257 2.41176C14.2968 1.6404 13.3238 1.04019 12.2626 0.645512C10.33 -0.0562212 8.21535 -0.0750796 6.27054 0.592074Z"
        fill="white"
      />
      <Path
        d="M6.27053 0.592025C8.21518 -0.075582 10.3298 -0.05722 12.2626 0.644057C13.324 1.04141 14.2965 1.64452 15.1243 2.41874C14.843 2.71968 14.5463 3.00093 14.2594 3.29343C13.7138 3.83812 13.1687 4.38093 12.624 4.92187C12.0809 4.40259 11.4241 4.01736 10.7058 3.79687C9.78668 3.51933 8.80883 3.50033 7.87959 3.74194C6.95034 3.98356 6.10559 4.47646 5.43803 5.16656C4.85532 5.75934 4.41403 6.47608 4.14709 7.26328L1.23193 5.00624C2.27538 2.93703 4.08205 1.35425 6.27053 0.592025Z"
        fill="#E33629"
      />
      <Path
        d="M0.458551 7.24216C0.615237 6.46562 0.875368 5.71361 1.23199 5.00623L4.14714 7.26888C3.76579 8.39192 3.76579 9.60944 4.14714 10.7325C3.17589 11.4825 2.20418 12.2362 1.23199 12.9937C0.339231 11.2167 0.066956 9.19193 0.458551 7.24216Z"
        fill="#F8BD00"
      />
      <Path
        d="M9.1786 7.33362H17.5486C17.8349 8.9043 17.7575 10.5197 17.3222 12.0558C16.9217 13.4684 16.1439 14.745 15.0722 15.7486C14.1314 15.0146 13.1864 14.2861 12.2456 13.5521C12.7121 13.2374 13.1102 12.8319 13.4161 12.3597C13.722 11.8876 13.9294 11.3585 14.0259 10.8042H9.1786C9.1772 9.64831 9.1786 8.49096 9.1786 7.33362Z"
        fill="#587DBD"
      />
      <Path
        d="M1.23047 12.9938C2.20266 12.2438 3.17437 11.49 4.14562 10.7325C4.52139 11.8353 5.23604 12.7911 6.1875 13.4635C6.78095 13.8829 7.45528 14.1741 8.1675 14.3185C8.8693 14.449 9.58914 14.449 10.2909 14.3185C10.9907 14.2 11.6579 13.9366 12.2498 13.545C13.1906 14.2791 14.1356 15.0075 15.0764 15.7416C14.0469 16.6778 12.7918 17.3305 11.4342 17.6358C9.93573 17.9881 8.37411 17.9712 6.88359 17.5866C5.70474 17.2718 4.60361 16.717 3.64922 15.9568C2.63905 15.1548 1.814 14.1441 1.23047 12.9938Z"
        fill="#319F43"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_24_1806">
        <Rect width={18} height={18} fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
