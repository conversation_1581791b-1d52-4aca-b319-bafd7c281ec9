import * as React from "react";
import Svg, { G, Path, Defs } from "react-native-svg";
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const SVGComponent = (props) => (
  <Svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G filter="url(#filter0_d_24_2561)">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.4114 20.361C31.4114 14.665 26.9712 9.00917 20.0596 9.00917C14.4781 9.00917 11.7195 13.14 10.5422 15.3381H14.0321C14.2985 15.3381 14.5541 15.4439 14.7424 15.6323C14.9308 15.8207 15.0367 16.0762 15.0367 16.3426C15.0367 16.6091 14.9308 16.8646 14.7424 17.053C14.5541 17.2414 14.2985 17.3472 14.0321 17.3472H8.00459C7.73815 17.3472 7.48263 17.2414 7.29424 17.053C7.10584 16.8646 7 16.6091 7 16.3426V10.3151C7 10.0487 7.10584 9.79318 7.29424 9.60478C7.48263 9.41639 7.73815 9.31055 8.00459 9.31055C8.27102 9.31055 8.52654 9.41639 8.71494 9.60478C8.90333 9.79318 9.00917 10.0487 9.00917 10.3151V13.9578C10.4256 11.4624 13.6986 7 20.0596 7C28.2229 7 33.4206 13.7006 33.4206 20.361C33.4206 27.0214 28.2229 33.722 20.0596 33.722C16.1538 33.722 12.9029 32.1749 10.5864 29.8202C9.36009 28.5664 8.38667 27.0882 7.71928 25.4663C7.66915 25.3443 7.64355 25.2136 7.64393 25.0816C7.64431 24.9497 7.67068 24.8191 7.72152 24.6974C7.77236 24.5757 7.84668 24.4651 7.94024 24.3721C8.03379 24.2791 8.14475 24.2054 8.26678 24.1553C8.38881 24.1052 8.51952 24.0796 8.65145 24.08C8.78337 24.0803 8.91393 24.1067 9.03567 24.1575C9.1574 24.2084 9.26793 24.2827 9.36095 24.3763C9.45396 24.4698 9.52764 24.5808 9.57777 24.7028C10.1403 26.0711 10.9681 27.3429 12.0209 28.4117C13.9738 30.4008 16.7143 31.7128 20.0596 31.7128C26.9712 31.7128 31.4114 26.057 31.4114 20.361ZM19.055 25.3839V15.3381H21.0642V25.3839H19.055Z"
        fill="white"
      />
    </G>
    <Defs></Defs>
  </Svg>
);
export default SVGComponent;
