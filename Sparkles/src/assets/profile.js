import * as React from "react";
import Svg, { Path } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={25}
    height={24}
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M15.9979 5.84615C15.9979 6.86621 15.5926 7.84449 14.8714 8.56579C14.1501 9.28708 13.1718 9.6923 12.1517 9.6923C11.1317 9.6923 10.1534 9.28708 9.43208 8.56579C8.71079 7.84449 8.30557 6.86621 8.30557 5.84615C8.30557 4.82609 8.71079 3.8478 9.43208 3.12651C10.1534 2.40522 11.1317 2 12.1517 2C13.1718 2 14.1501 2.40522 14.8714 3.12651C15.5926 3.8478 15.9979 4.82609 15.9979 5.84615ZM4.46045 20.3261C4.49341 18.308 5.31823 16.3837 6.75704 14.9682C8.19585 13.5527 10.1334 12.7594 12.1517 12.7594C14.1701 12.7594 16.1076 13.5527 17.5464 14.9682C18.9852 16.3837 19.81 18.308 19.843 20.3261C17.4304 21.4336 14.8063 22.0046 12.1517 22C9.40711 22 6.80198 21.401 4.46045 20.3261Z"
      stroke="black"
      strokeWidth={1.53846}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default SVGComponent;
