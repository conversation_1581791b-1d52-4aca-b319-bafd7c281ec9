import * as React from "react";
import Svg, { Path } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={194}
    height={39}
    viewBox="0 0 194 39"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M3.57031 21.0078C4.54427 21.5578 5.52969 22.0562 6.52656 22.5031C7.5349 22.9385 8.5776 23.3109 9.65469 23.6203C10.7318 23.9182 11.8604 24.1474 13.0406 24.3078C14.2323 24.4682 15.4984 24.5484 16.8391 24.5484C18.4547 24.5484 19.8297 24.4453 20.9641 24.2391C22.0984 24.0214 23.0208 23.7292 23.7313 23.3625C24.4531 22.9844 24.9745 22.5375 25.2953 22.0219C25.6276 21.5062 25.7938 20.9448 25.7938 20.3375C25.7938 19.3635 25.387 18.5958 24.5734 18.0344C23.7599 17.4615 22.5052 17.175 20.8094 17.175C20.0646 17.175 19.2797 17.2266 18.4547 17.3297C17.6297 17.4214 16.7932 17.5245 15.9453 17.6391C15.1089 17.7536 14.2781 17.8625 13.4531 17.9656C12.6396 18.0573 11.8719 18.1031 11.15 18.1031C9.94688 18.1031 8.78958 17.9484 7.67813 17.6391C6.57813 17.3297 5.59844 16.8656 4.73906 16.2469C3.89115 15.6281 3.2151 14.8547 2.71094 13.9266C2.20677 12.9984 1.95469 11.9156 1.95469 10.6781C1.95469 9.94479 2.05208 9.21719 2.24688 8.49531C2.45313 7.77344 2.77396 7.08594 3.20938 6.43281C3.65625 5.76823 4.22917 5.15521 4.92813 4.59375C5.62708 4.02083 6.46927 3.52812 7.45469 3.11562C8.45156 2.70312 9.5974 2.38229 10.8922 2.15312C12.1984 1.9125 13.6823 1.79219 15.3438 1.79219C16.5469 1.79219 17.7557 1.86094 18.9703 1.99844C20.1849 2.12448 21.3651 2.30781 22.5109 2.54844C23.6682 2.78906 24.7797 3.08125 25.8453 3.425C26.9109 3.75729 27.9021 4.12969 28.8188 4.54219L26.4297 8.94219C25.6734 8.6099 24.8599 8.30625 23.9891 8.03125C23.1182 7.74479 22.2073 7.49844 21.2563 7.29219C20.3052 7.08594 19.3198 6.92552 18.3 6.81094C17.2917 6.6849 16.2604 6.62187 15.2063 6.62187C13.7052 6.62187 12.4677 6.73073 11.4938 6.94844C10.5313 7.16615 9.76354 7.44687 9.19063 7.79062C8.61771 8.12292 8.21667 8.50104 7.9875 8.925C7.76979 9.3375 7.66094 9.75 7.66094 10.1625C7.66094 10.9646 8.02188 11.6234 8.74375 12.1391C9.46563 12.6432 10.5656 12.8953 12.0438 12.8953C12.6396 12.8953 13.3214 12.8552 14.0891 12.775C14.8682 12.6833 15.6818 12.5859 16.5297 12.4828C17.3891 12.3797 18.2599 12.288 19.1422 12.2078C20.0359 12.1161 20.901 12.0703 21.7375 12.0703C23.3188 12.0703 24.7167 12.2479 25.9313 12.6031C27.1573 12.9583 28.1828 13.4682 29.0078 14.1328C29.8328 14.7859 30.4573 15.5823 30.8813 16.5219C31.3052 17.45 31.5172 18.4927 31.5172 19.65C31.5172 21.1969 31.1563 22.5776 30.4344 23.7922C29.724 24.9953 28.7042 26.0151 27.375 26.8516C26.0573 27.6766 24.4646 28.3068 22.5969 28.7422C20.7292 29.1661 18.6495 29.3781 16.3578 29.3781C14.8453 29.3781 13.3672 29.2807 11.9234 29.0859C10.4797 28.9026 9.09323 28.6391 7.76406 28.2953C6.44635 27.9401 5.18594 27.5219 3.98281 27.0406C2.79115 26.5479 1.68542 26.0094 0.665625 25.425L3.57031 21.0078ZM39.9391 12.5344H35.0406V8.71875H39.9391V2.41094H45.0266V8.71875H49.925V12.5344H45.0266V29H39.9391V12.5344ZM56.8688 31.5438C57.3958 31.8875 57.9688 32.1911 58.5875 32.4547C59.2063 32.7182 59.8479 32.9359 60.5125 33.1078C61.1885 33.2797 61.876 33.4057 62.575 33.4859C63.274 33.5776 63.9672 33.6234 64.6547 33.6234C66.9922 33.6234 68.7911 33.1193 70.0516 32.1109C71.3234 31.1141 71.9594 29.613 71.9594 27.6078V26.9203C71.5125 27.2297 71.0083 27.5161 70.4469 27.7797C69.8969 28.0318 69.301 28.2495 68.6594 28.4328C68.0292 28.6161 67.3589 28.7594 66.6484 28.8625C65.9495 28.9542 65.2391 29 64.5172 29C62.9703 29 61.5839 28.7766 60.3578 28.3297C59.1432 27.8714 58.112 27.2068 57.2641 26.3359C56.4276 25.4536 55.7859 24.3708 55.3391 23.0875C54.8922 21.8042 54.6688 20.3375 54.6688 18.6875V8.71875H59.7391V17.2781C59.7391 19.7302 60.2031 21.5406 61.1313 22.7094C62.0708 23.8667 63.549 24.4453 65.5656 24.4453C66.9865 24.4453 68.2411 24.199 69.3297 23.7062C70.4297 23.2021 71.3063 22.5604 71.9594 21.7812V8.71875H77.0469V26.6797C77.0469 28.6161 76.7719 30.2948 76.2219 31.7156C75.6719 33.1479 74.8698 34.3339 73.8156 35.2734C72.7729 36.213 71.4896 36.912 69.9656 37.3703C68.4417 37.8401 66.7115 38.075 64.775 38.075C62.8844 38.075 61.1198 37.8745 59.4813 37.4734C57.8427 37.0724 56.3417 36.5281 54.9781 35.8406L56.8688 31.5438ZM83.7672 0.485937H88.8547V29H83.7672V0.485937ZM94.4234 18.7906C94.4234 17.4156 94.7042 16.1036 95.2656 14.8547C95.8271 13.5943 96.6349 12.4828 97.6891 11.5203C98.7432 10.5578 100.032 9.7901 101.556 9.21719C103.092 8.64427 104.833 8.35781 106.781 8.35781C108.718 8.35781 110.459 8.65 112.006 9.23437C113.565 9.80729 114.882 10.5979 115.959 11.6062C117.048 12.6146 117.879 13.8005 118.452 15.1641C119.036 16.5276 119.328 17.9885 119.328 19.5469C119.328 19.7417 119.322 19.9536 119.311 20.1828C119.311 20.4005 119.299 20.601 119.277 20.7844H100.095C100.313 21.3917 100.663 21.9474 101.144 22.4516C101.636 22.9557 102.249 23.3911 102.983 23.7578C103.716 24.1245 104.564 24.4109 105.527 24.6172C106.489 24.812 107.555 24.9094 108.723 24.9094C109.892 24.9094 111.055 24.7891 112.213 24.5484C113.37 24.2964 114.476 23.9812 115.53 23.6031L117.248 27.4875C116.584 27.7969 115.908 28.0719 115.22 28.3125C114.544 28.5417 113.834 28.7365 113.089 28.8969C112.356 29.0458 111.582 29.1604 110.769 29.2406C109.967 29.3208 109.119 29.3609 108.225 29.3609C105.945 29.3609 103.945 29.0859 102.227 28.5359C100.508 27.9859 99.0698 27.2354 97.9125 26.2844C96.7552 25.3333 95.8844 24.2161 95.3 22.9328C94.7156 21.6495 94.4234 20.2687 94.4234 18.7906ZM113.931 16.8141C113.748 16.2526 113.461 15.7312 113.072 15.25C112.694 14.7573 112.218 14.3333 111.645 13.9781C111.072 13.6115 110.408 13.325 109.652 13.1187C108.895 12.9125 108.065 12.8094 107.159 12.8094C106.197 12.8094 105.32 12.9182 104.53 13.1359C103.739 13.3536 103.046 13.6458 102.45 14.0125C101.854 14.3792 101.356 14.8031 100.955 15.2844C100.565 15.7656 100.279 16.2755 100.095 16.8141H113.931ZM136.12 2.13594H141.569V16.9344C141.569 18.149 141.735 19.2318 142.067 20.1828C142.399 21.1224 142.892 21.9187 143.545 22.5719C144.21 23.225 145.029 23.7234 146.003 24.0672C146.977 24.3995 148.111 24.5656 149.406 24.5656C150.69 24.5656 151.818 24.3995 152.792 24.0672C153.778 23.7234 154.597 23.225 155.25 22.5719C155.915 21.9187 156.413 21.1224 156.745 20.1828C157.078 19.2318 157.244 18.149 157.244 16.9344V2.13594H162.692V17.5875C162.692 19.3521 162.394 20.9562 161.798 22.4C161.203 23.8437 160.338 25.0812 159.203 26.1125C158.069 27.1437 156.677 27.9401 155.027 28.5016C153.388 29.063 151.515 29.3438 149.406 29.3438C147.298 29.3438 145.419 29.063 143.769 28.5016C142.13 27.9401 140.744 27.1437 139.609 26.1125C138.475 25.0812 137.61 23.8437 137.014 22.4C136.418 20.9562 136.12 19.3521 136.12 17.5875V2.13594ZM173.658 37.2844H168.57V8.71875H173.658V10.6266C174.105 10.3172 174.632 10.025 175.239 9.75C175.846 9.475 176.499 9.23437 177.198 9.02812C177.897 8.82187 178.619 8.66146 179.364 8.54687C180.109 8.42083 180.854 8.35781 181.598 8.35781C183.317 8.35781 184.893 8.6099 186.325 9.11406C187.769 9.61823 189.006 10.3286 190.038 11.2453C191.069 12.162 191.871 13.262 192.444 14.5453C193.028 15.8172 193.32 17.2266 193.32 18.7734C193.32 20.3891 193.017 21.85 192.409 23.1562C191.802 24.451 190.96 25.5625 189.883 26.4906C188.817 27.4073 187.545 28.1177 186.067 28.6219C184.601 29.1146 183.008 29.3609 181.289 29.3609C180.556 29.3609 179.828 29.3151 179.106 29.2234C178.396 29.1318 177.708 29.0057 177.044 28.8453C176.391 28.6849 175.772 28.4958 175.188 28.2781C174.615 28.049 174.105 27.8026 173.658 27.5391V37.2844ZM173.658 22.6234C174.173 23.0359 174.718 23.3854 175.291 23.6719C175.864 23.9583 176.448 24.1932 177.044 24.3766C177.64 24.5599 178.241 24.6974 178.848 24.7891C179.456 24.8693 180.046 24.9094 180.619 24.9094C181.788 24.9094 182.824 24.7547 183.73 24.4453C184.646 24.1245 185.414 23.6891 186.033 23.1391C186.663 22.5891 187.144 21.9417 187.477 21.1969C187.809 20.4521 187.975 19.6443 187.975 18.7734C187.975 17.9599 187.809 17.1922 187.477 16.4703C187.156 15.7484 186.686 15.1182 186.067 14.5797C185.46 14.0297 184.721 13.6 183.85 13.2906C182.991 12.9698 182.022 12.8094 180.945 12.8094C180.258 12.8094 179.57 12.8781 178.883 13.0156C178.195 13.1417 177.531 13.325 176.889 13.5656C176.259 13.8062 175.663 14.0984 175.102 14.4422C174.552 14.7859 174.07 15.1698 173.658 15.5937V22.6234Z"
      fill="white"
    />
  </Svg>
);
export default SVGComponent;
