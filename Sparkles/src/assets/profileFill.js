import * as React from "react";
import Svg, { Path } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={18}
    height={22}
    viewBox="0 0 18 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M12.9984 4.84615C12.9984 5.86621 12.5931 6.84449 11.8718 7.56579C11.1506 8.28708 10.1723 8.6923 9.15221 8.6923C8.13215 8.6923 7.15386 8.28708 6.43257 7.56579C5.71128 6.84449 5.30606 5.86621 5.30606 4.84615C5.30606 3.82609 5.71128 2.8478 6.43257 2.12651C7.15386 1.40522 8.13215 1 9.15221 1C10.1723 1 11.1506 1.40522 11.8718 2.12651C12.5931 2.8478 12.9984 3.82609 12.9984 4.84615ZM1.46094 19.3261C1.4939 17.308 2.31872 15.3837 3.75753 13.9682C5.19633 12.5527 7.13384 11.7594 9.15221 11.7594C11.1706 11.7594 13.1081 12.5527 14.5469 13.9682C15.9857 15.3837 16.8105 17.308 16.8435 19.3261C14.4309 20.4336 11.8068 21.0046 9.15221 21C6.4076 21 3.80247 20.401 1.46094 19.3261Z"
      fill="black"
      stroke="black"
      strokeWidth={1.53846}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default SVGComponent;
