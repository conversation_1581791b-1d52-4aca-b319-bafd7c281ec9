import * as React from "react";
import Svg, {
  G,
  Rect,
  Mask,
  Path,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
} from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={64}
    height={64}
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_220_2639)">
      <Rect width={64} height={64} rx={32} fill="black" />
      <Mask
        id="mask0_220_2639"
        style={{
          maskType: "luminance",
        }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={0}
        width={64}
        height={64}
      >
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.5493 0.0315552C13.1221 0.0822906 11.2825 0.193954 10.4464 0.362709C9.17014 0.620441 7.96487 1.01121 6.96253 1.52207C5.78487 2.12225 4.72939 2.88799 3.81801 3.79795C2.90454 4.70966 2.13573 5.76613 1.53314 6.94555C1.0237 7.94251 0.633157 9.14045 0.374327 10.4095C0.202176 11.254 0.088322 13.1073 0.0367095 14.5438C0.0157796 15.1309 0.00504071 15.8907 0.00504071 16.2412L0 47.7539C0 48.1029 0.0106293 48.863 0.0314496 49.4508C0.0821855 50.8779 0.193848 52.7175 0.362712 53.5536C0.620336 54.8297 1.01121 56.0351 1.52197 57.0375C2.12225 58.2152 2.88789 59.2705 3.79784 60.182C4.70956 61.0953 5.76613 61.8642 6.94544 62.4669C7.94251 62.9762 9.14045 63.3668 10.4094 63.6257C11.2539 63.7978 13.1073 63.9117 14.5438 63.9632C15.1307 63.9842 15.8906 63.995 16.2411 63.995L47.7538 64C48.1029 64 48.863 63.9895 49.4506 63.9686C50.8778 63.9177 52.7175 63.8062 53.5535 63.6373C54.8298 63.3796 56.0351 62.9887 57.0374 62.478C58.215 61.8778 59.2705 61.112 60.1819 60.2022C61.0952 59.2903 61.8641 58.234 62.4668 57.0546C62.9761 56.0575 63.3668 54.8597 63.6256 53.5906C63.7978 52.7461 63.9116 50.8926 63.9632 49.4562C63.9842 48.8692 63.9949 48.1094 63.9949 47.7589L64 16.2462C64 15.8971 63.9894 15.137 63.9684 14.5493C63.9176 13.1222 63.806 11.2826 63.6372 10.4465C63.3796 9.17025 62.9887 7.96497 62.4779 6.96264C61.8778 5.78487 61.1119 4.7295 60.2022 3.818C59.2903 2.90476 58.2338 2.13583 57.0545 1.53325C56.0574 1.02381 54.8596 0.633156 53.5905 0.374435C52.746 0.202282 50.8926 0.0884285 49.4562 0.0369263C48.8691 0.0157776 48.1093 0.00514984 47.7588 0.00514984L16.2461 0.000106812C15.897 0.000106812 15.1369 0.0106239 14.5493 0.0315552Z"
          fill="white"
        />
      </Mask>
      <G mask="url(#mask0_220_2639)">
        <Path
          d="M14.5493 0.0315552C13.1221 0.0822906 11.2825 0.193954 10.4464 0.362709C9.17014 0.620441 7.96487 1.01121 6.96253 1.52207C5.78487 2.12225 4.72939 2.88799 3.81801 3.79795C2.90454 4.70966 2.13573 5.76613 1.53314 6.94555C1.0237 7.94251 0.633157 9.14045 0.374327 10.4095C0.202176 11.254 0.088322 13.1073 0.0367095 14.5438C0.0157796 15.1309 0.00504071 15.8907 0.00504071 16.2412L0 47.7539C0 48.1029 0.0106293 48.863 0.0314496 49.4508C0.0821855 50.8779 0.193848 52.7175 0.362712 53.5536C0.620336 54.8297 1.01121 56.0351 1.52197 57.0375C2.12225 58.2152 2.88789 59.2705 3.79784 60.182C4.70956 61.0953 5.76613 61.8642 6.94544 62.4669C7.94251 62.9762 9.14045 63.3668 10.4094 63.6257C11.2539 63.7978 13.1073 63.9117 14.5438 63.9632C15.1307 63.9842 15.8906 63.995 16.2411 63.995L47.7538 64C48.1029 64 48.863 63.9895 49.4506 63.9686C50.8778 63.9177 52.7175 63.8062 53.5535 63.6373C54.8298 63.3796 56.0351 62.9887 57.0374 62.478C58.215 61.8778 59.2705 61.112 60.1819 60.2022C61.0952 59.2903 61.8641 58.234 62.4668 57.0546C62.9761 56.0575 63.3668 54.8597 63.6256 53.5906C63.7978 52.7461 63.9116 50.8926 63.9632 49.4562C63.9842 48.8692 63.9949 48.1094 63.9949 47.7589L64 16.2462C64 15.8971 63.9894 15.137 63.9684 14.5493C63.9176 13.1222 63.806 11.2826 63.6372 10.4465C63.3796 9.17025 62.9887 7.96497 62.4779 6.96264C61.8778 5.78487 61.1119 4.7295 60.2022 3.818C59.2903 2.90476 58.2338 2.13583 57.0545 1.53325C56.0574 1.02381 54.8596 0.633156 53.5905 0.374435C52.746 0.202282 50.8926 0.0884285 49.4562 0.0369263C48.8691 0.0157776 48.1093 0.00514984 47.7588 0.00514984L16.2461 0.000106812C15.897 0.000106812 15.1369 0.0106239 14.5493 0.0315552Z"
          fill="url(#paint0_linear_220_2639)"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M43.0939 36.2123C42.5239 35.9271 39.7214 34.5485 39.1988 34.3584C38.6763 34.1682 38.2964 34.0731 37.9164 34.6436C37.5363 35.2141 36.4439 36.4976 36.1114 36.8779C35.7789 37.2582 35.4464 37.3059 34.8764 37.0205C34.3063 36.7354 32.4697 36.1336 30.2924 34.1921C28.5979 32.6812 27.4538 30.815 27.1213 30.2445C26.7888 29.674 27.0859 29.3656 27.3713 29.0815C27.6276 28.8263 27.9412 28.416 28.2263 28.0832C28.5113 27.7504 28.6063 27.5127 28.7963 27.1325C28.9863 26.7521 28.8913 26.4193 28.7487 26.1341C28.6063 25.8489 27.4663 23.044 26.9913 21.9031C26.5286 20.7919 26.0587 20.9423 25.7088 20.9249C25.3766 20.9083 24.9963 20.9048 24.6162 20.9048C24.2362 20.9048 23.6187 21.0475 23.0961 21.6179C22.5737 22.1883 21.1011 23.5671 21.1011 26.3718C21.1011 29.1766 23.1437 31.8863 23.4287 32.2667C23.7137 32.647 27.4482 38.4029 33.1664 40.8712C34.5264 41.4583 35.5882 41.8088 36.416 42.0716C37.7816 42.5052 39.0242 42.4441 40.0064 42.2974C41.1016 42.1338 43.379 40.9187 43.854 39.5876C44.3289 38.2566 44.3289 37.1156 44.1865 36.8779C44.044 36.6403 43.664 36.4976 43.0939 36.2123ZM32.6934 50.4089H32.6858C29.2833 50.4075 25.9461 49.4939 23.0349 47.7668L22.3425 47.3558L15.1659 49.2379L17.0815 42.243L16.6306 41.5259C14.7325 38.5079 13.7301 35.0196 13.7315 31.4379C13.7357 20.9866 22.2419 12.4837 32.701 12.4837C37.7656 12.4856 42.5264 14.46 46.1064 18.0428C49.6864 21.6258 51.6569 26.3883 51.6549 31.4533C51.6507 41.9054 43.1446 50.4089 32.6934 50.4089ZM48.8312 15.3203C44.524 11.0095 38.796 8.63436 32.6933 8.63195C20.1189 8.63195 9.88484 18.8621 9.8798 31.4365C9.87816 35.4561 10.9286 39.3797 12.9249 42.8381L9.68848 54.6561L21.7822 51.4849C25.1144 53.3017 28.866 54.2591 32.6842 54.2608H32.6935H32.6936C45.2668 54.2608 55.5017 44.0293 55.5069 31.4547C55.5092 25.361 53.1385 19.631 48.8312 15.3203Z"
          fill="white"
        />
      </G>
    </G>
    <Defs>
      <LinearGradient
        id="paint0_linear_220_2639"
        x1={-0.0000686646}
        y1={64}
        x2={-0.0000686646}
        y2={0.0000370925}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#25CF43" />
        <Stop offset={1} stopColor="#61FD7D" />
      </LinearGradient>
      <ClipPath id="clip0_220_2639">
        <Rect width={64} height={64} rx={32} fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
