import * as React from "react";
import Svg, { G, Path, Defs } from "react-native-svg";
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const SVGComponent = (props) => (
  <Svg
    width={41}
    height={40}
    viewBox="0 0 41 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G filter="url(#filter0_d_24_1136)">
      <Path
        d="M19.9361 33.7117L7.57602 22.2768C0.840914 15.5047 10.666 2.38603 19.9361 12.9975C29.2061 2.42304 39.1237 15.5325 32.2961 22.2768L19.9361 33.7209V33.7117Z"
        fill="#D134AA"
        stroke="#D134AA"
        strokeWidth={1.48024}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </G>
    <Defs></Defs>
  </Svg>
);
export default SVGComponent;
