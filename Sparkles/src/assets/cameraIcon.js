import * as React from "react";
import Svg, { Rect, <PERSON> } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={47}
    height={48}
    viewBox="0 0 47 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Rect
      x={0.801136}
      y={1.30114}
      width={45.3977}
      height={45.3977}
      rx={22.6989}
      fill="black"
    />
    <Rect
      x={0.801136}
      y={1.30114}
      width={45.3977}
      height={45.3977}
      rx={22.6989}
      stroke="black"
      strokeWidth={1.60227}
    />
    <Path
      d="M21.239 19H17.95C16.83 19 16.27 19 15.842 19.218C15.4657 19.4097 15.1597 19.7157 14.968 20.092C14.75 20.52 14.75 21.08 14.75 22.2V27.8C14.75 28.92 14.75 29.48 14.968 29.907C15.16 30.284 15.465 30.59 15.842 30.782C16.269 31 16.829 31 17.947 31H29.553C30.671 31 31.23 31 31.657 30.782C32.0338 30.5902 32.3402 30.2838 32.532 29.907C32.75 29.48 32.75 28.921 32.75 27.803V22.197C32.75 21.079 32.75 20.519 32.532 20.092C32.34 19.7155 32.0337 19.4096 31.657 19.218C31.23 19 30.67 19 29.55 19H26.26M21.239 19H21.302M21.239 19C21.133 19 21.074 19 21.027 18.994C20.8882 18.9785 20.7541 18.9341 20.6335 18.8636C20.5128 18.7932 20.4083 18.6982 20.3266 18.5848C20.2449 18.4715 20.1879 18.3423 20.1592 18.2056C20.1305 18.0688 20.1308 17.9276 20.16 17.791C20.1828 17.7042 20.2095 17.6184 20.24 17.534L20.241 17.528C20.293 17.374 20.318 17.298 20.347 17.229C20.4894 16.8879 20.7236 16.5929 21.0235 16.3769C21.3234 16.1608 21.6774 16.0321 22.046 16.005C22.118 16 22.199 16 22.36 16H25.138C25.3 16 25.381 16 25.455 16.005C25.8235 16.0323 26.1772 16.1611 26.4769 16.3771C26.7766 16.5932 27.0106 16.888 27.153 17.229C27.182 17.297 27.207 17.374 27.259 17.529C27.305 17.667 27.329 17.736 27.339 17.791C27.3682 17.9275 27.3685 18.0687 27.3399 18.2054C27.3113 18.342 27.2543 18.4712 27.1727 18.5845C27.0911 18.6978 26.9867 18.7928 26.8662 18.8633C26.7457 18.9339 26.6118 18.9784 26.473 18.994C26.4022 18.9998 26.3311 19.0018 26.26 19M26.26 19H26.198H21.3M23.75 28C22.9544 28 22.1913 27.6839 21.6287 27.1213C21.0661 26.5587 20.75 25.7956 20.75 25C20.75 24.2044 21.0661 23.4413 21.6287 22.8787C22.1913 22.3161 22.9544 22 23.75 22C24.5456 22 25.3087 22.3161 25.8713 22.8787C26.4339 23.4413 26.75 24.2044 26.75 25C26.75 25.7956 26.4339 26.5587 25.8713 27.1213C25.3087 27.6839 24.5456 28 23.75 28Z"
      stroke="white"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default SVGComponent;
