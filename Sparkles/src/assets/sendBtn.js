import * as React from "react";
import Svg, { <PERSON>, <PERSON>, Defs, <PERSON>lip<PERSON><PERSON>, Rect } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_295_6106)">
      <Path
        d="M22.8181 3.75675L9.53333 21.2802L8.55802 11.9898L1 6.5L22.8181 3.75675ZM22.8181 3.75675L8.49983 12.0234"
        stroke="black"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </G>
    <Defs>
      <ClipPath id="clip0_295_6106">
        <Rect width={24} height={24} fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
