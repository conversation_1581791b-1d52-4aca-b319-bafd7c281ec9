import * as React from "react";
import Svg, { <PERSON>, Path } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Circle cx={9.5} cy={9.5} r={9.5} fill="black" />
    <Path
      d="M11.2626 6.67944C11.2626 7.14697 11.0769 7.59535 10.7463 7.92594C10.4157 8.25654 9.9673 8.44226 9.49977 8.44226C9.03225 8.44226 8.58387 8.25654 8.25327 7.92594C7.92268 7.59535 7.73696 7.14697 7.73696 6.67944C7.73696 6.21192 7.92268 5.76354 8.25327 5.43294C8.58387 5.10235 9.03225 4.91663 9.49977 4.91663C9.9673 4.91663 10.4157 5.10235 10.7463 5.43294C11.0769 5.76354 11.2626 6.21192 11.2626 6.67944ZM5.97461 13.3161C5.98972 12.3911 6.36776 11.5092 7.02721 10.8604C7.68667 10.2116 8.57469 9.84804 9.49977 9.84804C10.4249 9.84804 11.3129 10.2116 11.9723 10.8604C12.6318 11.5092 13.0098 12.3911 13.0249 13.3161C11.9192 13.8237 10.7165 14.0854 9.49977 14.0833C8.24183 14.0833 7.04781 13.8087 5.97461 13.3161Z"
      fill="white"
      stroke="white"
      strokeWidth={0.833332}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
export default SVGComponent;
