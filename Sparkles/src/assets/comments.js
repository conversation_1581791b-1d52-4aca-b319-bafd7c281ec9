import * as React from "react";
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rect } from "react-native-svg";
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const SVGComponent = (props) => (
  <Svg
    width={40}
    height={40}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_24_1141)">
      <G filter="url(#filter0_d_24_1141)">
        <Path
          d="M14.2802 31.176C15.9522 32 17.8402 32.456 19.8242 32.456C26.8002 32.456 32.4562 26.8 32.4562 19.824C32.4562 12.848 26.8002 7.19995 19.8322 7.19995C12.8642 7.19995 7.2002 12.856 7.2002 19.832C7.2002 22.424 7.9762 24.832 9.3122 26.832M14.2802 31.176L7.2002 32.456L9.3122 26.824M14.2802 31.176H14.2882"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M19.8318 20.3119C19.5598 20.3119 19.3438 20.0959 19.3438 19.8239C19.3438 19.5519 19.5598 19.3359 19.8318 19.3359"
          fill="white"
        />
        <Path
          d="M19.8318 20.3119C19.5598 20.3119 19.3438 20.0959 19.3438 19.8239C19.3438 19.5519 19.5598 19.3359 19.8318 19.3359"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M19.832 20.3119C20.104 20.3119 20.32 20.0959 20.32 19.8239C20.32 19.5519 20.104 19.3359 19.832 19.3359"
          fill="white"
        />
        <Path
          d="M19.832 20.3119C20.104 20.3119 20.32 20.0959 20.32 19.8239C20.32 19.5519 20.104 19.3359 19.832 19.3359"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M14.488 20.3119C14.216 20.3119 14 20.0959 14 19.8239C14 19.5519 14.216 19.3359 14.488 19.3359"
          fill="white"
        />
        <Path
          d="M14.488 20.3119C14.216 20.3119 14 20.0959 14 19.8239C14 19.5519 14.216 19.3359 14.488 19.3359"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M14.4883 20.3119C14.7603 20.3119 14.9763 20.0959 14.9763 19.8239C14.9763 19.5519 14.7603 19.3359 14.4883 19.3359"
          fill="white"
        />
        <Path
          d="M14.4883 20.3119C14.7603 20.3119 14.9763 20.0959 14.9763 19.8239C14.9763 19.5519 14.7603 19.3359 14.4883 19.3359"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M25.1755 20.3119C24.9035 20.3119 24.6875 20.0959 24.6875 19.8239C24.6875 19.5519 24.9035 19.3359 25.1755 19.3359"
          fill="white"
        />
        <Path
          d="M25.1755 20.3119C24.9035 20.3119 24.6875 20.0959 24.6875 19.8239C24.6875 19.5519 24.9035 19.3359 25.1755 19.3359"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M25.1758 20.3119C25.4478 20.3119 25.6638 20.0959 25.6638 19.8239C25.6638 19.5519 25.4478 19.3359 25.1758 19.3359"
          fill="white"
        />
        <Path
          d="M25.1758 20.3119C25.4478 20.3119 25.6638 20.0959 25.6638 19.8239C25.6638 19.5519 25.4478 19.3359 25.1758 19.3359"
          stroke="white"
          strokeWidth={1.6}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </G>
    </G>
    <Defs>
      <ClipPath id="clip0_24_1141">
        <Rect width={27} height={32} fill="white" transform="translate(6 6)" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
