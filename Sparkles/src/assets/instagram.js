import * as React from "react";
import Svg, {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Defs,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Stop,
  ClipPath,
} from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={64}
    height={64}
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <G clipPath="url(#clip0_220_2642)">
      <Rect width={64} height={64} rx={32} fill="black" />
      <G clipPath="url(#clip1_220_2642)">
        <Path
          d="M32.0104 0C18.6507 0 14.7435 0.0137875 13.984 0.0767998C11.2424 0.304757 9.53641 0.736554 7.67778 1.66215C6.24545 2.37362 5.1158 3.19829 4.00093 4.35433C1.97056 6.46257 0.740019 9.05626 0.294555 12.1394C0.0779912 13.6361 0.0149784 13.9413 0.00218238 21.5866C-0.00272798 24.135 0.00218238 27.4888 0.00218238 31.9874C0.00218238 45.3399 0.0169376 49.2443 0.0809416 50.0025C0.30244 52.671 0.720812 54.3499 1.60681 56.1864C3.30003 59.7018 6.53386 62.3408 10.3436 63.3255C11.6628 63.6652 13.1197 63.8523 14.9901 63.9409C15.7826 63.9754 23.8598 64 31.942 64C40.0242 64 48.1064 63.9901 48.8791 63.9508C51.0449 63.8488 52.3025 63.68 53.693 63.3205C57.5274 62.3309 60.7022 59.7313 62.4298 56.1667C63.2986 54.3745 63.7391 52.6316 63.9385 50.1024C63.9818 49.551 64 40.7591 64 31.9791C64 23.1975 63.9803 14.4219 63.937 13.8704C63.7352 11.3004 63.2947 9.57226 62.3978 7.74563C61.662 6.25036 60.8449 5.1337 59.6587 3.99198C57.5417 1.96939 54.9526 0.738514 51.8674 0.293437C50.3725 0.0772839 50.0748 0.0132797 42.4257 0H32.0104Z"
          fill="url(#paint0_radial_220_2642)"
        />
        <Path
          d="M32.0104 0C18.6507 0 14.7435 0.0137875 13.984 0.0767998C11.2424 0.304757 9.53641 0.736554 7.67778 1.66215C6.24545 2.37362 5.1158 3.19829 4.00093 4.35433C1.97056 6.46257 0.740019 9.05626 0.294555 12.1394C0.0779912 13.6361 0.0149784 13.9413 0.00218238 21.5866C-0.00272798 24.135 0.00218238 27.4888 0.00218238 31.9874C0.00218238 45.3399 0.0169376 49.2443 0.0809416 50.0025C0.30244 52.671 0.720812 54.3499 1.60681 56.1864C3.30003 59.7018 6.53386 62.3408 10.3436 63.3255C11.6628 63.6652 13.1197 63.8523 14.9901 63.9409C15.7826 63.9754 23.8598 64 31.942 64C40.0242 64 48.1064 63.9901 48.8791 63.9508C51.0449 63.8488 52.3025 63.68 53.693 63.3205C57.5274 62.3309 60.7022 59.7313 62.4298 56.1667C63.2986 54.3745 63.7391 52.6316 63.9385 50.1024C63.9818 49.551 64 40.7591 64 31.9791C64 23.1975 63.9803 14.4219 63.937 13.8704C63.7352 11.3004 63.2947 9.57226 62.3978 7.74563C61.662 6.25036 60.8449 5.1337 59.6587 3.99198C57.5417 1.96939 54.9526 0.738514 51.8674 0.293437C50.3725 0.0772839 50.0748 0.0132797 42.4257 0H32.0104Z"
          fill="url(#paint1_radial_220_2642)"
        />
        <Path
          d="M32.0104 0C18.6507 0 14.7435 0.0137875 13.984 0.0767998C11.2424 0.304757 9.53641 0.736554 7.67778 1.66215C6.24545 2.37362 5.1158 3.19829 4.00093 4.35433C1.97056 6.46257 0.740019 9.05626 0.294555 12.1394C0.0779912 13.6361 0.0149784 13.9413 0.00218238 21.5866C-0.00272798 24.135 0.00218238 27.4888 0.00218238 31.9874C0.00218238 45.3399 0.0169376 49.2443 0.0809416 50.0025C0.30244 52.671 0.720812 54.3499 1.60681 56.1864C3.30003 59.7018 6.53386 62.3408 10.3436 63.3255C11.6628 63.6652 13.1197 63.8523 14.9901 63.9409C15.7826 63.9754 23.8598 64 31.942 64C40.0242 64 48.1064 63.9901 48.8791 63.9508C51.0449 63.8488 52.3025 63.68 53.693 63.3205C57.5274 62.3309 60.7022 59.7313 62.4298 56.1667C63.2986 54.3745 63.7391 52.6316 63.9385 50.1024C63.9818 49.551 64 40.7591 64 31.9791C64 23.1975 63.9803 14.4219 63.937 13.8704C63.7352 11.3004 63.2947 9.57226 62.3978 7.74563C61.662 6.25036 60.8449 5.1337 59.6587 3.99198C57.5417 1.96939 54.9526 0.738514 51.8674 0.293437C50.3725 0.0772839 50.0748 0.0132797 42.4257 0H32.0104Z"
          fill="url(#paint2_radial_220_2642)"
        />
        <Path
          d="M32.0104 0C18.6507 0 14.7435 0.0137875 13.984 0.0767998C11.2424 0.304757 9.53641 0.736554 7.67778 1.66215C6.24545 2.37362 5.1158 3.19829 4.00093 4.35433C1.97056 6.46257 0.740019 9.05626 0.294555 12.1394C0.0779912 13.6361 0.0149784 13.9413 0.00218238 21.5866C-0.00272798 24.135 0.00218238 27.4888 0.00218238 31.9874C0.00218238 45.3399 0.0169376 49.2443 0.0809416 50.0025C0.30244 52.671 0.720812 54.3499 1.60681 56.1864C3.30003 59.7018 6.53386 62.3408 10.3436 63.3255C11.6628 63.6652 13.1197 63.8523 14.9901 63.9409C15.7826 63.9754 23.8598 64 31.942 64C40.0242 64 48.1064 63.9901 48.8791 63.9508C51.0449 63.8488 52.3025 63.68 53.693 63.3205C57.5274 62.3309 60.7022 59.7313 62.4298 56.1667C63.2986 54.3745 63.7391 52.6316 63.9385 50.1024C63.9818 49.551 64 40.7591 64 31.9791C64 23.1975 63.9803 14.4219 63.937 13.8704C63.7352 11.3004 63.2947 9.57226 62.3978 7.74563C61.662 6.25036 60.8449 5.1337 59.6587 3.99198C57.5417 1.96939 54.9526 0.738514 51.8674 0.293437C50.3725 0.0772839 50.0748 0.0132797 42.4257 0H32.0104Z"
          fill="url(#paint3_radial_220_2642)"
        />
        <Path
          d="M32.013 8.21777C25.5505 8.21777 24.7395 8.246 22.2012 8.36143C19.668 8.47735 17.9388 8.87811 16.4258 9.46614C14.8608 10.0735 13.5332 10.8859 12.2105 12.208C10.8869 13.5297 10.0739 14.8563 9.46411 16.4197C8.87417 17.9321 8.47261 19.6604 8.3586 22.1908C8.24508 24.7271 8.21533 25.5381 8.21533 31.9958C8.21533 38.4535 8.24409 39.2614 8.35909 41.7978C8.47561 44.3291 8.87666 46.057 9.4646 47.5689C10.0729 49.1328 10.8859 50.4594 12.209 51.7811C13.5312 53.1037 14.8588 53.9181 16.4228 54.5254C17.9368 55.1135 19.6665 55.5142 22.1992 55.6302C24.7375 55.7455 25.548 55.7738 32.01 55.7738C38.473 55.7738 39.2816 55.7456 41.8198 55.6301C44.353 55.5142 46.0842 55.1135 47.5982 54.5254C49.1627 53.9181 50.4884 53.1037 51.8105 51.7811C53.1341 50.4594 53.9472 49.1328 54.5569 47.5694C55.1419 46.057 55.5434 44.3287 55.6624 41.7983C55.7764 39.262 55.8062 38.4535 55.8062 31.9958C55.8062 25.5381 55.7764 24.7276 55.6624 22.1913C55.5434 19.6599 55.1419 17.9321 54.5569 16.4202C53.9472 14.8563 53.1341 13.5297 51.8105 12.208C50.4869 10.8854 49.1632 10.0729 47.5967 9.46612C46.0797 8.87811 44.3496 8.47735 41.8163 8.36143C39.2781 8.246 38.47 8.21777 32.0056 8.21777H32.013ZM29.8783 12.5028C30.5119 12.5018 31.2188 12.5028 32.013 12.5028C38.3664 12.5028 39.1195 12.5256 41.6284 12.6395C43.9485 12.7455 45.2077 13.1329 46.0465 13.4583C47.1569 13.8893 47.9487 14.4045 48.781 15.2367C49.6138 16.069 50.1294 16.8616 50.5617 17.9712C50.8874 18.8084 51.2756 20.0666 51.3812 22.385C51.4952 24.8916 51.52 25.6446 51.52 31.9903C51.52 38.3361 51.4952 39.0891 51.3812 41.5957C51.2751 43.914 50.8874 45.1723 50.5617 46.0095C50.1304 47.1191 49.6138 47.9092 48.781 48.7409C47.9482 49.5732 47.1574 50.0884 46.0465 50.5193C45.2087 50.8463 43.9485 51.2327 41.6284 51.3387C39.12 51.4526 38.3664 51.4774 32.013 51.4774C25.659 51.4774 24.906 51.4526 22.3975 51.3387C20.0775 51.2317 18.8183 50.8443 17.979 50.5189C16.8685 50.0879 16.0753 49.5727 15.2425 48.7405C14.4096 47.9082 13.8941 47.1176 13.4618 46.0075C13.1361 45.1703 12.7479 43.912 12.6423 41.5937C12.5283 39.0871 12.5055 38.3341 12.5055 31.9844C12.5055 25.6347 12.5283 24.8857 12.6423 22.3791C12.7484 20.0607 13.1361 18.8025 13.4618 17.9643C13.8931 16.8546 14.4096 16.062 15.2425 15.2298C16.0753 14.3976 16.8685 13.8824 17.979 13.4504C18.8178 13.1235 20.0775 12.7371 22.3975 12.6306C24.5927 12.5315 25.4434 12.5018 29.8783 12.4968L29.8783 12.5028ZM44.7149 16.4509C43.1385 16.4509 41.8594 17.7275 41.8594 19.3033C41.8594 20.8786 43.1385 22.1567 44.7149 22.1567C46.2914 22.1567 47.5704 20.8786 47.5704 19.3033C47.5704 17.728 46.2914 16.4499 44.7149 16.4499L44.7149 16.4509ZM32.013 19.7848C25.2644 19.7848 19.7929 25.2522 19.7929 31.9958C19.7929 38.7393 25.2644 44.2043 32.013 44.2043C38.7615 44.2043 44.2311 38.7393 44.2311 31.9958C44.2311 25.2522 38.7611 19.7848 32.0125 19.7848H32.013ZM32.013 24.0698C36.3934 24.0698 39.9449 27.6182 39.9449 31.9958C39.9449 36.3729 36.3934 39.9218 32.013 39.9218C27.6321 39.9218 24.0811 36.3729 24.0811 31.9958C24.0811 27.6182 27.6321 24.0698 32.013 24.0698Z"
          fill="white"
        />
      </G>
    </G>
    <Defs>
      <RadialGradient
        id="paint0_radial_220_2642"
        cx={0}
        cy={0}
        r={1}
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(61.9335 29.9976) rotate(164.25) scale(40.8924 29.461)"
      >
        <Stop stopColor="#FF005F" />
        <Stop offset={1} stopColor="#FC01D8" />
      </RadialGradient>
      <RadialGradient
        id="paint1_radial_220_2642"
        cx={0}
        cy={0}
        r={1}
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(16.9995 68.9293) rotate(-90) scale(51.086 54.2012)"
      >
        <Stop stopColor="#FFCC00" />
        <Stop offset={0.1242} stopColor="#FFCC00" />
        <Stop offset={0.5672} stopColor="#FE4A05" />
        <Stop offset={0.6942} stopColor="#FF0F3F" />
        <Stop offset={1} stopColor="#FE0657" stopOpacity={0} />
      </RadialGradient>
      <RadialGradient
        id="paint2_radial_220_2642"
        cx={0}
        cy={0}
        r={1}
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(33.6172 63.0974) rotate(-59.8703) scale(21.1245 27.5017)"
      >
        <Stop stopColor="#FFCC00" />
        <Stop offset={1} stopColor="#FFCC00" stopOpacity={0} />
      </RadialGradient>
      <RadialGradient
        id="paint3_radial_220_2642"
        cx={0}
        cy={0}
        r={1}
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(8.68343 2.60606) rotate(164.274) scale(40.4388 13.7775)"
      >
        <Stop stopColor="#780CFF" />
        <Stop offset={1} stopColor="#820BFF" stopOpacity={0} />
      </RadialGradient>
      <ClipPath id="clip0_220_2642">
        <Rect width={64} height={64} rx={32} fill="white" />
      </ClipPath>
      <ClipPath id="clip1_220_2642">
        <Rect width={64} height={64} fill="white" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default SVGComponent;
