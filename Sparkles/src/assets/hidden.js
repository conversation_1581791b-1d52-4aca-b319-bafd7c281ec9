import * as React from "react";
import Svg, { Rect, <PERSON> } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={47}
    height={47}
    viewBox="0 0 47 47"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Rect
      x={0.801136}
      y={0.801136}
      width={45.3977}
      height={45.3977}
      rx={22.6989}
      fill="black"
    />
    <Rect
      x={0.801136}
      y={0.801136}
      width={45.3977}
      height={45.3977}
      rx={22.6989}
      stroke="black"
      strokeWidth={1.60227}
    />
    <Path
      d="M23.5013 13.7083C20.8017 13.7083 18.6055 15.9045 18.6055 18.6041V21.5416H17.6263C16.5463 21.5416 15.668 22.4199 15.668 23.4999V31.3333C15.668 32.4133 16.5463 33.2916 17.6263 33.2916H29.3763C30.4563 33.2916 31.3346 32.4133 31.3346 31.3333V23.4999C31.3346 22.4199 30.4563 21.5416 29.3763 21.5416H28.3971V18.6041C28.3971 15.9045 26.2009 13.7083 23.5013 13.7083ZM29.3763 23.4999L29.3783 31.3333H17.6263V23.4999H29.3763ZM20.5638 21.5416V18.6041C20.5638 16.9845 21.8818 15.6666 23.5013 15.6666C25.1208 15.6666 26.4388 16.9845 26.4388 18.6041V21.5416H20.5638Z"
      fill="white"
    />
  </Svg>
);
export default SVGComponent;
