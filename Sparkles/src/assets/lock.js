import * as React from "react";
import Svg, { Rect, <PERSON> } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={82}
    height={81}
    viewBox="0 0 82 81"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Rect
      x={0.929932}
      width={80.1399}
      height={80.1399}
      rx={4.007}
      fill="black"
    />
    <Path
      d="M40.4298 18.4166C34.3415 18.4166 29.3882 23.3699 29.3882 29.4583V36.0833H27.1799C24.7441 36.0833 22.7632 38.0642 22.7632 40.5V58.1666C22.7632 60.6024 24.7441 62.5833 27.1799 62.5833H53.6798C56.1156 62.5833 58.0965 60.6024 58.0965 58.1666V40.5C58.0965 38.0642 56.1156 36.0833 53.6798 36.0833H51.4715V29.4583C51.4715 23.3699 46.5182 18.4166 40.4298 18.4166ZM53.6798 40.5L53.6843 58.1666H27.1799V40.5H53.6798ZM33.8048 36.0833V29.4583C33.8048 25.8057 36.7773 22.8333 40.4298 22.8333C44.0824 22.8333 47.0548 25.8057 47.0548 29.4583V36.0833H33.8048Z"
      fill="white"
    />
  </Svg>
);
export default SVGComponent;
