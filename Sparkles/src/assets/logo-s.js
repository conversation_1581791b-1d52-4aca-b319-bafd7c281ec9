import * as React from "react";
import Svg, { Rect, <PERSON> } from "react-native-svg";
const SVGComponent = (props) => (
  <Svg
    width={25}
    height={26}
    viewBox="0 0 25 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Rect
      x={0.429932}
      y={0.930054}
      width={24.1399}
      height={24.1399}
      rx={1.207}
      fill="#D134AA"
    />
    <Path
      d="M6.68963 15.5485C7.11024 15.7861 7.5358 16.0013 7.9663 16.1943C8.40175 16.3823 8.85205 16.5432 9.31719 16.6768C9.78234 16.8054 10.2697 16.9044 10.7794 16.9737C11.294 17.0429 11.8408 17.0776 12.4198 17.0776C13.1175 17.0776 13.7113 17.033 14.2012 16.944C14.6911 16.85 15.0894 16.7238 15.3962 16.5654C15.708 16.4021 15.9331 16.2091 16.0717 15.9865C16.2152 15.7638 16.2869 15.5213 16.2869 15.2591C16.2869 14.8385 16.1112 14.5069 15.7599 14.2645C15.4086 14.017 14.8667 13.8933 14.1344 13.8933C13.8127 13.8933 13.4738 13.9156 13.1175 13.9601C12.7612 13.9997 12.4 14.0443 12.0338 14.0937C11.6726 14.1432 11.3138 14.1902 10.9576 14.2348C10.6062 14.2744 10.2747 14.2941 9.96295 14.2941C9.44338 14.2941 8.9436 14.2273 8.46361 14.0937C7.98857 13.9601 7.56549 13.7597 7.19436 13.4925C6.82819 13.2253 6.53624 12.8913 6.31851 12.4905C6.10078 12.0897 5.99192 11.622 5.99192 11.0876C5.99192 10.7709 6.03398 10.4567 6.1181 10.145C6.20717 9.83323 6.34573 9.53633 6.53376 9.25427C6.72675 8.96727 6.97416 8.70254 7.27601 8.46007C7.57786 8.21265 7.94156 7.99987 8.36712 7.82173C8.79762 7.6436 9.29245 7.50504 9.85161 7.40608C10.4157 7.30216 11.0565 7.2502 11.774 7.2502C12.2936 7.2502 12.8157 7.27989 13.3402 7.33927C13.8647 7.3937 14.3744 7.47288 14.8692 7.57679C15.369 7.68071 15.849 7.80689 16.3092 7.95534C16.7694 8.09884 17.1974 8.25966 17.5933 8.4378L16.5615 10.338C16.235 10.1945 15.8836 10.0633 15.5075 9.94457C15.1315 9.82086 14.7381 9.71447 14.3274 9.6254C13.9167 9.53633 13.4911 9.46705 13.0507 9.41757C12.6153 9.36314 12.1699 9.33592 11.7147 9.33592C11.0664 9.33592 10.532 9.38293 10.1114 9.47695C9.69574 9.57097 9.3642 9.6922 9.11679 9.84065C8.86937 9.98415 8.69618 10.1474 8.59721 10.3305C8.50319 10.5087 8.45619 10.6868 8.45619 10.865C8.45619 11.2113 8.61206 11.4959 8.9238 11.7185C9.23555 11.9363 9.71059 12.0451 10.3489 12.0451C10.6062 12.0451 10.9007 12.0278 11.2322 11.9932C11.5687 11.9536 11.92 11.9115 12.2862 11.867C12.6573 11.8225 13.0334 11.7829 13.4144 11.7482C13.8004 11.7086 14.174 11.6889 14.5352 11.6889C15.2181 11.6889 15.8218 11.7655 16.3463 11.9189C16.8758 12.0723 17.3186 12.2925 17.6749 12.5795C18.0312 12.8616 18.3009 13.2055 18.484 13.6113C18.6671 14.0121 18.7586 14.4624 18.7586 14.9622C18.7586 15.6302 18.6027 16.2265 18.291 16.751C17.9842 17.2706 17.5438 17.711 16.9698 18.0722C16.4007 18.4285 15.7129 18.7006 14.9063 18.8887C14.0997 19.0718 13.2016 19.1633 12.212 19.1633C11.5588 19.1633 10.9205 19.1212 10.297 19.0371C9.67347 18.9579 9.07473 18.8441 8.50072 18.6957C7.93166 18.5423 7.38735 18.3617 6.86777 18.1538C6.35315 17.9411 5.87563 17.7085 5.43523 17.4561L6.68963 15.5485Z"
      fill="white"
    />
  </Svg>
);
export default SVGComponent;
