import {createAsyncThunk} from '@reduxjs/toolkit';
import {postRequest} from '../../api/verbs';
import {LOGIN_URL, BASE_URL} from '@env';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '../../api/api';
import { clearAllUserData } from '../../utils/clearUserData';

const userLogin = createAsyncThunk('auth/userLogin', async payload => {
  try {
    console.log('[AUTH ACTIONS] Login attempt with payload:', payload);
    console.log('[AUTH ACTIONS] Environment variables:', { BASE_URL, LOGIN_URL });
    console.log('[AUTH ACTIONS] Login URL:', LOGIN_URL);

    // Use a fallback URL if LOGIN_URL is undefined
    const loginUrl = LOGIN_URL || 'auth/login';
    console.log('[AUTH ACTIONS] Using login URL:', loginUrl);

    try {
      const response = await postRequest(loginUrl, payload);
      console.log('[AUTH ACTIONS] Login response:', response.data ? 'Success' : 'No Data');

      if (!response.data) {
        throw new Error('No data in response');
      }

      if (!response.data.token) {
        throw new Error('No token in response');
      }

      // Token ve refreshToken'ı kaydet
      await AsyncStorage.setItem('token', response.data.token);

      // RefreshToken varsa kaydet
      if (response.data.refreshToken) {
        await AsyncStorage.setItem('refreshToken', response.data.refreshToken);
      }

      // Token'ı Axios headers'a ekle
      api.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`;

      console.log('[AUTH ACTIONS] Token ve user bilgileri kaydedildi');

      return response.data;
    } catch (requestError) {
      console.error('[AUTH ACTIONS] Request error details:', {
        message: requestError.message,
        status: requestError.response?.status,
        data: requestError.response?.data,
        config: requestError.config
      });

      // Check for specific error code for unregistered email
      if (requestError.response?.data?.errorCode === 'EMAIL_NOT_REGISTERED') {
        throw {
          message: requestError.response.data.message,
          errorCode: requestError.response.data.errorCode
        };
      }

      // Make sure we're throwing an object with a message property for consistent error handling
      if (typeof requestError.response?.data === 'string') {
        throw { message: requestError.response?.data };
      } else if (requestError.response?.data?.message) {
        throw { message: requestError.response.data.message };
      } else {
        throw { message: requestError.message || 'Girdiğiniz e-posta veya şifre hatalı. Lütfen kontrol ediniz.' };
      }
    }
  } catch (error) {
    console.error('[AUTH ACTIONS] Login error:', error.response?.data || error.message);

    // Ensure we're throwing an object with a message property
    if (typeof error === 'string') {
      throw { message: error };
    } else if (error.message) {
      throw { message: error.message };
    } else {
      throw { message: 'Girdiğiniz e-posta veya şifre hatalı. Lütfen kontrol ediniz.' };
    }
  }
});

const userCheck = createAsyncThunk('auth/userCheck', async (_, { rejectWithValue }) => {
  try {
    console.log('[AUTH ACTIONS] Starting user check...');
    const token = await AsyncStorage.getItem('token');
    const refreshToken = await AsyncStorage.getItem('refreshToken');

    // If no tokens at all, user is not logged in
    if (!token && !refreshToken) {
      console.log('[AUTH ACTIONS] No tokens found, user not logged in');
      return false;
    }

    // If we have a token, try to validate it by making a test API call
    if (token) {
      try {
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        console.log('[AUTH ACTIONS] Testing existing token...');

        // Test the token with a simple API call
        await api.get('/auth/me');
        console.log('[AUTH ACTIONS] Existing token is valid');
        return true;
      } catch (tokenError) {
        console.log('[AUTH ACTIONS] Existing token is invalid, will try to refresh');
        // Token is invalid, continue to refresh logic below
      }
    }

    // Try to refresh the token if we have a refresh token
    if (refreshToken) {
      try {
        console.log('[AUTH ACTIONS] Attempting to refresh token...');
        const response = await api.post('/auth/refresh-token', { refreshToken });
        const newToken = response.data.token || response.data.accessToken;
        const newRefreshToken = response.data.refreshToken;

        if (!newToken) {
          throw new Error('Refresh token response does not include new token');
        }

        // Save new tokens
        await AsyncStorage.setItem('token', newToken);
        if (newRefreshToken) {
          await AsyncStorage.setItem('refreshToken', newRefreshToken);
        }

        // Set token in API headers
        api.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;
        console.log('[AUTH ACTIONS] Token successfully refreshed');

        return true;
      } catch (refreshError) {
        console.error('[AUTH ACTIONS] Token refresh failed:', refreshError);

        // Check if refresh token is expired
        const isRefreshTokenExpired = refreshError.response?.data?.errorCode === 'REFRESH_TOKEN_EXPIRED' ||
                                     refreshError.response?.data?.errorCode === 'INVALID_REFRESH_TOKEN' ||
                                     refreshError.response?.data?.errorCode === 'USER_NOT_FOUND';

        if (isRefreshTokenExpired) {
          console.log('[AUTH ACTIONS] Refresh token expired, clearing all tokens');
          await AsyncStorage.multiRemove(['token', 'refreshToken']);
          delete api.defaults.headers.common['Authorization'];
        }

        return false;
      }
    }

    // No valid tokens available
    console.log('[AUTH ACTIONS] No valid tokens available');
    return false;

  } catch (error) {
    console.error('[AUTH ACTIONS] Token check error:', error);
    return rejectWithValue(error.message);
  }
});

const userLogout = createAsyncThunk('auth/userLogout', async () => {
  try {
    console.log('[AUTH ACTIONS] Starting comprehensive logout process...');

    // Clear all user data from AsyncStorage
    await clearAllUserData();

    // API headers'dan token'ı kaldır
    delete api.defaults.headers.common['Authorization'];

    console.log('[AUTH ACTIONS] Comprehensive logout completed successfully');
    return true;
  } catch (error) {
    console.error('[AUTH ACTIONS] Error during logout process:', error);
    throw error;
  }
});

// Comprehensive logout for token expiration scenarios
const comprehensiveLogout = createAsyncThunk('auth/comprehensiveLogout', async () => {
  try {
    console.log('[AUTH ACTIONS] Starting comprehensive logout due to token expiration...');

    // Clear all user data from AsyncStorage
    await clearAllUserData();

    // API headers'dan token'ı kaldır
    delete api.defaults.headers.common['Authorization'];

    console.log('[AUTH ACTIONS] Comprehensive logout completed successfully');
    return true;
  } catch (error) {
    console.error('[AUTH ACTIONS] Error during comprehensive logout:', error);
    // Even if there's an error, we should still clear what we can
    try {
      await AsyncStorage.multiRemove(['token', 'refreshToken', 'isLoggedIn']);
      delete api.defaults.headers.common['Authorization'];
    } catch (fallbackError) {
      console.error('[AUTH ACTIONS] Fallback cleanup also failed:', fallbackError);
    }
    throw error;
  }
});

export {userLogin, userCheck, userLogout, comprehensiveLogout};
