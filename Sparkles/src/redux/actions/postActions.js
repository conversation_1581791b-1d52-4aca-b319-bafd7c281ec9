import {createAsyncThunk} from '@reduxjs/toolkit';
import api, { clearPostCache } from '../../api/api';
import { addLike, addLikedPost, likePost, removeLike, removeLikedPost, setLikeStatus, unlikePost, updateLikeCount } from '../slices/postSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';


// Tüm feed postlarını çekme
export const fetchFeedPosts = createAsyncThunk(
  'posts/fetchFeedPosts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('posts/feed');
      console.log('API Response:', response.data.data.posts);
      return response.data.data.posts;
    } catch (error) {
      console.error('API Error:', error);
      return rejectWithValue(error.response?.data?.message || 'Network Error');
    }
  }
);

// kullanıcı postlarını çekme
export const fetchUserPosts = createAsyncThunk(
  'posts/fetchUserPosts',
  async (userId, { rejectWithValue }) => {
    try {
      console.log('Fetching posts for user ID:', userId);
      const response = await api.get(`users/${userId}/posts`);

      // Log the full response for debugging
      console.log('User posts API response structure:', {
        hasData: !!response.data,
        hasDataPosts: !!(response.data?.data?.posts),
        postsLength: response.data?.data?.posts?.length || 0
      });

      // Ensure we return an array even if the API response is unexpected
      const posts = response.data?.data?.posts || [];
      console.log('Returning posts array with length:', posts.length);

      return posts;
    } catch (error) {
      console.error('API Error when fetching user posts:', error);
      return rejectWithValue(error.response?.message || 'Network Error');
    }
  }
);

export const toggleLike = createAsyncThunk(
  'posts/toggleLike',
  async (postId, { getState, rejectWithValue }) => {
    try {
      // Get current user ID for optimistic updates
      const state = getState();
      const currentUserId = state.auth.user?.id || state.auth.user?._id;

      // Check if the post is already liked by the user
      const currentPost = state.getPostDetail.currentPost;
      const isCurrentlyLiked = currentPost?.likes?.includes(currentUserId) || false;

      console.log('Toggle like - before API call:', {
        postId,
        currentUserId,
        isCurrentlyLiked,
        currentLikeCount: currentPost?.likeCount
      });

      // Make the API call
      const token = getState().auth.token;
      const response = await api.post(
        `posts/${postId}/like`,
        {},
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Toggle like - API response:', response.data);

      // Clear cache for this post to ensure fresh data on next fetch
      clearPostCache(postId);

      // Extract data from the new backend response format
      const responseData = response.data.data || {};
      const newLikeCount = responseData.likeCount;
      const newIsLiked = responseData.isLiked;

      // Return detailed information for state updates
      return {
        postId,
        likeCount: newLikeCount,
        isLiked: newIsLiked,
        userId: currentUserId
      };
    } catch (error) {
      console.error('Toggle like error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);



// Postları temizleme
export const clearPosts = () => ({ type: 'posts/clearPosts' });



