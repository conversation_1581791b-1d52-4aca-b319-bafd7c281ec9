import api from '../../api/api';
import { createAsyncThunk } from '@reduxjs/toolkit';



// export const fetchMessages = (conversationId, page = 1, limit = 20) => {
//   return async (dispatch) => {
//     try {
//       console.log(`Fetching messages for conversation ${conversationId}, page ${page}`);
//       dispatch(fetchMessagesStart());

//       const response = await api.get(
//         `messages/conversations/${conversationId}`,
//         {
//           params: { page, limit },
//         }
//       );

//       console.log('API response received:', response.data);

//       dispatch(
//         fetchMessagesSuccess({
//           messages: response.data.data.messages,
//           currentPage: response.data.currentPage,
//           totalPages: response.data.totalPages,
//           total: response.data.total,
//         })
//       );
//     } catch (error) {
//       console.error('Error fetching messages:', error);
//       dispatch(fetchMessagesFailure(error.message));
//     }
//   };
// };

export const fetchConversationMessages = createAsyncThunk(
    'messages/fetchConversationMessages',
    async ({ conversationId, page = 1, limit = 20, isGroupChat = false }, { rejectWithValue }) => {
      try {
        // Önce conversationId'yi kontrol edelim
        if (!conversationId || typeof conversationId !== 'string') {
          throw new Error('Geçersiz conversationId');
        }

        console.log('✅ Geçerli conversationId:', conversationId, 'isGroupChat:', isGroupChat);

        // Use the appropriate endpoint based on whether this is a group chat or not
        // Fix: Use the correct API endpoint path with leading slash
        const endpoint = isGroupChat
          ? `/messages/groups/${conversationId}/messages`
          : `/messages/conversations/${conversationId}`;

        console.log('Using endpoint:', endpoint);

        const response = await api.get(endpoint, {
          params: { page, limit }
        });

        console.log('API Response - FULL DATA:', JSON.stringify(response.data, null, 2));

        // Check if the response has the expected structure
        if (response.data && response.data.data) {
          // Check if messages are available in the response
          if (response.data.data.conversations && response.data.data.conversations.messages) {
            console.log('✅ Found messages in data.conversations.messages, count:',
              response.data.data.conversations.messages.length);
          } else if (response.data.data.messages) {
            console.log('✅ Found messages in data.messages, count:',
              response.data.data.messages.length);
          } else {
            console.warn('⚠️ No messages found in the response');
          }
        } else {
          console.warn('⚠️ Unexpected response structure');
        }

        return response.data;
      } catch (err) {
        console.error('❌ Detaylı hata:', {
          errorMessage: err.message,
          conversationId,
          isString: typeof conversationId === 'string',
          isValid: !!conversationId
        });

        return rejectWithValue({
          message: err.response?.data?.message || err.message,
          status: err.response?.status
        });
      }
    }
  );

  export const sendMessage = createAsyncThunk(
    'messages/sendMessage',
    async ({ conversationId, messageData }, { rejectWithValue, dispatch }) => {
      try {
        console.log('Sending message:', messageData);

        // Temel payload
        const payload = {
          text: messageData.text,
          conversationId: conversationId
        };

        // Grup mesajı için özel alanlar
        if (messageData.groupId) {
          const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(messageData.groupId);
          if (!isValidObjectId) {
            console.error('Invalid groupId format');
            throw new Error('Geçersiz grup ID formatı');
          }
          payload.groupId = messageData.groupId;
        }
        // Bireysel mesaj için özel alanlar
        else if (messageData.recipientId) {
          payload.recipientId = messageData.recipientId;
        }

        // Endpoint belirleme - both regular and group messages use the same endpoint
        // Fix: Use the correct API endpoint path with leading slash
        const endpoint = '/messages/send';

        console.log('Sending to endpoint:', endpoint, 'Payload:', payload);

        const response = await api.post(endpoint, payload);

        // Başarılı yanıt işleme
        if (response.data?.status === 'success') {
          console.log('Message sent successfully:', response.data.data);

          // Update conversation last message in Redux instead of fetching all conversations
          try {
            const { updateConversationLastMessage } = require('../slices/conversationsSlice');
            dispatch(updateConversationLastMessage({
              conversationId: conversationId,
              message: response.data.data.message
            }));
          } catch (updateError) {
            console.error('Conversation update error:', updateError);
          }

          return {
            message: response.data.data.message,
            tempMessageId: messageData.tempMessageId, // Include temp ID for replacement
            ...response.data.data
          };
        } else {
          throw new Error('Beklenmeyen yanıt formatı');
        }

      } catch (error) {
        console.error('Message send error:', {
          error: error.message,
          response: error.response?.data,
          payload: messageData
        });

        return rejectWithValue({
          message: error.response?.data?.message || error.message,
          status: error.response?.status,
          tempMessageId: messageData.tempMessageId
        });
      }
    }
  );

  export const deleteMessage = createAsyncThunk(
    'messages/deleteMessage',
    async (messageId, { rejectWithValue, dispatch }) => {
      try {
        console.log('Deleting message with ID:', messageId);

        // Validate messageId
        if (!messageId || typeof messageId !== 'string') {
          throw new Error('Geçersiz messageId');
        }

        // Fix: Use the correct API endpoint path
        const response = await api.delete(`/messages/${messageId}`);

        // If successful, log the success
        if (response.data?.status === 'success') {
          console.log('Message deleted successfully');
          // Note: The conversation list will be updated when the user returns to MessageListScreen
          // via the useFocusEffect hook, avoiding unnecessary API calls
        }

        return { messageId, ...response.data };
      } catch (err) {
        console.error('Error deleting message:', {
          error: err.message,
          response: err.response?.data,
          messageId
        });

        return rejectWithValue({
          message: err.response?.data?.message || err.message,
          status: err.response?.status
        });
      }
    }
  );

