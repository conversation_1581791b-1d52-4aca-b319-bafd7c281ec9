import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../api/api";

export const fetchConversations = createAsyncThunk(
  'conversations/fetchConversations',
  async ({ page = 1, limit = 20 }, { rejectWithValue }) => {
    try {
      console.log('Fetching conversations with params:', { page, limit });

      const response = await api.get('/messages/conversations', {
        params: { page, limit },
      });

      console.log('API response status:', response.status);
      console.log('API response structure:', {
        hasData: !!response.data,
        hasConversations: !!(response.data?.data?.conversations),
        conversationsLength: response.data?.data?.conversations?.length || 0
      });

      // Log the full conversations data for debugging
      if (response.data?.data?.conversations) {
        console.log('FULL CONVERSATIONS DATA:', JSON.stringify(response.data.data.conversations));
      }

      // Check if we have the expected data structure
      if (!response.data?.data?.conversations) {
        console.error('Unexpected API response structure:', response.data);
        return rejectWithValue({
          message: 'API response does not contain conversations data',
          response: response.data
        });
      }

      // API'den gelen veriyi normalize ediyoruz
      const normalizedConversations = response.data.data.conversations.map(conv => {
        try {
          // Log the raw conversation data for debugging
          console.log('Raw conversation data:', conv);

          // Check if conversation has required fields
          if (!conv._id) {
            console.warn('Conversation missing _id:', conv);
          }

          // Check if this is a group conversation
          const isGroupChat = conv.type === 'group';

          // Handle group conversations differently than direct conversations
          if (isGroupChat) {
            console.log('Processing group conversation:', conv.name || 'Unnamed Group');

            return {
              _id: conv._id,
              type: 'group',
              name: conv.name || 'Group Chat',
              participants: conv.participants || [],
              participantCount: conv.participantCount || (conv.participants ? conv.participants.length : 0),
              groupImage: conv.groupImage,
              createdAt: conv.createdAt,
              updatedAt: conv.updatedAt,
              // Handle different possible formats of lastMessage
              lastMessage: conv.lastMessage ? (
                typeof conv.lastMessage === 'object' ?
                  {
                    _id: conv.lastMessage._id,
                    text: conv.lastMessage.text,
                    sender: conv.lastMessage.sender,
                    date: conv.lastMessage.createdAt || conv.lastMessageDate || conv.updatedAt
                  }
                  :
                  // If lastMessage is a string (message ID), preserve it as is
                  conv.lastMessage
              ) : null,
              // Add messages array for compatibility with local messages
              messages: conv.messages || [],
              // Add message count and unread count if available
              messageCount: conv.messageCount || (conv.messages ? conv.messages.length : 1),
              unreadCount: conv.unreadCount || 0 // Default to 0 if not available
            };
          }

          // For direct conversations, check if otherParticipant exists
          if (!conv.otherParticipant) {
            console.warn('Direct conversation missing otherParticipant:', conv);
            // Skip this conversation by returning null, we'll filter these out later
            return null;
          }

          return {
            _id: conv._id, // Keep the MongoDB _id format for consistency
            type: 'direct',
            createdAt: conv.createdAt,
            updatedAt: conv.updatedAt,
            otherParticipant: {
              _id: conv.otherParticipant._id,
              username: conv.otherParticipant.username,
              profilePicture: conv.otherParticipant.profilePicture,
              followerCount: conv.otherParticipant.followerCount,
              followingCount: conv.otherParticipant.followingCount,
              postCount: conv.otherParticipant.postCount
            },
            // Handle different possible formats of lastMessage
            lastMessage: conv.lastMessage ? (
              typeof conv.lastMessage === 'object' ?
                {
                  _id: conv.lastMessage._id,
                  text: conv.lastMessage.text,
                  date: conv.lastMessage.createdAt || conv.lastMessageDate || conv.updatedAt
                }
                :
                // If lastMessage is a string (message ID), preserve it as is
                conv.lastMessage
            ) : null,
            // Add messages array for compatibility with local messages
            messages: conv.messages || [],
            // Add message count and unread count if available
            messageCount: conv.messageCount || (conv.messages ? conv.messages.length : 1),
            // Use the actual unreadCount from the API, don't default to 1
            unreadCount: conv.unreadCount || 0 // Default to 0 if not available
          };
        } catch (err) {
          console.error('Error normalizing conversation:', err);
          return null; // Skip this conversation
        }
      }).filter(Boolean); // Remove any null entries

      console.log('Normalized conversations count:', normalizedConversations.length);

      return {
        ...response.data,
        data: {
          conversations: normalizedConversations
        }
      };
    } catch (error) {
      console.error('Error fetching conversations:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const startNewConversation = createAsyncThunk(
  'conversations/startNewConversation',
  async (conversationData, { rejectWithValue }) => {
    console.log('startNewConversation thunk çalıştı, gönderilen data:', conversationData);
    try {
      const response = await api.post(`/messages/conversations`, conversationData);
      console.log('API yanıtı:', response.data);
      return response.data;
    } catch (error) {
      console.error('API hatası:', error.response ? error.response.data : error.message);
      return rejectWithValue(error.response ? error.response.data : error.message);
    }
  }
);

export const startNewGroupConversation = createAsyncThunk(
  'conversations/startNewGroupConversation',
  async (groupData, { rejectWithValue }) => {
    console.log('startNewGroupConversation thunk çalıştı, gönderilen data:', groupData);

    // Validate participants array
    if (!groupData.participants || !Array.isArray(groupData.participants) || groupData.participants.length === 0) {
      console.error('Invalid participants data:', groupData.participants);
      return rejectWithValue({ message: 'Geçersiz katılımcı listesi' });
    }

    // Make sure we have a clean array of participant IDs
    const cleanData = {
      ...groupData,
      participants: [...groupData.participants] // Create a new array to avoid reference issues
    };

    console.log('Sending group data to API:', cleanData);

    try {
      const response = await api.post(`/messages/groups`, cleanData);
      console.log('Grup API yanıtı:', response.data);
      return response.data;
    } catch (error) {
      console.error('Grup API hatası:', error.response ? error.response.data : error.message);
      return rejectWithValue(error.response ? error.response.data : error.message);
    }
  }
);

export const deleteConversation = createAsyncThunk(
  'conversations/deleteConversation',
  async ({ conversationId, isGroupChat = false }, { rejectWithValue, dispatch }) => {
    try {
      console.log('Deleting conversation with ID:', conversationId, 'isGroupChat:', isGroupChat);

      // Validate conversationId
      if (!conversationId || typeof conversationId !== 'string') {
        throw new Error('Geçersiz conversationId');
      }

      // Use the appropriate endpoint based on whether this is a group chat or not
      const endpoint = isGroupChat
        ? `/messages/groups/${conversationId}`
        : `/messages/conversations/${conversationId}`;

      const response = await api.delete(endpoint);

      // If successful, return the conversation ID and response data
      if (response.data?.status === 'success') {
        console.log('Conversation deleted successfully');
      }

      return { conversationId, isGroupChat, ...response.data };
    } catch (err) {
      console.error('Error deleting conversation:', {
        error: err.message,
        response: err.response?.data,
        conversationId
      });

      return rejectWithValue({
        message: err.response?.data?.message || err.message,
        status: err.response?.status
      });
    }
  }
);