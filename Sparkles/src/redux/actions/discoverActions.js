import { createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../api/api';

// Fetch discover posts (For You section)
export const fetchDiscoverPosts = createAsyncThunk(
  'discover/fetchDiscoverPosts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('discover');
      console.log('Discover API Response:', response.data.data.posts);
      return response.data.data.posts;
    } catch (error) {
      console.error('Discover API Error:', error);
      return rejectWithValue(error.response?.data?.message || 'Network Error');
    }
  }
);

// Clear discover posts
export const clearDiscoverPosts = () => ({ type: 'discover/clearDiscoverPosts' });
