import { createAsyncThunk } from '@reduxjs/toolkit';
import api, { clearPostCache } from '../../api/api';


// Fetch comments for a post
export const fetchComments = createAsyncThunk(
  'comments/fetchComments',
  async (postId, { rejectWithValue }) => {
    try {
      const response = await api.get(`posts/${postId}/comments`);
      return response.data;
    } catch (err) {
      return rejectWithValue(err.response.data);
    }
  }
);

// Add a new comment
export const addComment = createAsyncThunk(
  'comments/addComment',
  async ({ postId, text, parentComment }, { rejectWithValue }) => {
    try {
      const response = await api.post(`posts/${postId}/comments`, {
        text,
        parentComment
      });

      // Clear cache for this post to ensure fresh data on next fetch
      clearPostCache(postId);

      return response.data;
    } catch (err) {
      return rejectWithValue(err.response.data);
    }
  }
);

export const addReply = createAsyncThunk(
  'comments/addReply',
  async ({ postId, parentCommentId, text, isSubReply, originalParentId }, { rejectWithValue }) => {
    try {
      // If this is a sub-reply (reply to a reply), use the original parent comment ID
      // This ensures that sub-replies are properly nested under the correct parent
      const effectiveParentId = isSubReply && originalParentId ? originalParentId : parentCommentId;

      const response = await api.post(`posts/${postId}/comments`, {
        text,
        parentComment: parentCommentId,
        isSubReply: isSubReply || false,
        originalParentId: originalParentId || null
      });

      // Clear cache for this post to ensure fresh data on next fetch
      clearPostCache(postId);

      // Make sure the response includes the parent field
      const commentData = response.data.data.comment;

      // Ensure the parent field is set correctly
      if (!commentData.parent) {
        commentData.parent = parentCommentId;
      }

      // Ensure we have all the necessary data for proper UI updates
      return {
        data: {
          parentCommentId,
          reply: {
            ...commentData,
            // Ensure parent field is correctly set for proper nesting in the UI
            parent: isSubReply ? originalParentId : parentCommentId
          },
          isSubReply,
          originalParentId
        }
      };
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);

// Delete a comment
export const deleteComment = createAsyncThunk(
  'comments/deleteComment',
  async ({ postId, commentId, isReply = false, parentCommentId = null }, { rejectWithValue }) => {
    try {
      await api.delete(`posts/${postId}/comments/${commentId}`);

      // Clear cache for this post to ensure fresh data on next fetch
      clearPostCache(postId);

      return { commentId, isReply, parentCommentId };
    } catch (err) {
      return rejectWithValue(err.response.data);
    }
  }
);

// Like/Unlike a comment
export const toggleCommentLike = createAsyncThunk(
  'comments/toggleCommentLike',
  async ({ postId, commentId }, { getState, rejectWithValue }) => {
    try {
      const response = await api.post(`posts/${postId}/comments/${commentId}/like`);

      return {
        commentId,
        isLiked: response.data.data.isLiked,
        likeCount: response.data.data.likeCount,
        comment: response.data.data.comment
      };
    } catch (err) {
      return rejectWithValue(err.response.data);
    }
  }
);