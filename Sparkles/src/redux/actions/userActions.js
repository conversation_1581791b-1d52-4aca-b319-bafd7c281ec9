import {createAsyncThunk} from '@reduxjs/toolkit';
import {getRequest, patchRequest} from '../../api/verbs';
import {USER_INFO_URL, PROFILE_UPDATE_URL, BASE_URL} from '@env';
import api, { clearUserProfileCache } from '../../api/api';
import { clearProfilePictureCache } from '../../utils/imagePrefetch';

// USER_INFO_URL çevre değişkeni boş olabilir, bu yüzden doğrudan string kullanacağız.
const getUserInfo = createAsyncThunk('user/getUserInfo', async (params, { rejectWithValue }) => {
  try {
    // 'auth/me' endpoint'ini doğrudan string olarak kullanıyoruz
    const userInfoUrl = 'auth/me';
    console.log('Kullanıcı bilgileri alınıyor. URL:', userInfoUrl);
    console.log('Çevre değişkeni USER_INFO_URL:', USER_INFO_URL);
    console.log('BASE_URL:', BASE_URL);

    const response = await getRequest(userInfoUrl, params);
    console.log("Kullanıcı bilgileri cevabı:", response.data);

    if (response.data && response.data.data && response.data.data.user) {
      // API yanıtını kontrol edelim
      const user = response.data.data.user;
      console.log("API'den gelen kullanıcı bilgileri:", user);
      console.log("kullanıcı id", user.id);

      // fullName alan yok ise username'i fullName olarak ekleyelim
      if (!user.fullName && user.username) {
        user.fullName = user.name || user.username;
      }

      // Takipçi ve takip edilen sayılarını kontrol et ve ekle
      if (response.data.data.stats) {
        user.followerCount = response.data.data.stats.followersCount || 0;
        user.followingCount = response.data.data.stats.followingCount || 0;
      } else {
        // API stats dönmüyorsa, varsayılan değerleri kullan
        if (!user.hasOwnProperty('followerCount')) {
          user.followerCount = 0;
        }
        if (!user.hasOwnProperty('followingCount')) {
          user.followingCount = 0;
        }
      }

      console.log("Düzenlenmiş kullanıcı bilgileri:", {
        id: user.id,
        username: user.username,
        followerCount: user.followerCount,
        followingCount: user.followingCount
      });

      return user;
    } else {
      console.error("API cevabı beklenen formatta değil:", response.data);
      return rejectWithValue("Kullanıcı bilgileri alınamadı");
    }
  } catch (error) {
    console.error("Kullanıcı bilgileri alınırken hata:", error);
    return rejectWithValue(error.message || "Kullanıcı bilgileri alınamadı");
  }
});

const profileUpdate = createAsyncThunk(
  'user/profileUpdate',
  async (formData, { rejectWithValue, getState }) => {
    try {
      // PROFILE_UPDATE_URL çevre değişkeni doğrudan string olarak kullanılabilir
      const profileUpdateUrl = 'users/profile';
      console.log('Profil güncelleniyor. URL:', profileUpdateUrl);

      // Get current user info to check if profile picture is being updated
      const state = getState();
      const currentUser = state.user?.userInfo || state.auth?.user;
      const oldProfilePicture = currentUser?.profilePicture;

      const response = await patchRequest(profileUpdateUrl, formData);
      console.log("Profil güncelleme cevabı:", response.data);

      if (response.data && response.data.data && response.data.data.user) {
        const updatedUser = response.data.data.user;

        // Clear API cache for user profile data
        console.log('[PROFILE UPDATE] Clearing user profile cache...');
        clearUserProfileCache(updatedUser.id);

        // If profile picture was updated, clear image cache
        if (updatedUser.profilePicture && updatedUser.profilePicture !== oldProfilePicture) {
          console.log('[PROFILE UPDATE] Profile picture updated, clearing image cache...');
          clearProfilePictureCache(oldProfilePicture);
          clearProfilePictureCache(updatedUser.profilePicture);
        }

        console.log('[PROFILE UPDATE] Caches cleared successfully');
        return updatedUser;
      } else {
        console.error("API cevabı beklenen formatta değil:", response.data);
        return rejectWithValue("Profil güncellenemedi");
      }
    } catch (error) {
      console.error("Profil güncellenirken hata:", error);
      return rejectWithValue(error.message || "Profil güncellenemedi");
    }
  }
);

// kullanıcı profili görüntüleme
export const getUserProfile = createAsyncThunk(
  'user/getUserProfile',
  async (userId, { rejectWithValue }) => {
    try {
      console.log("getUserProfile - Profil çekiliyor, userId:", userId);
      const response = await api.get(`users/${userId}`);

      console.log("getUserProfile - API yanıtı:", response.data);

      if (response.data && response.data.data) {
        const user = response.data.data.user;

        // Takipçi ve takip edilen sayılarını kontrol et ve ekle
        // if (response.data.data.stats) {
        //   user.followerCount = response.data.data.stats.followersCount || 0;
        //   user.followingCount = response.data.data.stats.followingCount || 0;
        // } else {
        //   // API stats dönmüyorsa, varsayılan değerleri kullan
        //   if (!user.hasOwnProperty('followerCount')) {
        //     user.followerCount = 0;
        //   }
        //   if (!user.hasOwnProperty('followingCount')) {
        //     user.followingCount = 0;
        //   }
        // }

        console.log("getUserProfile - Düzenlenmiş profil bilgileri:", {
          id: user.id,
          username: user.username,
          followerCount: user.followerCount,
          followingCount: user.followingCount
        });

        return user;
      } else {
        console.error("getUserProfile - API yanıtı beklenen formatta değil:", response.data);
        return rejectWithValue("Profil bilgileri alınamadı");
      }
    } catch (error) {
      console.error("getUserProfile - Hata:", error);
      return rejectWithValue(error.response?.data?.message || 'Profil yüklenemedi');
    }
  }
);

export {
    getUserInfo,
    profileUpdate
}