import { createAsyncThunk } from '@reduxjs/toolkit';
import { clearAllUserData } from '../../utils/clearUserData';
import { GLOBAL_RESET } from '../store';
import api, { clearApiCache } from '../../api/api';

/**
 * Comprehensive logout action that:
 * 1. Clears all AsyncStorage data
 * 2. Removes API authorization headers
 * 3. Dispatches global Redux state reset
 * 4. <PERSON>tionally calls logout API endpoint
 */
export const comprehensiveLogout = createAsyncThunk(
  'auth/comprehensiveLogout',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      console.log('[COMPREHENSIVE LOGOUT] Starting complete logout process...');

      // Step 1: Clear all AsyncStorage data
      console.log('[COMPREHENSIVE LOGOUT] Clearing AsyncStorage data...');
      await clearAllUserData();

      // Step 2: Remove API authorization headers and clear cache
      console.log('[COMPREHENSIVE LOGOUT] Removing API authorization headers...');
      delete api.defaults.headers.common['Authorization'];

      console.log('[COMPREHENSIVE LOGOUT] Clearing API cache...');
      clearApiCache();

      // Step 3: Optional - Call logout API endpoint to invalidate token on server
      try {
        console.log('[COMPREHENSIVE LOGOUT] Calling logout API endpoint...');
        await api.post('/auth/logout');
        console.log('[COMPREHENSIVE LOGOUT] Server logout successful');
      } catch (apiError) {
        // Don't fail the entire logout process if API call fails
        console.warn('[COMPREHENSIVE LOGOUT] Server logout failed (continuing anyway):', apiError.message);
      }

      // Step 4: Dispatch global reset to clear all Redux state
      console.log('[COMPREHENSIVE LOGOUT] Dispatching global Redux reset...');
      dispatch({ type: GLOBAL_RESET });

      console.log('[COMPREHENSIVE LOGOUT] Complete logout process finished successfully');
      return { success: true, message: 'Logout completed successfully' };

    } catch (error) {
      console.error('[COMPREHENSIVE LOGOUT] Error during logout process:', error);

      // Even if there's an error, try to clear what we can
      try {
        delete api.defaults.headers.common['Authorization'];
        dispatch({ type: GLOBAL_RESET });
      } catch (fallbackError) {
        console.error('[COMPREHENSIVE LOGOUT] Fallback cleanup also failed:', fallbackError);
      }

      return rejectWithValue({
        message: error.message || 'Logout process encountered an error',
        error: error
      });
    }
  }
);

/**
 * Quick logout action for emergency situations
 * This is a simpler version that just clears local data without API calls
 */
export const quickLogout = createAsyncThunk(
  'auth/quickLogout',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      console.log('[QUICK LOGOUT] Starting quick logout...');

      // Clear local data
      await clearAllUserData();
      delete api.defaults.headers.common['Authorization'];
      clearApiCache();

      // Reset Redux state
      dispatch({ type: GLOBAL_RESET });

      console.log('[QUICK LOGOUT] Quick logout completed');
      return { success: true, message: 'Quick logout completed' };

    } catch (error) {
      console.error('[QUICK LOGOUT] Error during quick logout:', error);
      return rejectWithValue({
        message: error.message || 'Quick logout failed',
        error: error
      });
    }
  }
);
