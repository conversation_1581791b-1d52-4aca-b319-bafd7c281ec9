import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./slices/authSlice";
import likesReducer from './slices/likesSlice';
import commentsReducer from './slices/commentSlice';
import savedPostsReducer from './slices/savedPostSlice';
import userReducer from "./slices/userSlice"
import categorisReducer from "./slices/categoriesSlice"
import postReducer from "./slices/postSlice"
import searchReducer from "./slices/searchProfileSlice"
import followReducer from "./slices/followSlice"
import brandReducer from "./slices/brandSlice"
import photoReducer from "./slices/photoSlice"
import colorReducer from "./slices/colorSlice"
import postDetailReducer from "./slices/postDetailSlice"
import productLinkReducer from "./slices/productLinkSlice"
import postCreateReducer from "./slices/postCreateSlice"
import getPostDetailReducer from "./slices/getPostDetailSlice"
import conversationsReducer from "./slices/conversationsSlice"
import messagesReducer from "./slices/messagesSlice"
import discoverReducer from "./slices/discoverSlice"

// Global action type for resetting all slices
export const GLOBAL_RESET = 'GLOBAL_RESET';

// Root reducer that handles global reset
const rootReducer = (state, action) => {
  // If global reset action is dispatched, reset all state to initial values
  if (action.type === GLOBAL_RESET) {
    console.log('[STORE] Global reset triggered - clearing all Redux state');
    state = undefined;
  }

  return {
    posts: postReducer(state?.posts, action),
    auth: authReducer(state?.auth, action),
    user: userReducer(state?.user, action),
    likes: likesReducer(state?.likes, action),
    comments: commentsReducer(state?.comments, action),
    savedPosts: savedPostsReducer(state?.savedPosts, action),
    category: categorisReducer(state?.category, action),
    searchProfile: searchReducer(state?.searchProfile, action),
    follow: followReducer(state?.follow, action),
    brands: brandReducer(state?.brands, action),
    photo: photoReducer(state?.photo, action),
    colors: colorReducer(state?.colors, action),
    postDetail: postDetailReducer(state?.postDetail, action),
    productLink: productLinkReducer(state?.productLink, action),
    postCreate: postCreateReducer(state?.postCreate, action),
    getPostDetail: getPostDetailReducer(state?.getPostDetail, action),
    conversations: conversationsReducer(state?.conversations, action),
    messages: messagesReducer(state?.messages, action),
    discover: discoverReducer(state?.discover, action)
  };
};

const store = configureStore({
  reducer: rootReducer,
});

export default store;
