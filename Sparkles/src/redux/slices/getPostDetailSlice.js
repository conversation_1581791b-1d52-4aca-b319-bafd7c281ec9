import { createSlice } from '@reduxjs/toolkit';
import { fetchPostDetail } from '../actions/getPostDetailActions';
import { toggleLike } from '../actions/postActions';
import { deleteComment, addComment, addReply } from '../actions/commentActions';


const initialState = {
  currentPost: [],
  loading: false,
  error: null
};

const postDetailSlice = createSlice({
  name: 'postDetail',
  initialState,
  reducers: {
    clearPostDetail: (state) => {
      state.currentPost = null;
      state.loading = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPostDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPostDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.currentPost = action.payload;
      })
      .addCase(fetchPostDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle like toggle action
      .addCase(toggleLike.fulfilled, (state, action) => {
        const { likeCount, isLiked, userId, postId } = action.payload;

        console.log('getPostDetailSlice - toggleLike.fulfilled:', {
          likeCount,
          isLiked,
          userId,
          postId,
          currentPostId: state.currentPost?._id || state.currentPost?.id
        });

        if (state.currentPost) {
          console.log('Updating post detail state:', {
            oldLikeCount: state.currentPost.likeCount,
            newLikeCount: likeCount,
            oldLikes: state.currentPost.likes,
            isLiked,
            userId
          });

          // Update the like count
          state.currentPost.likeCount = likeCount;

          // Update the likes array
          if (isLiked) {
            // Add user to likes if not already there
            if (!state.currentPost.likes) {
              state.currentPost.likes = [userId];
            } else if (!state.currentPost.likes.includes(userId)) {
              state.currentPost.likes.push(userId);
            }
          } else {
            // Remove user from likes
            if (state.currentPost.likes) {
              state.currentPost.likes = state.currentPost.likes.filter(id => id !== userId);
            }
          }

          console.log('Updated post detail state:', {
            newLikeCount: state.currentPost.likeCount,
            newLikes: state.currentPost.likes
          });
        }

      })
      .addCase(deleteComment.fulfilled, (state, action) => {
  if (state.currentPost) {
    const { commentId } = action.payload;

    // 1. comments array'inden silinen yorumun ID'sini kaldır
    state.currentPost.comments = state.currentPost.comments.filter(
      id => id !== commentId
    );

    // 2. commentCount'u azalt (eğer 0'ın altına düşmemesi için kontrol)
    state.currentPost.commentCount = Math.max(0, state.currentPost.commentCount - 1);
  }
})
      // Handle add comment action
      .addCase(addComment.fulfilled, (state, action) => {
        if (state.currentPost) {
          const newComment = action.payload.data.comment;

          // 1. Add the new comment ID to the comments array if it's a main comment (not a reply)
          if (!newComment.parent) {
            // Initialize comments array if it doesn't exist
            if (!state.currentPost.comments) {
              state.currentPost.comments = [];
            }

            // Add the new comment ID to the comments array
            state.currentPost.comments.push(newComment._id);

            // 2. Increment the comment count
            state.currentPost.commentCount = (state.currentPost.commentCount || 0) + 1;

            console.log('Updated post detail after adding comment:', {
              commentId: newComment._id,
              newCommentCount: state.currentPost.commentCount
            });
          }
        }
      })
      // Handle add reply action
      .addCase(addReply.fulfilled, (state, action) => {
        if (state.currentPost) {
          const { reply, isSubReply, parentCommentId } = action.payload.data;

          // We should NOT increment the comment count for replies
          // Only main comments (where parent is null) should be counted
          // All replies (whether to main comments or to other replies) should NOT affect the total comment count

          console.log('Reply added but comment count NOT updated:', {
            replyId: reply._id,
            isReply: true,
            parentCommentId,
            currentCommentCount: state.currentPost.commentCount
          });
        }
      })

  }
});

  export const { clearPostDetail, addPost } = postDetailSlice.actions;
  export default postDetailSlice.reducer;

