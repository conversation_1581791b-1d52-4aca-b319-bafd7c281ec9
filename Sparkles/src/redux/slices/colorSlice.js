// colorSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  selectedColors: [],
  // (<PERSON>steğe bağlı) uygulamada kullanabileceğin tüm renkleri sabit liste olarak buraya koyabilirsin
  availableColors: [
    { id: 1, name: '<PERSON><PERSON>', hex: '#000000' },
    { id: 2, name: '<PERSON><PERSON>', hex: '#FFFFFF' },
    { id: 3, name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', hex: '#FF0000' },
    { id: 4, name: '<PERSON><PERSON>', hex: '#0000FF' },
    { id: 5, name: '<PERSON><PERSON><PERSON>', hex: '#00FF00' },
    { id: 6, name: '<PERSON><PERSON><PERSON>', hex: '#FFFF00' },
    { id: 7, name: '<PERSON><PERSON>', hex: '#800080' },
    { id: 8, name: '<PERSON><PERSON>', hex: '#808080' },
    { id: 9, name: '<PERSON><PERSON><PERSON>', hex: '#FFA500' },
    { id: 10, name: 'Pembe', hex: '#FFC0CB' },
  ],
};

const colorSlice = createSlice({
  name: 'colors',
  initialState,
  reducers: {
    setSelectedColors(state, action) {
      state.selectedColors = action.payload;
    },
    toggleColorSelection(state, action) {
      const color = action.payload;
      const existingIndex = state.selectedColors.findIndex(c => c.id === color.id);
      if (existingIndex >= 0) {
        // Seçili ise çıkar
        state.selectedColors.splice(existingIndex, 1);
      } else {
        // Değilse ekle
        state.selectedColors.push(color);
      }
    },
    clearSelectedColors(state) {
      state.selectedColors = [];
    }
  },
});

export const {
  setSelectedColors,
  toggleColorSelection,
  clearSelectedColors,
} = colorSlice.actions;

export default colorSlice.reducer;
