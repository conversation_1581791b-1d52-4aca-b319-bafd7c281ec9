import { createSlice } from '@reduxjs/toolkit';
import { fetchConversationMessages, deleteMessage, sendMessage } from '../actions/messagesActions';

const initialState = {
  currentConversation: null,
  messages: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 1
  }
};

const messagesSlice = createSlice({
    name: 'messages',
    initialState,
    reducers: {
      clearMessages: (state) => {
        console.log('🧹 Mesajlar temizlendi');
        state.messages = [];
        state.currentConversation = null;
        state.pagination = {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 1
        };
      },
      addNewMessage: (state, action) => {
        console.log('➕ Yeni mesaj eklendi:', action.payload);

        // Log sender information for debugging
        if (action.payload.sender) {
          console.log('Mesaj gönderen bilgisi:', {
            senderId: action.payload.sender._id || action.payload.sender.id,
            senderUsername: action.payload.sender.username,
            isTemp: action.payload.sender._id === 'currentUser'
          });
        }

        // Use push instead of unshift to add messages to the end of the array
        state.messages.push(action.payload);
      },
      replaceTempMessage: (state, action) => {
        const { tempMessageId, realMessage } = action.payload;
        console.log('🔄 Replacing temporary message:', { tempMessageId, realMessageId: realMessage._id });

        // Find and replace the temporary message with the real one
        const tempIndex = state.messages.findIndex(msg => msg._id === tempMessageId);
        if (tempIndex !== -1) {
          state.messages[tempIndex] = realMessage;
          console.log('✅ Temporary message replaced successfully');
        } else {
          // If temp message not found, just add the real message
          state.messages.push(realMessage);
          console.log('⚠️ Temp message not found, added real message instead');
        }
      },
      resetAllMessages: (state) => {
        console.log('[MESSAGES SLICE] Resetting all messages to initial state');
        Object.assign(state, initialState);
      }
    },
    extraReducers: (builder) => {
      builder
        .addCase(fetchConversationMessages.pending, (state) => {
          console.log('⏳ Mesajlar yükleniyor...');
          state.loading = true;
          state.error = null;
        })
        .addCase(fetchConversationMessages.fulfilled, (state, action) => {
          console.log('🎉 Mesajlar başarıyla yüklendi - FULL PAYLOAD:', JSON.stringify(action.payload, null, 2));
          console.log('🎉 Mesajlar başarıyla yüklendi:', {
            messageCount: action.payload.data?.messages?.length || 'unknown',
            pagination: {
              page: action.payload.currentPage,
              totalPages: action.payload.totalPages
            }
          });

          state.loading = false;

          // Check the structure of the payload and extract messages accordingly
          let messagesExtracted = false;
          let newMessages = [];

          // Structure 1: data.conversations.messages (from backend controller)
          if (action.payload.data && action.payload.data.conversations && Array.isArray(action.payload.data.conversations.messages)) {
            state.currentConversation = action.payload.data.conversation || action.payload.data.conversations;
            newMessages = action.payload.data.conversations.messages;
            console.log('✅ Using structure 1 - messages count:', newMessages.length);
            messagesExtracted = true;
          }
          // Structure 2: data.messages (common API pattern)
          else if (action.payload.data && Array.isArray(action.payload.data.messages)) {
            state.currentConversation = action.payload.data.conversation;
            newMessages = action.payload.data.messages;
            console.log('✅ Using structure 2 - messages count:', newMessages.length);
            messagesExtracted = true;
          }
          // Structure 3: conversations.messages (alternative structure)
          else if (action.payload.conversations && Array.isArray(action.payload.conversations.messages)) {
            state.currentConversation = action.payload.conversation;
            newMessages = action.payload.conversations.messages;
            console.log('✅ Using structure 3 - messages count:', newMessages.length);
            messagesExtracted = true;
          }
          // Structure 4: root.messages array
          else if (Array.isArray(action.payload.messages)) {
            newMessages = action.payload.messages;
            console.log('✅ Using structure 4 - messages count:', newMessages.length);
            messagesExtracted = true;
          }
          // Structure 5: single message in data.message
          else if (action.payload.data && action.payload.data.message) {
            // If there's a single message in the response (e.g., from sending a new message)
            state.messages = [...state.messages, action.payload.data.message];
            console.log('✅ Using structure 5 - single message added');
            messagesExtracted = true;
          }

          // Handle pagination: if this is page 1, replace all messages; otherwise, prepend older messages
          if (messagesExtracted && newMessages.length > 0) {
            const currentPage = action.payload.currentPage || action.payload.data?.pagination?.page || 1;

            if (currentPage === 1) {
              // First page: replace all messages
              state.messages = newMessages;
              console.log('📄 Page 1: Replaced all messages, count:', state.messages.length);
            } else {
              // Subsequent pages: prepend older messages to the beginning
              // Filter out any duplicate messages to avoid duplicates
              const existingMessageIds = new Set(state.messages.map(msg => msg._id));
              const uniqueNewMessages = newMessages.filter(msg => !existingMessageIds.has(msg._id));

              state.messages = [...uniqueNewMessages, ...state.messages];
              console.log('📄 Page', currentPage, ': Prepended', uniqueNewMessages.length, 'older messages, total count:', state.messages.length);
            }
          }

          // If none of the above structures matched, try additional fallbacks
          if (!messagesExtracted) {
            console.log('⚠️ Standard structures not found, trying fallbacks...');

            // Try to extract messages from any possible location in the payload
            let extractedMessages = [];

            // Fallback 1: Check if the payload itself is the messages array
            if (Array.isArray(action.payload)) {
              extractedMessages = action.payload;
              console.log('📦 Found messages at root level array');
            }
            // Fallback 2: Check if data.conversations is the messages array
            else if (action.payload.data && Array.isArray(action.payload.data.conversations)) {
              extractedMessages = action.payload.data.conversations;
              console.log('📦 Found messages in data.conversations array');
            }
            // Fallback 3: Check if data.conversations has a messages property that's not an array
            else if (action.payload.data && action.payload.data.conversations && action.payload.data.conversations.messages) {
              // If messages exists but isn't an array, try to convert it
              const messages = action.payload.data.conversations.messages;
              if (typeof messages === 'object' && !Array.isArray(messages)) {
                extractedMessages = Object.values(messages);
                console.log('📦 Converted messages object to array');
              }
            }

            // Handle pagination for fallback messages
            if (extractedMessages.length > 0) {
              const currentPage = action.payload.currentPage || action.payload.data?.pagination?.page || 1;

              if (currentPage === 1) {
                // First page: replace all messages
                state.messages = extractedMessages;
                console.log('📄 Fallback Page 1: Replaced all messages, count:', state.messages.length);
              } else {
                // Subsequent pages: prepend older messages to the beginning
                const existingMessageIds = new Set(state.messages.map(msg => msg._id));
                const uniqueNewMessages = extractedMessages.filter(msg => !existingMessageIds.has(msg._id));

                state.messages = [...uniqueNewMessages, ...state.messages];
                console.log('📄 Fallback Page', currentPage, ': Prepended', uniqueNewMessages.length, 'older messages, total count:', state.messages.length);
              }
              messagesExtracted = true;
            } else {
              // Only set empty array if this is page 1
              const currentPage = action.payload.currentPage || action.payload.data?.pagination?.page || 1;
              if (currentPage === 1) {
                state.messages = [];
              }
            }
            console.log('📊 Final messages count after fallback:', state.messages.length);
          }

          // Update pagination state with data from API response
          const paginationData = action.payload.data?.pagination || action.payload.pagination || {};
          state.pagination = {
            page: paginationData.page || action.payload.currentPage || 1,
            limit: paginationData.limit || action.payload.limit || 20,
            total: paginationData.total || action.payload.total || 0,
            totalPages: paginationData.totalPages || action.payload.totalPages || 1
          };

          console.log('📊 Updated pagination state:', state.pagination);
        })
        .addCase(fetchConversationMessages.rejected, (state, action) => {
          console.error('🔥 Mesajlar yüklenirken hata:', action.payload || action.error);
          state.loading = false;
          state.error = action.payload?.message || action.error.message;
        })
        .addCase(deleteMessage.pending, (state) => {
          console.log('⏳ Mesaj siliniyor...');
          state.loading = true;
          state.error = null;
        })
        .addCase(deleteMessage.fulfilled, (state, action) => {
          console.log('✅ Mesaj başarıyla silindi:', action.payload);
          state.loading = false;

          // Remove the deleted message from the messages array
          if (action.payload && action.payload.messageId) {
            state.messages = state.messages.filter(
              message => message._id !== action.payload.messageId
            );
          }
        })
        .addCase(deleteMessage.rejected, (state, action) => {
          console.error('🔥 Mesaj silinirken hata:', action.payload || action.error);
          state.loading = false;
          state.error = action.payload?.message || action.error.message;
        })
        .addCase(sendMessage.pending, (state) => {
          console.log('⏳ Mesaj gönderiliyor...');
          state.error = null;
        })
        .addCase(sendMessage.fulfilled, (state, action) => {
          console.log('✅ Mesaj başarıyla gönderildi:', action.payload);

          // Replace temporary message with real message from API
          if (action.payload.tempMessageId && action.payload.message) {
            const tempIndex = state.messages.findIndex(msg => msg._id === action.payload.tempMessageId);
            if (tempIndex !== -1) {
              state.messages[tempIndex] = action.payload.message;
              console.log('🔄 Temporary message replaced with real message');
            } else {
              // If temp message not found, just add the real message
              state.messages.push(action.payload.message);
              console.log('⚠️ Temp message not found, added real message instead');
            }
          } else if (action.payload.message) {
            // If no temp message ID, just add the new message
            state.messages.push(action.payload.message);
            console.log('➕ New message added to state');
          }
        })
        .addCase(sendMessage.rejected, (state, action) => {
          console.error('🔥 Mesaj gönderilirken hata:', action.payload || action.error);
          state.error = action.payload?.message || action.error.message;

          // Remove the temporary message if sending failed
          if (action.payload?.tempMessageId) {
            state.messages = state.messages.filter(msg => msg._id !== action.payload.tempMessageId);
            console.log('🗑️ Removed temporary message due to send failure');
          }
        });
    }
  });

  export const { clearMessages, addNewMessage, replaceTempMessage, resetAllMessages } = messagesSlice.actions;
  export default messagesSlice.reducer;