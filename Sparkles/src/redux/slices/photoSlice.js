import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  photoUri: null,
  filterName: 'No Filter',
};

const photoSlice = createSlice({
  name: 'photo',
  initialState,
  reducers: {
    setPhotoUri(state, action) {
      state.photoUri = action.payload;
    },
    setFilterName(state, action) {
      state.filterName = action.payload;
    },
    clearPhotoUri(state) {
      state.photoUri = null;
      state.filterName = 'No Filter';
    }
  },
});

export const { setPhotoUri, setFilterName, clearPhotoUri } = photoSlice.actions;
export default photoSlice.reducer;
