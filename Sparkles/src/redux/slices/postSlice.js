import {createSlice} from '@reduxjs/toolkit';
import {fetchFeedPosts, fetchUserPosts, likePost, toggleLike} from '../actions/postActions';
import {addComment, deleteComment} from '../actions/commentActions';



const initialState = {
  userPosts: {},
  feedPosts: [],
  posts: [],
  likedPosts: [],
  status: 'idle',
  loading: false,
  error: null,
};

const postsSlice = createSlice({
  name: 'posts',
  initialState,
  reducers: {

    clearPosts: (state) => {
      state.posts = [];
      state.error = null;
    },
    clearUserPosts: (state) => {
      state.userPosts = [];
      state.error = null;
      // Set loading to true when clearing posts to ensure loader is shown immediately
      state.loading = true;
      state.status = 'loading';
    },
    setPosts: (state, action) => {
      state.posts = action.payload;
    },

    resetAllPosts: (state) => {
      console.log('[POSTS SLICE] Resetting all posts to initial state');
      Object.assign(state, initialState);
    },

  },
  extraReducers: builder => {
    builder
       // Postlar yüklenirken
       .addCase(fetchFeedPosts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFeedPosts.fulfilled, (state, action) => {
        state.loading = false;
        state.feedPosts = action.payload;
      })
      .addCase(fetchFeedPosts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Kullanıcı Postları için
      .addCase(fetchUserPosts.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.status = 'loading';
      })
      .addCase(fetchUserPosts.fulfilled, (state, action) => {
        state.loading = false;
        state.status = 'succeeded';
        // Ensure userPosts is always an array
        state.userPosts = Array.isArray(action.payload) ? action.payload : [];
        console.log("User posts updated in store:", state.userPosts.length);
      })
      .addCase(fetchUserPosts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.status = 'failed';
      })
      .addCase(toggleLike.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(toggleLike.fulfilled, (state, action) => {
        const { postId, likeCount, isLiked, userId } = action.payload;

        console.log('Post slice: Updating like state', {
          postId,
          likeCount,
          isLiked,
          userId
        });

        // Update posts array
        state.posts = state.posts.map(post => {
          if (post.id === postId || post._id === postId) {
            console.log('Post slice: Found matching post', {
              oldLikeCount: post.likeCount,
              newLikeCount: likeCount,
              oldIsLiked: post.isLikedByUser,
              newIsLiked: isLiked
            });

            // Update like count and like status
            return {
              ...post,
              likeCount: likeCount,
              isLikedByUser: isLiked,
              // Update likes array if it exists
              likes: isLiked
                ? (post.likes ? [...post.likes.filter(id => id !== userId), userId] : [userId])
                : (post.likes ? post.likes.filter(id => id !== userId) : [])
            };
          }
          return post;
        });

        // Update feedPosts array if it exists
        if (state.feedPosts && state.feedPosts.length > 0) {
          state.feedPosts = state.feedPosts.map(post => {
            if (post.id === postId || post._id === postId) {
              console.log('Post slice: Found matching feed post', {
                oldLikeCount: post.likeCount,
                newLikeCount: likeCount,
                oldIsLiked: post.isLikedByUser,
                newIsLiked: isLiked
              });

              return {
                ...post,
                likeCount: likeCount,
                isLikedByUser: isLiked,
                likes: isLiked
                  ? (post.likes ? [...post.likes.filter(id => id !== userId), userId] : [userId])
                  : (post.likes ? post.likes.filter(id => id !== userId) : [])
              };
            }
            return post;
          });
        }

        // Update userPosts array if it exists
        if (state.userPosts && Array.isArray(state.userPosts) && state.userPosts.length > 0) {
          state.userPosts = state.userPosts.map(post => {
            if (post.id === postId || post._id === postId) {
              console.log('Post slice: Found matching user post', {
                oldLikeCount: post.likeCount,
                newLikeCount: likeCount,
                oldIsLiked: post.isLikedByUser,
                newIsLiked: isLiked
              });

              return {
                ...post,
                likeCount: likeCount,
                isLikedByUser: isLiked,
                likes: isLiked
                  ? (post.likes ? [...post.likes.filter(id => id !== userId), userId] : [userId])
                  : (post.likes ? post.likes.filter(id => id !== userId) : [])
              };
            }
            return post;
          });
        }

        // Update likedPosts array
        if (isLiked) {
          if (!state.likedPosts.includes(postId)) {
            state.likedPosts.push(postId);
          }
        } else {
          state.likedPosts = state.likedPosts.filter(id => id !== postId);
        }

        state.loading = false;
      })
      .addCase(toggleLike.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // Handle comment actions
      .addCase(addComment.fulfilled, (state, action) => {
        const newComment = action.payload.data.comment;
        const postId = newComment.post;

        console.log('Post slice: Updating comment count for post', {
          postId,
          commentId: newComment._id,
          isMainComment: !newComment.parent
        });

        // Only increment comment count for main comments (not replies)
        if (!newComment.parent) {
          // Helper function to update comment count in a post
          const updatePostCommentCount = (post) => {
            if (post.id === postId || post._id === postId) {
              console.log('Post slice: Found matching post for comment update', {
                oldCommentCount: post.commentCount,
                newCommentCount: (post.commentCount || 0) + 1
              });

              return {
                ...post,
                commentCount: (post.commentCount || 0) + 1,
                // Add comment ID to comments array if it exists
                comments: post.comments
                  ? [...post.comments, newComment._id]
                  : [newComment._id]
              };
            }
            return post;
          };

          // Update posts array
          state.posts = state.posts.map(updatePostCommentCount);

          // Update feedPosts array
          if (state.feedPosts && state.feedPosts.length > 0) {
            state.feedPosts = state.feedPosts.map(updatePostCommentCount);
          }

          // Update userPosts array
          if (state.userPosts && Array.isArray(state.userPosts) && state.userPosts.length > 0) {
            state.userPosts = state.userPosts.map(updatePostCommentCount);
          }
        }
      })
      .addCase(deleteComment.fulfilled, (state, action) => {
        const { commentId, isReply, parentCommentId } = action.payload;

        console.log('Post slice: Handling comment deletion', {
          commentId,
          isReply,
          parentCommentId
        });

        // Helper function to update comment count in a post
        const updatePostCommentCountForDeletion = (post) => {
          // Check if this post contains the deleted comment
          if (post.comments && post.comments.includes(commentId)) {
            console.log('Post slice: Found post containing deleted comment', {
              postId: post._id || post.id,
              oldCommentCount: post.commentCount,
              newCommentCount: Math.max((post.commentCount || 0) - 1, 0)
            });

            return {
              ...post,
              commentCount: Math.max((post.commentCount || 0) - 1, 0),
              comments: post.comments.filter(id => id !== commentId)
            };
          }
          return post;
        };

        // Only decrement for main comments (not replies)
        if (!isReply) {
          // Update posts array
          state.posts = state.posts.map(updatePostCommentCountForDeletion);

          // Update feedPosts array
          if (state.feedPosts && state.feedPosts.length > 0) {
            state.feedPosts = state.feedPosts.map(updatePostCommentCountForDeletion);
          }

          // Update userPosts array
          if (state.userPosts && Array.isArray(state.userPosts) && state.userPosts.length > 0) {
            state.userPosts = state.userPosts.map(updatePostCommentCountForDeletion);
          }
        }
      });





  },
});

export const {
  clearPosts,
  clearUserPosts,
  setPosts,
  resetAllPosts,
  addLike,
  setLikeStatus
} = postsSlice.actions;
export default postsSlice.reducer;
// Selector'lar
export const selectLikedPosts = (state) => state.posts.likedPosts;
export const selectIsPostLiked = (postId) => (state) =>
  state.posts.likedPosts.includes(postId);
