import { createSlice } from '@reduxjs/toolkit';
import { fetchDiscoverPosts } from '../actions/discoverActions';

const initialState = {
  discoverPosts: [],
  loading: false,
  error: null,
};

const discoverSlice = createSlice({
  name: 'discover',
  initialState,
  reducers: {
    clearDiscoverPosts: (state) => {
      state.discoverPosts = [];
      state.error = null;
    },
    resetDiscoverState: (state) => {
      console.log('[DISCOVER SLICE] Resetting discover state to initial values');
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    builder
      // Discover posts loading
      .addCase(fetchDiscoverPosts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDiscoverPosts.fulfilled, (state, action) => {
        state.loading = false;
        state.discoverPosts = action.payload;
      })
      .addCase(fetchDiscoverPosts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearDiscoverPosts, resetDiscoverState } = discoverSlice.actions;
export default discoverSlice.reducer;
