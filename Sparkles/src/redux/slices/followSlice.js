import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../api/api";

// Helper fonksiyonlar
const handlePending = (state) => {
  state.status = "loading";
  state.loading = true;
  state.error = null;
};

const handleRejected = (state, action) => {
  state.status = "failed";
  state.loading = false;

  // Enhanced error handling
  const error = action.payload;
  state.error = error;

  console.error("API Error Details:", {
    timestamp: new Date().toISOString(),
    errorType: error?.name || 'UnknownError',
    message: error?.message || 'No error message available',
    statusCode: error?.status || error?.response?.status || 'N/A',
    requestUrl: error?.config?.url || 'Unknown endpoint',
    requestMethod: error?.config?.method?.toUpperCase() || 'N/A',
    stackTrace: error?.stack || 'No stack trace available',
    fullError: error
  });
};

// Takip listesindeki ID'leri normalize eden yardımcı fonksiyon
const normalizeId = (id) => {
  if (!id) return null;

  // Eğer id bir string değilse, toString() ile string'e çevir
  return typeof id === 'string' ? id : String(id);
};

// Kullanıcının takip durumunu kontrol eden yardımcı fonksiyon
export const isUserFollowing = (following, targetUserId) => {
  if (!following || !following.length || !targetUserId) {
    console.log('Takip kontrolü başarısız: Eksik veri', {
      following: following ? `${following.length} eleman` : 'undefined',
      targetUserId
    });
    return false;
  }

  // Tüm olası ID formatlarını kontrol et
  const normalizedTargetId = normalizeId(targetUserId);
  console.log('Normalize edilmiş hedef ID:', normalizedTargetId);

  // Ayrıntılı log için takip listesinin ilk birkaç elemanını yazdır
  const sampleFollowing = following.slice(0, 3).map(f => ({
    id: f.id || f._id,
    username: f.username
  }));

  console.log('Takip listesi örneği:', sampleFollowing);

  const isFollowing = following.some(user => {
    // Takip edilen kullanıcı nesnesinin olası ID formatlarını kontrol et
    let followingId = null;

    if (user._id) {
      followingId = normalizeId(user._id);
    } else if (user.id) {
      followingId = normalizeId(user.id);
    } else if (typeof user === 'string') {
      // Direkt string ID durumu
      followingId = user;
    }

    const comparison = followingId === normalizedTargetId;

    if (comparison) {
      console.log('Takip eşleşmesi bulundu:', {
        followingId,
        targetId: normalizedTargetId,
        username: user.username
      });
    }

    return comparison;
  });

  console.log('Takip durumu sonucu:', isFollowing);
  return isFollowing;
};

// Async Thunk'lar
export const fetchFollowers = createAsyncThunk(
  "follow/fetchFollowers",
  async (userId, { rejectWithValue }) => {
    try {
      if (!userId) throw new Error("Kullanıcı ID'si geçersiz");
      console.log("Takipçiler getiriliyor, userId:", userId);
      const response = await api.get(`/follow/users/${userId}/followers`);
      return response.data.data.followers;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

export const fetchFollowing = createAsyncThunk(
  "follow/fetchFollowing",
  async (userId, { rejectWithValue }) => {
    try {
      if (!userId) throw new Error("Kullanıcı ID'si geçersiz");
      console.log("Takip edilenler getiriliyor, userId:", userId);
      const response = await api.get(`follow/users/${userId}/following`);
      return response.data.data.following;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

export const followUser = createAsyncThunk(
  "follow/followUser",
  async (userId, { rejectWithValue, dispatch, getState }) => {
    try {
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.error("Geçersiz kullanıcı ID'si:", userId);
        return rejectWithValue("Kullanıcı ID'si geçersiz");
      }

      const currentUser = getState().auth.user;
      if (!currentUser || (!currentUser._id && !currentUser.id)) {
        return rejectWithValue("Kullanıcı girişi yapılmamış");
      }

      const currentUserId = currentUser._id || currentUser.id;
      console.log(`Takip işlemi - Kullanıcı ${currentUserId}, Hedef: ${userId}`);

      // Zaten takip ediliyor mu kontrol et
      const currentFollowing = getState().follow.following;
      if (isUserFollowing(currentFollowing, userId)) {
        console.log("Zaten takip ediliyor, Redux state'i güncellemeden dön");
        // Özel bir payload döndür, böylece reducer'da bu durumu ele alabiliriz
        return {
          userId: userId,
          alreadyFollowing: true,
          username: getState().user.currentProfile?.username || "Unknown User",
          timestamp: Date.now() // Add timestamp to ensure the action is unique
        };
      }

      try {
        // Web ile uyumli endpoint kullanımı
        const response = await api.post(`/follow/users/${userId}/follow`);

        // API yanıt formatınıza göre düzenleyin; örneğin:
        if (response.data && response.data.status === 'success') {
          // Immediately update the Redux state with the new follow relationship
          // This ensures the UI updates immediately without waiting for the fetchFollowing call
          const followData = response.data.data.follow;
          const targetUsername = getState().user.currentProfile?.username || "Unknown User";

          // Return the follow data with the username for the reducer to use
          const result = {
            followData: followData,
            userId: userId,
            username: targetUsername,
            timestamp: Date.now() // Add timestamp to ensure the action is unique
          };

          // Also fetch updated data in the background
          // This is important to keep the state in sync with the server
          Promise.all([
            dispatch(fetchFollowing(currentUserId)),
            dispatch(fetchFollowers(userId))
          ]).catch(err => console.error("Error fetching updated follow data:", err));

          return result;
        } else {
          return rejectWithValue("Takip işlemi başarısız oldu");
        }
      } catch (error) {
        console.error("Takip işlemi API hatası:", error.response?.data || error.message);

        // "Zaten takip ediyorsunuz" hatası için özel işlem
        if (error.response?.data?.message === "You are already following this user" ||
            error.response?.data?.message === "Bu kullanıcıyı zaten takip ediyorsunuz") {
          console.log("API'den 'zaten takip ediliyor' hatası alındı, özel durum olarak işleniyor");

          // Özel bir payload döndür, böylece reducer'da bu durumu ele alabiliriz
          return {
            userId: userId,
            alreadyFollowing: true,
            username: getState().user.currentProfile?.username || "Unknown User",
            timestamp: Date.now() // Add timestamp to ensure the action is unique
          };
        }

        return rejectWithValue(error.response?.data?.message || error.message);
      }
    } catch (error) {
      console.error("Takip işlemi genel hatası:", error);
      return rejectWithValue(error.message || "Takip işlemi sırasında bir hata oluştu");
    }
  }
);

export const unfollowUser = createAsyncThunk(
  "follow/unfollowUser",
  async (userId, { rejectWithValue, dispatch, getState }) => {
    try {
      if (!userId || userId === 'undefined' || userId === 'null') {
        console.error("Geçersiz kullanıcı ID'si:", userId);
        return rejectWithValue("Kullanıcı ID'si geçersiz");
      }

      const currentUser = getState().auth.user;
      if (!currentUser || (!currentUser._id && !currentUser.id)) {
        return rejectWithValue("Kullanıcı girişi yapılmamış");
      }

      const currentUserId = currentUser._id || currentUser.id;
      console.log(`Takipten çıkarma işlemi - Kullanıcı ${currentUserId}, Hedef: ${userId}`);

      // Takip ediliyor mu kontrol et
      const currentFollowing = getState().follow.following;
      if (!isUserFollowing(currentFollowing, userId)) {
        return rejectWithValue("Bu kullanıcıyı takip etmiyorsunuz");
      }

      // Web ile uyumlu endpoint kullanımı
      await api.delete(`/follow/users/${userId}/follow`);

      // Immediately update the Redux state to reflect the unfollow action
      // This ensures the UI updates immediately after the API call succeeds
      console.log("Takipten çıkarma başarılı, Redux state güncelleniyor");

      // Return the userId immediately so the reducer can update the state
      // This is critical for immediate UI updates
      const result = {
        userId,
        timestamp: Date.now() // Add timestamp to ensure the action is unique
      };

      // Also fetch updated data in the background
      // This is important to keep the state in sync with the server
      Promise.all([
        dispatch(fetchFollowing(currentUserId)),
        dispatch(fetchFollowers(userId))
      ]).catch(err => console.error("Error fetching updated follow data after unfollow:", err));

      return result; // Takipten çıkarılan kullanıcının ID'sini döndür
    } catch (error) {
      console.error("Takipten çıkarma API hatası:", error.response?.data || error.message);
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

const followSlice = createSlice({
  name: "follow",
  initialState: {
    followers: [],
    following: [],
    status: "idle",
    error: null,
    loading: false
  },
  reducers: {
    clearFollowError: (state) => {
      state.error = null;
    },
    resetFollowStatus: (state) => {
      state.status = "idle";
    },
    'followers/reset': (state) => {
      state.followers = [];
    },
    'following/reset': (state) => {
      state.following = [];
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFollowers.pending, handlePending)
      .addCase(fetchFollowers.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.loading = false;
        state.followers = action.payload || [];
      })
      .addCase(fetchFollowers.rejected, handleRejected)
      .addCase(fetchFollowing.pending, handlePending)
      .addCase(fetchFollowing.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.loading = false;
        state.following = action.payload || [];
      })
      .addCase(fetchFollowing.rejected, handleRejected)

      // followUser
      .addCase(followUser.pending, handlePending)
      .addCase(followUser.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.loading = false;

        // Action payload'dan userId'yi al
        const userId = action.payload?.userId;
        const alreadyFollowing = action.payload?.alreadyFollowing;
        const username = action.payload?.username || "Unknown User";

        // Eğer "zaten takip ediliyor" durumu varsa, error state'ini güncelle
        if (alreadyFollowing) {
          console.log("Reducer: 'Zaten takip ediliyor' durumu işleniyor");

          // Set a special flag to indicate this is not a real error
          state.error = {
            message: "Bu kullanıcıyı zaten takip ediyorsunuz",
            isAlreadyFollowing: true
          };

          // Make sure the user is in the following list
          const normalizedUserId = normalizeId(userId);
          const existingIndex = state.following.findIndex(f => {
            const followingId = normalizeId(f.id || f._id);
            return followingId === normalizedUserId;
          });

          if (existingIndex === -1) {
            console.log("Reducer: Kullanıcı takip listesinde bulunamadı, ekleniyor");
            state.following.push({
              id: userId,
              _id: userId,
              username: username
            });
          }
        }
        // Normal başarılı takip durumu
        else if (userId) {
          // Make sure the user is in the following list
          const normalizedUserId = normalizeId(userId);
          const existingIndex = state.following.findIndex(f => {
            const followingId = normalizeId(f.id || f._id);
            return followingId === normalizedUserId;
          });

          if (existingIndex === -1) {
            console.log("Reducer: Kullanıcı takip listesine ekleniyor");
            // Add the user to the following list immediately
            state.following.push({
              id: userId,
              _id: userId,
              username: username
            });

            // Log the updated following list for debugging
            console.log(`Reducer: Kullanıcı takip listesine eklendi. Yeni liste uzunluğu: ${state.following.length}`);
          } else {
            console.log("Reducer: Kullanıcı zaten takip listesinde var, güncelleme yapılmıyor");
          }
        }
      })
      .addCase(followUser.rejected, (state, action) => {
        // Check if the error message contains "already following"
        const errorMessage = action.payload || "";
        const isAlreadyFollowingError =
          typeof errorMessage === 'string' && (
            errorMessage.includes("Bu kullanıcıyı zaten takip ediyorsunuz") ||
            errorMessage.includes("You are already following this user") ||
            errorMessage.includes("already following")
          );

        if (isAlreadyFollowingError) {
          console.log("Reducer: Reddedilen eylemde 'zaten takip ediliyor' hatası algılandı");

          // This is not a real error, mark as succeeded
          state.status = "succeeded";
          state.loading = false;

          // Set a special flag to indicate this is not a real error
          state.error = {
            message: errorMessage,
            isAlreadyFollowing: true
          };

          // Try to extract userId from the action meta
          const userId = action.meta?.arg;
          if (userId) {
            // Make sure the user is in the following list
            const normalizedUserId = normalizeId(userId);
            const existingIndex = state.following.findIndex(f => {
              const followingId = normalizeId(f.id || f._id);
              return followingId === normalizedUserId;
            });

            if (existingIndex === -1) {
              console.log("Reducer: Kullanıcı takip listesine ekleniyor (hata durumunda)");
              state.following.push({
                id: userId,
                _id: userId
              });
            }
          }
        } else {
          // Standart hata işleme
          handleRejected(state, action);
        }
      })

      // unfollowUser
      .addCase(unfollowUser.pending, handlePending)
      .addCase(unfollowUser.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.loading = false;

        // Action payload'dan userId'yi al
        const userId = action.payload?.userId;
        console.log("Unfollow reducer - removing user from following list:", userId);

        if (userId) {
          // Takip edilen kullanıcıyı listeden çıkar (farklı ID formatlarını kontrol et)
          const beforeLength = state.following.length;

          // Normalize the target ID for comparison
          const normalizedTargetId = normalizeId(userId);

          // Filter out the unfollowed user from the following list
          state.following = state.following.filter(f => {
            // Normalize IDs for comparison
            const followingId = normalizeId(f.id || f._id);
            return followingId !== normalizedTargetId;
          });

          const afterLength = state.following.length;
          const removedCount = beforeLength - afterLength;

          if (removedCount > 0) {
            console.log(`Unfollow reducer - removed ${removedCount} users from following list. New length: ${afterLength}`);
          } else {
            console.log(`Unfollow reducer - no users removed from following list. User ID ${normalizedTargetId} not found in list.`);
          }
        }
      })
      .addCase(unfollowUser.rejected, handleRejected)
  }
});

export const {
  clearFollowError,
  resetFollowStatus,
  'followers/reset': resetFollowers,
  'following/reset': resetFollowing
} = followSlice.actions;
export default followSlice.reducer;