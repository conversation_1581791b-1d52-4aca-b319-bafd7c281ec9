import { createSlice } from '@reduxjs/toolkit';
import { addComment, deleteComment, fetchComments, addReply, toggleCommentLike } from '../actions/commentActions';

const initialState = {
  comments: [],
  loading: false,
  error: null,
  status: 'idle',
  lastUpdated: null
};

const commentSlice = createSlice({
  name: 'comments',
  initialState,
  reducers: {
    clearComments: (state) => {
      state.comments = [];
      state.loading = false;
      state.error = null;
      state.status = 'idle';
    },
    // Yanıt eklemek için senkron reducer - enhanced to handle both regular replies and sub-replies
    addReplyToComment: (state, action) => {
      const { parentCommentId, reply, isOptimistic, replaceOptimistic, removeOptimistic } = action.payload;

      // Handle removing optimistic updates
      if (removeOptimistic) {
        const removeFromReplies = (comments) => {
          comments.forEach(comment => {
            if (comment.replies) {
              comment.replies = comment.replies.filter(r => r.id !== reply.id && r._id !== reply._id);
              removeFromReplies(comment.replies);
            }
          });
        };
        removeFromReplies(state.comments);
        state.lastUpdated = Date.now();
        return;
      }

      // <PERSON>le replacing optimistic updates with real data
      if (replaceOptimistic) {
        const replaceInReplies = (comments) => {
          comments.forEach(comment => {
            if (comment.replies) {
              const optimisticIndex = comment.replies.findIndex(r => r.id === replaceOptimistic || r._id === replaceOptimistic);
              if (optimisticIndex !== -1) {
                comment.replies[optimisticIndex] = reply;
                state.lastUpdated = Date.now();
                return true;
              }
              if (replaceInReplies(comment.replies)) return true;
            }
          });
          return false;
        };
        replaceInReplies(state.comments);
        return;
      }

      // Find the target parent comment (could be a main comment or a sub-comment)
      const findParentAndAddReply = (comments, targetParentId) => {
        for (const comment of comments) {
          // Check if this is the direct parent
          if (comment._id === targetParentId || comment.id === targetParentId) {
            if (!comment.replies) {
              comment.replies = [];
            }

            // Check if the reply already exists to avoid duplicates
            const replyExists = comment.replies.some(r =>
              r._id === reply._id || r.id === reply.id
            );

            if (!replyExists) {
              // Add the reply and sort by creation time to maintain chronological order
              comment.replies.push(reply);
              // Sort replies by creation time (oldest first)
              comment.replies.sort((a, b) => {
                const dateA = new Date(a.createdAt || 0);
                const dateB = new Date(b.createdAt || 0);
                return dateA - dateB;
              });
              state.lastUpdated = Date.now();
              return true;
            }
            return false;
          }

          // Recursively search in replies
          if (comment.replies && comment.replies.length > 0) {
            if (findParentAndAddReply(comment.replies, targetParentId)) {
              return true;
            }
          }
        }
        return false;
      };

      // Try to find and add the reply
      const success = findParentAndAddReply(state.comments, parentCommentId);

      if (!success) {
        console.warn(`Parent comment ${parentCommentId} not found for reply ${reply._id || reply.id}`);
      }
    }
  },
  extraReducers: (builder) => {
    // Fetch comments
    builder
      .addCase(fetchComments.pending, (state) => {
        state.loading = true;
        state.status = 'loading';
        state.error = null;
      })
      .addCase(fetchComments.fulfilled, (state, action) => {
        state.loading = false;
        state.status = 'succeeded';

        const rawComments = action.payload.data.comments;

        // Build nested structure from flat API response
        const buildNestedStructure = (comments) => {
          // Create a map to store all comments by their ID
          const commentMap = new Map();

          // First pass: create all comment objects with empty replies arrays
          comments.forEach(comment => {
            commentMap.set(comment._id, {
              ...comment,
              replies: []
            });
          });

          // Second pass: build the nested structure
          const rootComments = [];

          commentMap.forEach(comment => {
            if (comment.parent) {
              // This is a reply, find its parent and add it to the parent's replies
              const parentComment = commentMap.get(comment.parent);
              if (parentComment) {
                parentComment.replies.push(comment);
              }
            } else {
              // This is a root comment
              rootComments.push(comment);
            }
          });

          // Sort replies within each comment by creation time (oldest first)
          const sortReplies = (comment) => {
            if (comment.replies && comment.replies.length > 0) {
              comment.replies.sort((a, b) => {
                const dateA = new Date(a.createdAt || 0);
                const dateB = new Date(b.createdAt || 0);
                return dateA - dateB; // Oldest first for replies
              });

              // Recursively sort sub-replies
              comment.replies.forEach(reply => sortReplies(reply));
            }
          };

          // Sort all replies
          rootComments.forEach(comment => sortReplies(comment));

          return rootComments;
        };

        state.comments = buildNestedStructure(rawComments);
      })
      .addCase(fetchComments.rejected, (state, action) => {
        state.loading = false;
        state.status = 'failed';
        state.error = action.payload?.message || action.error.message;
      });

    // Add comment
    builder
      .addCase(addComment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addComment.fulfilled, (state, action) => {
        state.loading = false;
        const newComment = action.payload.data.comment;

        // Eğer bu bir yanıt ise (parentCommentId varsa)
        if (newComment.parentComment) {
          const parentComment = state.comments.find(c => c._id === newComment.parentComment);
          if (parentComment) {
            if (!parentComment.replies) {
              parentComment.replies = [];
            }
            // Add the reply and sort by creation time to maintain chronological order
            parentComment.replies.push(newComment);
            // Sort replies by creation time (oldest first)
            parentComment.replies.sort((a, b) => {
              const dateA = new Date(a.createdAt || 0);
              const dateB = new Date(b.createdAt || 0);
              return dateA - dateB;
            });
          }
        } else {
          // Ana yorum ise doğrudan ekle (en sona ekle - en eski yorum en üstte olacak şekilde)
          state.comments.push(newComment);
        }
      })
      .addCase(addComment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || action.error.message;
      });

    // Add reply (Yanıt eklemek için özel case)
    builder
      .addCase(addReply.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addReply.fulfilled, (state, action) => {
        state.loading = false;

        // Note: We don't add the reply here because we're using optimistic updates
        // The reply is added optimistically via addReplyToComment and then replaced with real data
        // This prevents duplicate replies from appearing
        console.log('Reply API call completed successfully - optimistic update will be replaced with real data');

        // Just update the lastUpdated timestamp to trigger any necessary re-renders
        state.lastUpdated = Date.now();
      })
      .addCase(addReply.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || action.error.message;
      });

    // Delete comment
    builder
    .addCase(deleteComment.pending, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(deleteComment.fulfilled, (state, action) => {
      state.loading = false;
      const { commentId, isReply, parentCommentId } = action.payload;

      // ID alanını kontrol et (hem _id hem id destekler)
      const findId = (item) => item._id || item.id;

      if (isReply) {
        // Yanıt silme
        const parentComment = state.comments.find(c => findId(c) === parentCommentId);
        if (parentComment && parentComment.replies) {
          parentComment.replies = parentComment.replies.filter(
            reply => findId(reply) !== commentId
          );
        }
      } else {
        // Ana yorum silme
        state.comments = state.comments.filter(
          comment => findId(comment) !== commentId
        );
      }
    })
    .addCase(deleteComment.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload?.message || action.error.message;
    });

    // Toggle comment like
    builder
    .addCase(toggleCommentLike.pending, (state) => {
      state.loading = true;
      state.error = null;
    })
    .addCase(toggleCommentLike.fulfilled, (state, action) => {
      state.loading = false;
      const { commentId, isLiked, likeCount, comment } = action.payload;

      // Helper function to update comment likes recursively
      const updateCommentLikes = (comments) => {
        for (let i = 0; i < comments.length; i++) {
          const currentComment = comments[i];

          // Check if this is the comment we want to update
          if (currentComment._id === commentId || currentComment.id === commentId) {
            // Update the comment with new like data
            currentComment.likes = comment.likes;
            currentComment.likeCount = likeCount;
            state.lastUpdated = Date.now();
            return true;
          }

          // If this comment has replies, search recursively
          if (currentComment.replies && currentComment.replies.length > 0) {
            if (updateCommentLikes(currentComment.replies)) {
              return true;
            }
          }
        }
        return false;
      };

      // Update the comment in the state
      updateCommentLikes(state.comments);
    })
    .addCase(toggleCommentLike.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload?.message || action.error.message;
    });

  }
});

export const { clearComments, addReplyToComment } = commentSlice.actions;
export default commentSlice.reducer;