import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  categories: [
    "<PERSON>z", "Ce<PERSON>", "Elbis<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>",
    "Panto<PERSON>", "Sweat<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>tek"
  ],
  // Her kategori için bağımsız veri yapısı
  categoryData: {
    /*
    Örnek yapı:
    "Bluz": {
      selected: false,
      brand: "",
      color: "",
      collarType: "",
      url: ""
    }
    */
  }
};

const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    // Kategori seçimini toggle etme
    toggleCategory: (state, action) => {
      const category = action.payload;

      // Eğer kategori verisi yoksa, başlat
      if (!state.categoryData[category]) {
        state.categoryData[category] = {
          selected: true,
          brand: "",
          color: "",
          collarType: "",
          url: ""
        };
      } else {
        // <PERSON><PERSON>i varsa selected durumunu tersine çevir
        state.categoryData[category].selected = !state.categoryData[category].selected;
      }
    },

    updateCategoryBrand: (state, action) => {
      console.log('Updating category brand:', action.payload);
      const { category, brand } = action.payload;
      if (state.categoryData[category]) {
        state.categoryData[category].brand = brand;
      }
    },

    // Kategori rengini güncelleme
    updateCategoryColor: (state, action) => {
      const { category, color } = action.payload;
      if (state.categoryData[category]) {
        state.categoryData[category].color = color;
      }
    },

    // Kategori yaka tipini güncelleme
    updateCategoryCollarType: (state, action) => {
      const { category, collarType } = action.payload;
      if (state.categoryData[category]) {
        state.categoryData[category].collarType = collarType;
      }
    },

    // Kategori URL'sini güncelleme
    updateCategoryUrl: (state, action) => {
      const { category, url } = action.payload;
      if (state.categoryData[category]) {
        state.categoryData[category].url = url;
      }
    },

    // Tüm kategori verisini güncelleme (isteğe bağlı)
    setCategoryData: (state, action) => {
      const { category, data } = action.payload;
      state.categoryData[category] = data;
    },

    // Reset all category data
    resetCategoryData: (state) => {
      console.log('[CATEGORIES SLICE] Resetting category data to initial state');
      Object.assign(state, initialState);
    }
  }
});

// Action'ları export etme
export const {
  toggleCategory,
  updateCategoryBrand,
  updateCategoryColor,
  updateCategoryCollarType,
  updateCategoryUrl,
  setCategoryData,
  resetCategoryData
} = categorySlice.actions;

// Selector'lar
export const selectCategories = (state) => state.category.categories;
export const selectCategoryData = (state) => state.category.categoryData;
export const selectSelectedCategories = (state) =>
  Object.entries(state.category.categoryData)
    .filter(([_, data]) => data.selected)
    .map(([category]) => category);

export default categorySlice.reducer;


 // brands:[