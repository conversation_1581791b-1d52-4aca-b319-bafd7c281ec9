import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  selectedBrands: [],
  availableBrands:[
    { id: 1, name: "Adidas" },
    { id: 2, name: "<PERSON>" },
    { id: 3, name: "<PERSON><PERSON>" },
    { id: 4, name: "<PERSON><PERSON>" },
    { id: 5, name: "<PERSON>&M" },
    { id: 6, name: "LC Waikiki" },
    { id: 7, name: "<PERSON><PERSON>" },
  ]
};

const brandSlice = createSlice({
  name: 'brands',
  initialState,
  reducers: {
    // Tüm markaları set etme (örneğin başlangıç parametresi için)
    setSelectedBrands(state, action) {
      state.selectedBrands = action.payload;
    },
    // <PERSON><PERSON>li bir markayı ekle veya çıkar
    toggleBrandSelection(state, action) {
      const brand = action.payload;
      const existingIndex = state.selectedBrands.findIndex(b => b.id === brand.id);
      if (existingIndex >= 0) {
        // <PERSON><PERSON>er marka seçili ise çıkar
        state.selectedBrands.splice(existingIndex, 1);
      } else {
        // Değilse ekle
        state.selectedBrands.push(brand);
      }
    },
    clearSelectedBrands(state) {
      state.selectedBrands = [];
    }
    
  },
});

export const { setSelectedBrands, toggleBrandSelection,clearSelectedBrands } = brandSlice.actions;
export default brandSlice.reducer;
