
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import SvgPost from "../../assets/postProfile";
import SvgPostActive from "../../assets/postprofileActive";
import SvgCollections from "../../assets/collection";
import SvgCollectionActive from "../../assets/collectionActive";
import SvgFav from "../../assets/favProfile";
import SvgFavActive from "../../assets/favActive"
import ProfilePost from "../Profile/ProfilePost";
import Collections from "../Home/Collections";
import { useRoute } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { fetchFeedPosts, fetchUserPosts } from "../../redux/actions/postActions";
import { useEffect, useState, useRef } from "react";
import LikedPost from "../Profile/LikedPost";
import { clearUserPosts } from "../../redux/slices/postSlice";
import store from "../../redux/store";


const SearchProfileDetail = ({ user }) => {
  const [activeTab, setActiveTab] = useState("post");
  const dispatch = useDispatch()
  const route = useRoute();

  const userInfo = useSelector((state) => state.user);
  // Get the current authenticated user from Redux store
  const currentUser = useSelector(state => state.auth.user);

  // Get the profile user ID from props if available, otherwise fall back to logged-in user ID
  const profileUserId = user?.id || user?._id;
  const loggedInUserId = currentUser?.id || currentUser?._id;
  // Use the profile user ID for fetching posts
  const userId = profileUserId || loggedInUserId;

  // Check if this is the current user's own profile
  const isSelfProfile = loggedInUserId === profileUserId;

  const userPosts = useSelector((state) => state.posts.userPosts || []);
  const loading = useSelector((state) => state.posts.loading);
  const error = useSelector((state) => state.posts.error);

  console.log("SearchProfileDetail - Using userId:", userId);
  console.log("SearchProfileDetail - Profile user:", user?.username);
  console.log("SearchProfileDetail - Logged in user:", userInfo?.userInfo?.username);
  console.log("SearchProfileDetail - Is self profile:", isSelfProfile);
  console.log("SearchProfileDetail - Posts:", userPosts?.length || 0);

  // Track if this is the first render
  const isFirstRender = useRef(true);
  const prevUserId = useRef(null);

  // Clear posts immediately when component mounts or userId changes
  useEffect(() => {
    // Always clear posts when userId changes to prevent showing previous profile's posts
    if (userId !== prevUserId.current) {
      console.log("SearchProfileDetail - Clearing posts due to userId change");
      dispatch(clearUserPosts());
      prevUserId.current = userId;
    }
  }, [userId, dispatch]);

  // Fetch posts when activeTab is "post" and userId is available
  useEffect(() => {
    if (activeTab === "post" && userId) {
      console.log("SearchProfileDetail - Fetching posts for userId:", userId);

      // Only fetch posts if this isn't the first render (posts were already cleared)
      if (!isFirstRender.current) {
        dispatch(fetchUserPosts(userId));
      } else {
        // On first render, clear and then fetch
        dispatch(clearUserPosts());
        dispatch(fetchUserPosts(userId));
        isFirstRender.current = false;
      }
    }
  }, [activeTab, dispatch, userId]);


  const renderContent = () => {
    // If trying to access collections or favorites on another user's profile,
    // force it back to posts tab
    if (!isSelfProfile && (activeTab === "collections" || activeTab === "fav")) {
      // Reset to posts tab if trying to access restricted tabs
      setActiveTab("post");
      return <ProfilePost posts={userPosts} loading={loading} userId={userId} />;
    }

    switch (activeTab) {
      case "post":
        // Pass loading state explicitly to ensure loader is shown immediately
        return <ProfilePost posts={userPosts} loading={loading} userId={userId} />;
      case "collections":
        // Only show collections if it's the user's own profile
        return isSelfProfile ? <Collections /> : null;
      case "fav":
        // Only show liked posts if it's the user's own profile
        return isSelfProfile ? <LikedPost userInfo={userInfo}/> : null;
      default:
        return null;
    }
  };

  return (
    <View>
      <View style={styles.tabContainer}>
        <TouchableOpacity onPress={() => setActiveTab("post")}>
          {activeTab === "post" ? <SvgPostActive /> : <SvgPost />}
        </TouchableOpacity>

        {isSelfProfile && (
          <>
            <TouchableOpacity onPress={() => setActiveTab("collections")}>
              {activeTab === "collections" ? <SvgCollectionActive /> : <SvgCollections />}
            </TouchableOpacity>

            <TouchableOpacity onPress={() => setActiveTab("fav")}>
              {activeTab === "fav" ? <SvgFavActive /> : <SvgFav />}
            </TouchableOpacity>
          </>
        )}
      </View>

      <View style={styles.contentContainer}>{renderContent()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: "row",
    marginHorizontal:20,
    marginTop:35,
    gap:61
  },
  contentContainer: {
    marginHorizontal:20,
    marginTop:15
  },
});

export default SearchProfileDetail;
// const userId = useSelector((state) => state.user.userInfo?.id);
//   const { userPosts, loading, error } = useSelector((state) => state.posts);
// console.log("userpost", userPosts)
//   useEffect(() => {
//     if (userId) {
//       dispatch(fetchFeedPosts(userId));
//     }
//   }, [userId]);