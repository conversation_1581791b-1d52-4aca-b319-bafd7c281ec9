import { StyleSheet, Text, View, TouchableOpacity } from 'react-native'
import SvgHidden from "../../assets/hidden"

const SearchProfileHidden = ({ handleFollowToggle, user }) => {
  const onFollowPress = () => {
    console.log('SearchProfileHidden - Follow button pressed for user:', user?.username || 'Unknown user');

    // Call the parent component's handleFollowToggle function immediately
    // No need for setTimeout as we've improved the state management
    handleFollowToggle();

    // Use requestAnimationFrame for any UI updates that need to happen after the state change
    requestAnimationFrame(() => {
      console.log('SearchProfileHidden - Forcing UI update after follow button press');
    });
  };

  return (
    <View style={styles.container}>
      <SvgHidden style={styles.icon}/>
      <View style={styles.textContainer}>
        <Text style={styles.textHeader}>Bu hesap gizli</Text>
        <Text style={styles.text}>Fotoğraf ve videoları görebilmek için bu hesabı takip et</Text>
        <TouchableOpacity
          style={styles.followButton}
          onPress={onFollowPress}
          activeOpacity={0.7} // Add active opacity for better touch feedback
        >
          <Text style={styles.followText}>Takip Et</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default SearchProfileHidden

const styles = StyleSheet.create({
    container:{
        marginHorizontal:20,
        backgroundColor:"#F0F0F0",
        flexDirection:"row",
        height:120,
        borderRadius:10,
        alignItems:"center",
        gap:9,
        marginTop:50
    },
    icon:{
        marginLeft:10
    },
    textContainer:{
        gap:8
    },
    textHeader:{
        fontSize:14,
        fontWeight: "600",
        color:"#000000"
    },
    text:{
        fontSize:14,
        color:"#9D9C9C",
        width:228
    },
    followButton: {
        backgroundColor: "#000000",
        borderRadius: 5,
        paddingVertical: 8,
        paddingHorizontal: 20,
        alignSelf: "flex-start",
        marginTop: 5
    },
    followText: {
        color: "#FFFFFF",
        fontSize: 14,
        fontWeight: "500"
    }
})