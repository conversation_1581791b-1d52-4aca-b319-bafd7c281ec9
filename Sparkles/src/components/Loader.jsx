import React, { useEffect, useRef, useMemo } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';

// Memoized Loader component to prevent unnecessary re-renders
const Loader = React.memo(({ isVisible = true, speed = 'normal' }) => {
  const rotation = useRef(new Animated.Value(0)).current;
  const animationRef = useRef(null);
  const isFirstRender = useRef(true);

  // Calculate duration based on speed - memoized to avoid recalculation
  const duration = useMemo(() => {
    switch (speed) {
      case 'slow': return 2000;
      case 'fast': return 800;
      default: return 1200; // normal speed
    }
  }, [speed]);

  // Start or restart animation
  const startAnimation = () => {
    // Only reset the animation value on first render
    // This prevents the flickering when the component re-renders
    if (isFirstRender.current) {
      rotation.setValue(0);
      isFirstRender.current = false;
    }

    // Stop any existing animation before starting a new one
    if (animationRef.current) {
      animationRef.current.stop();
    }

    // Create and store the animation reference
    animationRef.current = Animated.loop(
      Animated.timing(rotation, {
        toValue: 1,
        duration,
        easing: Easing.linear, // Use linear easing for smoother rotation
        useNativeDriver: true,
        isInteraction: false, // Mark as not an interaction to improve performance
      })
    );

    // Start the animation
    animationRef.current.start();
  };

  // Handle visibility changes
  useEffect(() => {
    if (isVisible) {
      startAnimation();
    } else if (animationRef.current) {
      // Stop animation when not visible
      animationRef.current.stop();
    }

    // Cleanup on unmount
    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
      }
    };
  }, [isVisible, duration]);

  // Memoize the rotation interpolation to avoid recalculation
  const rotate = useMemo(() => {
    return rotation.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });
  }, [rotation]);

  if (!isVisible) return null;

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.spinner,
          { transform: [{ rotate }] },
        ]}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  spinner: {
    borderWidth: 4,
    borderColor: '#B2B2B2',
    borderTopColor: '#000',
    borderRadius: 25,
    width: 32,
    height: 32,
  },
});

export default Loader;