import { useState } from "react";
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Image,
} from "react-native";
// Temporarily comment out FastImage
// import FastImage from "react-native-fast-image";
import SvgPinkPlus from "../../assets/pinkplus"
import { launchImageLibrary } from "react-native-image-picker";
import { useDispatch } from "react-redux";
import { setProfilePicture } from "../../redux/slices/authSlice";

const ProfileImagePicker = () => {
  const [imageUri, setImageUri] = useState(null);
   // Android için galeri izni isteme
  // const requestGalleryPermission = async () => {
  //   if (Platform.OS === "android") {
  //     const granted = await PermissionsAndroid.request(
  //       PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
  //     );
  //     return granted === PermissionsAndroid.RESULTS.GRANTED;
  //   }
  //   return true;
  // };

  // Galeriden fotoğraf seçme işlemi
  // const selectImage = async () => {
  //   // const hasPermission = await requestGalleryPermission();
  //   // if (!hasPermission) {
  //   //   alert("Galeriyi açmak için izin vermelisin!");
  //   //   return;
  //   // }

  //   launchImageLibrary({ mediaType: "photo" }, (response) => {
  //     if (response.didCancel) return;
  //     if (response.errorCode) {
  //       alert("Fotoğraf seçme hatası: " + response.errorMessage);
  //       return;
  //     }
  //     if (response.assets && response.assets.length > 0) {
  //       setImageUri(response.assets[0].uri);
  //     }
  //   });
  // };

  const dispatch = useDispatch();
// const handleProfilePicture = (image) => {
//   dispatch(setProfilePicture(image)); // Redux state'e kaydet
// };




  const selectImage = async () => {
    launchImageLibrary({
      mediaType: "photo",
      quality: 0.8,
      maxWidth: 800,
      maxHeight: 800,
      includeBase64: false
    }, (response) => {
      if (response.didCancel) {
        console.log("Kullanıcı resim seçimini iptal etti");
        return;
      }

      if (response.errorCode) {
        console.error("Fotoğraf seçme hatası:", response.errorMessage);
        alert("Fotoğraf seçme hatası: " + response.errorMessage);
        return;
      }

      if (response.assets && response.assets.length > 0) {
        const selectedImage = response.assets[0];
        console.log("Seçilen resim detayları:", {
          uri: selectedImage.uri,
          type: selectedImage.type,
          fileName: selectedImage.fileName,
          fileSize: selectedImage.fileSize
        });

        // Yerel state'e URI'yı kaydet (görüntüleme için)
        setImageUri(selectedImage.uri);

        // Tüm resim nesnesini Redux'a kaydet (uri, type, name, vb.)
        dispatch(setProfilePicture(selectedImage));
      }
    });
  };


  return (
    <View style={styles.container}>
    <View style={styles.imageWrapper}>
      {/* Eğer imageUri varsa, onu göster; yoksa varsayılan bir görsel göster */}
      <Image
        source={imageUri
          ? { uri: imageUri }
          : require('../../assets/profilePhoto.png')
        }
        style={styles.profileImage}
        resizeMode="cover"
      />
    </View>
    <TouchableOpacity style={styles.plusButton} onPress={selectImage}>
      <SvgPinkPlus />  {/* SVG + Butonu */}
    </TouchableOpacity>
  </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  imageWrapper: {
    position: "relative",
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: "hidden",
  },
  profileImage: {
    width: "100%",
    height: "100%",
    borderRadius: 50,
  },
  defaultImage: {
    width: "100%",
    height: "100%",
    backgroundColor: "#ddd",
    borderRadius: 50,
  },
  plusButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default ProfileImagePicker;
