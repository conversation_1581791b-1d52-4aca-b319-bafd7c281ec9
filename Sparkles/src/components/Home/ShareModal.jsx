import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  TextInput,
  Image,
  Keyboard,
  Easing,
  Animated,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Alert
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import SvgLess from '../../assets/less';
import SvgCopyLink from '../../assets/copyLink';
import SvgWhatsapp from '../../assets/whatsapp';
import SvgInstagram from '../../assets/instagram';
import SvgFacebook from '../../assets/facebook';
import SvgSnapchat from '../../assets/snapchat';
import SvgX from '../../assets/x';
import SvgTelegram from '../../assets/telegram';
import SvgShareLink from '../../assets/shareLink';
import SvgClear from "../../assets/clear"
import {useEffect, useRef, useState} from 'react';
import SvgSearchPeople from '../../assets/searchpeople';
import SvgPeople from '../../assets/people';
import {height, width} from '../../utils/helpers';
import { useDispatch, useSelector } from 'react-redux';
import { fetchFollowing } from '../../redux/slices/followSlice';
import { useNavigation } from '@react-navigation/native';
import { startNewConversation, startNewGroupConversation } from '../../redux/actions/conversationsActions';
import { SCREENS } from '../../utils/router';


const ShareModal = ({modalVisible, setModalVisible, selectedUsers, setSelectedUsers}) => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [copied, setCopied] = useState(false);
  const [searchText, setSearchText] = useState('');
  const hasSelectedUsers = selectedUsers.length >= 1;
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [startingConversation, setStartingConversation] = useState(false);
  const [startingAction, setStartingAction] = useState(''); // To track which action is being performed

  const currentUser = useSelector(state => state.auth.user);
  const currentUserId = currentUser?._id || currentUser?.id;

  const { following, loading } = useSelector(state => state.follow);

  const animatedHeight = useRef(new Animated.Value(height * 0.85)).current; // Başlangıç yüksekliği

  useEffect(() => {
    if (modalVisible && currentUserId) {
      setIsLoading(true);

      dispatch({ type: 'following/reset' });

      dispatch(fetchFollowing(currentUserId))
        .then(() => {
          setIsLoading(false);
        })
        .catch(error => {
          console.error("Error fetching following:", error);
          setIsLoading(false);
        });
    }
  }, [dispatch, currentUserId, modalVisible]);

  useEffect(() => {
    if (following && following.length > 0) {
      setFilteredUsers(following);
    }
  }, [following]);

  useEffect(() => {
    const keyboardShowListener = Keyboard.addListener("keyboardDidShow", (event) => {
      const Modalheight = event.endCoordinates.height;

      Animated.timing(animatedHeight, {
        toValue: height * 0.85 - Modalheight,
        duration: 100,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    const keyboardHideListener = Keyboard.addListener("keyboardDidHide", () => {
      Animated.timing(animatedHeight, {
        toValue: height * 0.85,
        duration: 50,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    return () => {
      keyboardShowListener.remove();
      keyboardHideListener.remove();
    };
  }, []);

  useEffect(() => {
    if (!following || following.length === 0) return;

    if (searchText.trim() === '') {
      setFilteredUsers(following);
    } else {
      const filtered = following.filter(user =>
        user.username?.toLowerCase().includes(searchText.toLowerCase()),
      );
      setFilteredUsers(filtered);
    }
  }, [searchText, following]);

    const toggleUserSelection = userId => {
      setSelectedUsers(prevSelected => {
        const newSet = new Set(prevSelected);
        newSet.has(userId) ? newSet.delete(userId) : newSet.add(userId);
        return Array.from(newSet);
      });
    };

  const findSelectedUserDetails = (userId) => {
    return filteredUsers.find(user => (user._id || user.id) === userId);
  };

  const handleStartConversation = async () => {
    if (selectedUsers.length === 0) {
      Alert.alert('Uyarı', 'Lütfen en az bir kişi seçin.');
      return;
    }

    setStartingConversation(true);
    setStartingAction('group');

    try {
      if (selectedUsers.length === 1) {
        const recipientId = selectedUsers[0];
        const selectedUser = findSelectedUserDetails(recipientId);

        if (!selectedUser) {
          throw new Error('Seçilen kullanıcı bilgileri bulunamadı.');
        }

        const result = await dispatch(startNewConversation({ recipientId })).unwrap();

        setSelectedUsers([]);

        setModalVisible(false);

        navigation.navigate(SCREENS.CHATSCREEN, {
          messageId: result.data.conversation._id,
          conversationId: result.data.conversation._id,
          isApiConversation: true,
          recipientId: recipientId,
          userInfo: {
            userId: recipientId,
            userImage: { uri: selectedUser.profilePicture || null },
            name: selectedUser.fullName || selectedUser.username,
            username: selectedUser.username,
            followers: selectedUser.followerCount || 0,
            post: selectedUser.postCount || 0,
          }
        });
      } else {
        // Start a group chat with all selected users
        const participantIds = [...selectedUsers];

        const selectedUserDetails = participantIds.map(userId => findSelectedUserDetails(userId));

        const groupName = selectedUserDetails
          .filter(user => user)
          .map(user => user.username)
          .slice(0, 3)
          .join(', ');

        const result = await dispatch(
          startNewGroupConversation({
            name: groupName,
            participants: participantIds
          })
        ).unwrap();

        setSelectedUsers([]);

        setModalVisible(false);

        const populatedParticipants = result.data.group.participants || [];

        navigation.navigate(SCREENS.CHATSCREEN, {
          messageId: result.data.group._id,
          conversationId: result.data.group._id,
          isApiConversation: true,
          isGroupChat: true,
          groupInfo: {
            _id: result.data.group._id,
            name: groupName,
            participants: populatedParticipants.length > 0 ? populatedParticipants : participantIds
          }
        });
      }
    } catch (error) {
      console.error('Sohbet başlatma hatası:', error);
      Alert.alert(
        'Hata',
        error.message || 'Sohbet başlatılırken bir hata oluştu. Lütfen tekrar deneyin.'
      );
    } finally {
      setStartingConversation(false);
      setStartingAction('');
    }
  };

  // Handle sending messages separately to each selected user
  const handleSendSeparately = async () => {
    if (selectedUsers.length === 0) {
      Alert.alert('Uyarı', 'Lütfen en az bir kişi seçin.');
      return;
    }

    setStartingConversation(true);
    setStartingAction('separate');

    try {
      const conversations = [];

      // Create individual conversations with each selected user
      for (const recipientId of selectedUsers) {
        const selectedUser = findSelectedUserDetails(recipientId);

        if (!selectedUser) {
          console.error('Kullanıcı bilgileri bulunamadı:', recipientId);
          continue;
        }

        const result = await dispatch(startNewConversation({ recipientId })).unwrap();

        conversations.push({
          messageId: result.data.conversation._id,
          conversationId: result.data.conversation._id,
          isApiConversation: true,
          recipientId: recipientId,
          userInfo: {
            userId: recipientId,
            userImage: { uri: selectedUser.profilePicture || null },
            name: selectedUser.fullName || selectedUser.username,
            username: selectedUser.username,
            followers: selectedUser.followerCount || 0,
            post: selectedUser.postCount || 0,
          }
        });
      }

      setSelectedUsers([]);
      setModalVisible(false);

      // Navigate to the last created conversation if any were created
      if (conversations.length > 0) {
        navigation.navigate(SCREENS.CHATSCREEN, conversations[conversations.length - 1]);
      }

    } catch (error) {
      console.error('Ayrı sohbetler başlatma hatası:', error);
      Alert.alert(
        'Hata',
        error.message || 'Sohbetler başlatılırken bir hata oluştu. Lütfen tekrar deneyin.'
      );
    } finally {
      setStartingConversation(false);
      setStartingAction('');
    }
  };

  const handleCopyLink = () => {
    Clipboard.setString('https://example.com');
    setCopied(true);
    setTimeout(() => setCopied(false), 3000);
    // Note: This function doesn't close the modal, so no need to handle selectedUsers preservation here
  };

      const handlePlatformShare = (platform) => {
        const postUrl = `https://example.com/post/share`;

        const shareLinks = {
          whatsapp: `https://wa.me/?text=${encodeURIComponent(postUrl)}`,
          instagram: `https://www.instagram.com/?url=${encodeURIComponent(postUrl)}`,
          facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`,
          snapchat: `https://www.snapchat.com/share?url=${encodeURIComponent(postUrl)}`,
          x: `https://twitter.com/intent/tweet?url=${encodeURIComponent(postUrl)}`,
          telegram: `https://t.me/share/url?url=${encodeURIComponent(postUrl)}`,
          share: postUrl, // Genel paylaşım
        };

        if (shareLinks[platform]) {
          console.log(`Sharing to ${platform}: ${shareLinks[platform]}`);
        }

        // Close the modal and clear selected users
        setSelectedUsers([]);
        setModalVisible(false);
      };
  const shareOptions = [
    {
      id: 'copy',
      label: (
        <Text style={[styles.shareText, {textAlign: 'center', width: '100%'}]}>
          {copied ? 'Kopyalandı' : 'Bağlantıyı\nkopyala'}
        </Text>
      ),
      icon: () => <SvgCopyLink />,
      onPress: handleCopyLink,
    },
    { id: 'whatsapp', label: 'WhatsApp', icon: () => <SvgWhatsapp />, onPress: () => handlePlatformShare('whatsapp') },
    { id: 'instagram', label: 'Instagram', icon: () => <SvgInstagram />, onPress: () => handlePlatformShare('instagram') },
    { id: 'facebook', label: 'Facebook', icon: () => <SvgFacebook />, onPress: () => handlePlatformShare('facebook') },
    { id: 'snapchat', label: 'Snapchat', icon: () => <SvgSnapchat />, onPress: () => handlePlatformShare('snapchat') },
    { id: 'x', label: 'X', icon: () => <SvgX />, onPress: () => handlePlatformShare('x') },
    { id: 'telegram', label: 'Telegram', icon: () => <SvgTelegram />, onPress: () => handlePlatformShare('telegram') },
    { id: 'share', label: 'Paylaş', icon: () => <SvgShareLink />, onPress: () => handlePlatformShare('share') },
  ];


  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (modalVisible) {

      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 1000,
        useNativeDriver: true,
      }).start();
    }
  }, [modalVisible]);
  const [slideAnim] = useState(new Animated.Value(500));

  useEffect(() => {
    if (modalVisible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } else {
      // Log when modal is closed to help with debugging
      console.log('ShareModal closed, selected users cleared:', selectedUsers);

      Animated.timing(slideAnim, {
        toValue: 500,
        duration: 800,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    }
  }, [modalVisible, selectedUsers]);

  return (
   <Modal
     animationType="none"
     transparent={true}
     visible={modalVisible}
     onRequestClose={() => {
       // Close the modal when back button is pressed (Android) and clear selected users
       setSelectedUsers([]);
       setModalVisible(false);
     }}>

     <KeyboardAvoidingView
       behavior={Platform.OS === "ios" ? "padding" : "height"}
       style={{ flex: 1 }}>

       <TouchableWithoutFeedback onPress={() => {
         Keyboard.dismiss();
         // Close the modal when clicking on the overlay and clear selected users
         setSelectedUsers([]);
         setModalVisible(false);
       }}>
         <View style={styles.overlay}>
           {/* Overlay touchable area - closes modal when tapping outside */}
           <TouchableWithoutFeedback onPress={(e) => {
             // Prevent the click from bubbling up to the overlay
             e.stopPropagation();
           }}>
             <Animated.View style={[styles.modalContainer, { height: animatedHeight }]}>
             {/* <View style={styles.overlay}> */}
               {/* <View style={styles.modalContainer}> */}
              <View style={styles.scontainer}>
                <View
                  style={[
                    styles.searchContainer,
                    hasSelectedUsers && { width: "75%" },
                  ]}
                >
                  <SvgSearchPeople style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Kişi ara"
                    placeholderTextColor="#BBBBBB"
                    onChangeText={setSearchText}
                    value={searchText}
                    selectionColor="#D134AA"
                  />
                  {searchText.length > 0 && (
                    <TouchableOpacity
                      onPress={() => setSearchText('')}
                      style={styles.clearIcon}
                    >
                      <SvgClear />
                    </TouchableOpacity>
                  )}
                </View>

                {hasSelectedUsers && (
                  <TouchableOpacity
                    style={styles.selectedCountContainer}
                    onPress={() => setSelectedUsers([])}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.selectedCountText}>{selectedUsers.length}</Text>
                    <SvgPeople />
                  </TouchableOpacity>
                )}

                {!hasSelectedUsers && (
                  <TouchableOpacity
                    onPress={() => {
                      // Close the modal and clear selected users
                      setSelectedUsers([]);
                      setModalVisible(false);
                      setTimeout(() => {
                        Keyboard.dismiss();
                      }, 100);
                    }}
                    activeOpacity={0.7}
                    style={styles.closeButton}
                  >
                    <SvgLess style={styles.closeBtn} />
                  </TouchableOpacity>
                )}
              </View>

              {/* Kullanıcı Listesi */}
              {isLoading || loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#D134AA" />
                  <Text style={styles.loadingText}>Kişiler yükleniyor...</Text>
                </View>
              ) : filteredUsers.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>Kullanıcı bulunamadı</Text>
                </View>
              ) : (
                <FlatList
                  data={filteredUsers}
                  keyExtractor={(item) => (item._id || item.id).toString()}
                  numColumns={3}
                  showsVerticalScrollIndicator={false}
                  renderItem={({ item }) => {
                    const userId = item._id || item.id;
                    const isSelected = selectedUsers.includes(userId);
                    return (
                      <TouchableOpacity
                        style={styles.userItem}
                        onPress={() => toggleUserSelection(userId)}
                        activeOpacity={0.7}
                      >
                        <View style={styles.userImageContainer}>
                          <View style={styles.userImageWrapper}>
                            <Image
                              source={
                                item.profilePicture
                                  ? { uri: item.profilePicture }
                                  : require('../../assets/profilePhoto.png')
                              }
                              style={styles.userImage}
                            />
                          </View>
                          <View style={[
                            styles.borderOverlay,
                            isSelected && styles.selectedBorderOverlay
                          ]} />
                          {isSelected && <View style={styles.shadowOverlay} />}
                        </View>
                        <Text style={styles.username}>{item.username}</Text>
                      </TouchableOpacity>
                    );
                  }}
                />
              )}

              {hasSelectedUsers ? (
                selectedUsers.length > 1 ? (
                  // Show two buttons for multiple selected users, one under the other
                  <View style={styles.buttonContainer}>
                    <TouchableOpacity
                      style={[
                        styles.multiButton,
                        startingConversation && styles.disabledButton
                      ]}
                      activeOpacity={0.7}
                      onPress={handleStartConversation}
                      disabled={startingConversation}
                    >
                      {startingConversation && startingAction === 'group' ? (
                        <ActivityIndicator size="small" color="#FFFFFF" />
                      ) : (
                        <Text style={styles.buttonText}>Grup Sohbetini Başlat</Text>
                      )}
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.multiButton,
                        startingConversation && styles.disabledButton
                      ]}
                      activeOpacity={0.7}
                      onPress={handleSendSeparately}
                      disabled={startingConversation}
                    >
                      {startingConversation && startingAction === 'separate' ? (
                        <ActivityIndicator size="small" color="#FFFFFF" />
                      ) : (
                        <Text style={styles.buttonText}>Ayrı Ayrı Gönder</Text>
                      )}
                    </TouchableOpacity>
                  </View>
                ) : (
                  // Single button for one selected user
                  <TouchableOpacity
                    style={[
                      styles.button,
                      startingConversation && styles.disabledButton
                    ]}
                    activeOpacity={0.7}
                    onPress={handleStartConversation}
                    disabled={startingConversation}
                  >
                    {startingConversation && startingAction === 'group' ? (
                      <ActivityIndicator size="small" color="#FFFFFF" />
                    ) : (
                      <Text style={styles.buttonText}>Sohbeti Başlat</Text>
                    )}
                  </TouchableOpacity>
                )
              ) : (
                <FlatList
                  data={shareOptions}
                  horizontal
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.shareButton}
                      onPress={item.onPress}
                      activeOpacity={0.7}
                    >
                      {item.icon()}
                      <Text style={styles.shareText}>{item.label}</Text>
                    </TouchableOpacity>
                  )}
                  contentContainerStyle={styles.shareScroll}
                  showsHorizontalScrollIndicator={false}
                />
              )}
            {/* </View> */}
          {/* </View> */}


            </Animated.View>
           </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </Modal>
  );

};

export default ShareModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 40,
    maxHeight: height * 0.55,
  },
  closeButton: {
    right: 16,
    left:16
  },
  closeBtn: {
    width: 24,
    height: 24,
  },
  shareButton: {
    alignItems: 'center',
    height: 160
  },
  shareText: {
    marginTop: 5,
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 10,
    textAlign: 'center',
  },
  shareScroll: {
    gap: 17,
    marginVertical: 10,
  },
  buttonContainer: {
    flexDirection: 'column',
    width: width * 0.90,
    marginTop: 10,
  },
  button: {
    backgroundColor: '#000',
    padding: 15,
    borderRadius: 33,
    alignItems: 'center',
    marginTop: 10,
    width: width * 0.90,
    height: 47
  },
  multiButton: {
    backgroundColor: '#000',
    padding: 15,
    borderRadius: 33,
    alignItems: 'center',
    width: width * 0.90, // Full width like the single button
    height: 47,
    marginBottom: 10 // Add margin between buttons
  },
  disabledButton: {
    backgroundColor: '#999',
    opacity: 0.7
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    alignItems: "center",
    justifyContent: "center"
  },
  scontainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: width * 0.9,
    height: 38,
    flex:1,
    paddingVertical:20,
    marginBottom:20

  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#000',
    paddingHorizontal: 10,
    height: 38,
    width: '85%',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: '#000',
    fontWeight: '500',
    textAlignVertical: 'center',
  },
  clearIcon: {
    marginLeft: 10,
    padding: 5,
  },
  selectedCountContainer: {
    backgroundColor: "#000",
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 10,
    paddingVertical: 5,
    height: 38,
    marginLeft: 10,
  },
  selectedCountText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 14,
    marginRight: 5,
  },
  userItem: {
    alignItems: 'center',
    justifyContent: "center",
    marginBottom: 15,
    width: width * 0.30,
  },
  userImageContainer: {
    width: 84,
    height: 84,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  userImageWrapper: {
    width: 78,
    height: 78,
    borderRadius: 39,
    overflow: 'hidden',
  },
  userImage: {
    width: '100%',
    height: '100%',
  },
  borderOverlay: {
    position: 'absolute',
    width: 84,
    height: 84,
    borderRadius: 42,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  selectedBorderOverlay: {
    borderColor: '#D134AA',
  },
  shadowOverlay: {
    position: 'absolute',
    width: 84,
    height: 84,
    borderRadius: 42,
    backgroundColor: '#D134AA33',
    shadowColor: '#D134AA',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 8,
  },
  username: {
    marginTop: 5,
    fontSize: 12,
    color: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    height: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    height: 200,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },

});
