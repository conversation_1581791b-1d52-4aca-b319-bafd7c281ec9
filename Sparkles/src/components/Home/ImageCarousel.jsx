import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import React, {useState, useCallback} from 'react';
import {ScrollView} from 'react-native';
import FastImage from 'react-native-fast-image';
import SvgMore from '../../assets/more';
import {width} from '../../utils/helpers';
import ShareBottomSheet from './ShareBottomSheet';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

const ImageCarousel = React.memo(({item, onPress}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  // Get posts from Redux store
  const feedPosts = useSelector(state => state.posts.feedPosts);
  const postsStatus = useSelector(state => state.posts.status);
  const postsError = useSelector(state => state.posts.error);
  // Get the current logged-in user from Redux store
  const currentUser = useSelector(state => state.auth.user);

  //  useEffect(() => {
  //     dispatch(fetchFeedPosts());
  //     console.log("imagecarousel feedpost", feedPosts)
  //   }, [dispatch]);

  // Orijinal veriye erişim (birden çok resim için)
  const postData = item.originalData || item;

  // Eğer çoklu resim yoksa tek resmi diziye çevir
  const images = Array.isArray(postData.images)
    ? postData.images
    : [postData.image || postData.uri];




  return (
    <View style={styles.container}>
    {/* Üst bilgi çubuğu - Tasarımınız aynı kaldı */}
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.userInfo}
        activeOpacity={0.7}
        onPress={() => {
          // Get the post user ID
          let postUserId;
          let postUser;

          if (item.originalData && item.originalData.user && item.originalData.user._id) {
            postUserId = item.originalData.user._id;
            postUser = item.originalData.user;
          } else if (item.user && item.user._id) {
            postUserId = item.user._id;
            postUser = item.user;
          }

          // Get the current user ID
          const currentUserId = currentUser?.id || currentUser?._id;

          // Convert IDs to strings for reliable comparison (handles ObjectId vs string)
          const postUserIdStr = postUserId?.toString();
          const currentUserIdStr = currentUserId?.toString();

          // Compare IDs and navigate accordingly
          console.log("Profile navigation - Post user ID:", postUserIdStr);
          console.log("Profile navigation - Current user ID:", currentUserIdStr);
          console.log("Profile navigation - Is same user:", currentUserIdStr === postUserIdStr);

          if (currentUserIdStr && postUserIdStr && (currentUserIdStr === postUserIdStr)) {
            // If it's the logged-in user, navigate to Profile screen
            console.log("Navigating to own Profile screen");
            navigation.navigate('Profile');
          } else {
            // If it's another user, navigate to SearchProfile screen
            console.log("Navigating to SearchProfile screen for user:", postUser?.username);
            navigation.navigate('SearchProfile', { user: postUser });
          }
        }}
      >
        <FastImage
          source={item.profilePicture
            ? {
                uri: item.profilePicture,
                priority: FastImage.priority.high,
                cache: FastImage.cacheControl.web
              }
            : require('../../assets/profilePhoto.png')
          }
          style={styles.userImage}
          resizeMode={FastImage.resizeMode.cover}
        />
        <Text style={{fontSize: 12}}>{item.username}</Text>
      </TouchableOpacity>
      <View>
        <TouchableOpacity
          onPress={() => setModalVisible(true)}
          activeOpacity={0.7}>
          <SvgMore />
        </TouchableOpacity>
        <ShareBottomSheet
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
        />
      </View>
    </View>

    {/* Resim carousel - Veri yapınıza uyarlanmış hali */}
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      pagingEnabled={true}
      style={{marginBottom: 5, width: item.dimensions.width}}>
      {images.map((img, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => onPress(postData, index)}
          activeOpacity={1}>
          <FastImage
            source={typeof img === 'string'
              ? {
                  uri: img,
                  priority: FastImage.priority.normal,
                  cache: FastImage.cacheControl.immutable
                }
              : img
            }
            style={{
              width: item.dimensions.width,
              height: item.dimensions.height,
              borderRadius: 10,
            }}
            resizeMode={FastImage.resizeMode.cover}
          />
        </TouchableOpacity>
      ))}
    </ScrollView>
  </View>
  );
});

export default ImageCarousel;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: Math.floor((width - 30) / 2),
    marginBottom: 8,
    marginTop: 15,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 7,
  },
  userImage: {
    width: 24,
    height: 24,
    borderRadius:12
  },
});
