import React, {useEffect, useRef, useState, useMemo, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  Image,
  Modal,
  StyleSheet,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Dimensions,
  Animated,
  Keyboard,
  Easing,
  ScrollView,
} from 'react-native';
import {
  commentsData,
  dismissKeyboard,
  height,
  width,
} from '../../utils/helpers';
import SvgLess from '../../assets/less';
import SvgLike from '../../assets/like-s';
import SvgLikeFill from '../../assets/likeFill-s';
import SvgSendBtn from '../../assets/sendBtn';
import { useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { addComment, addReply, deleteComment, fetchComments, toggleCommentLike } from '../../redux/actions/commentActions';
import { addReplyToComment } from '../../redux/slices/commentSlice';
import SvgTrash from '../../assets/trash';
import { Swipeable } from 'react-native-gesture-handler';
import RNModal from 'react-native-modal';


const CommentModal = ({commentModal, setCommentModal, onSubmitComment, postId, onCommentCountChange}) => {
  const route = useRoute()
  // const { postId } = route.params;
  const userInfo = useSelector((state) => state.user);
  const currentUserId = userInfo?.userInfo?._id || userInfo?.userInfo?.id;
  const defaultAvatar = 'https://via.placeholder.com/40'; // Default avatar if user has no profile picture
  // const [comments, setComments] = useState(commentsData);
  // const [newComment, setNewComment] = useState('');
  // const [expandedComments, setExpandedComments] = useState({});
  // const [replyingTo, setReplyingTo] = useState(null);
  const emojis = ['👏', '🙌', '😎', '😍', '❤️‍🔥', '🫵'];
  const [commentText, setCommentText] = useState('');
  // const [keyboardHeight, setKeyboardHeight] = useState(new Animated.Value(0));
console.log("YORUM POSTID", postId)
console.log("YORUM USERINFO", userInfo.userInfo.profilePicture)
  const addEmojiToInput = emoji => {
    setNewComment(prev => prev + emoji);
    // Optional: Focus the input after adding an emoji
    // if (textInputRef.current) {
    //   textInputRef.current.focus();
    // }
  };
  // const toggleLike = (commentId, replyId = null) => {
  //   setComments(prevComments =>
  //     prevComments.map(comment => {
  //       if (comment.id === commentId) {
  //         if (replyId) {
  //           // Eğer alt yorumsa, replies içindeki ilgili reply'yi bul
  //           return {
  //             ...comment,
  //             replies: comment.replies.map(reply => {
  //               if (reply.id === replyId) {
  //                 return {
  //                   ...reply,
  //                   liked: !reply.liked, // Alt yorumun beğeni durumunu değiştir
  //                   likes: reply.liked ? reply.likes - 1 : reply.likes + 1, // Beğeni sayısını güncelle
  //                 };
  //               }
  //               return reply;
  //             }),
  //           };
  //         } else {
  //           // Ana yorumun beğeni durumunu değiştir
  //           return {
  //             ...comment,
  //             liked: !comment.liked,
  //             likes: comment.liked ? comment.likes - 1 : comment.likes + 1,
  //           };
  //         }
  //       }
  //       return comment;
  //     }),
  //   );
  // };

  // Yorum gönderildiğinde
  const handleSubmit = () => {
    if (commentText.trim()) {
      onSubmitComment(commentText);
      setCommentText('');
    }
  };

  // const toggleReplies = commentId => {
  //   setExpandedComments(prevState => ({
  //     ...prevState,
  //     [commentId]: !prevState[commentId],
  //   }));
  // };

  // const addComment = () => {
  //   if (newComment.trim() !== '') {
  //     const newReply = {
  //       id: `${Date.now()}`,
  //       user: 'minealada',
  //       text: newComment,
  //       avatar: require('../../assets/profilePhoto.png'),
  //       likes: 0,
  //       liked: false,
  //     };

  //     if (replyingTo) {
  //       setComments(prevComments =>
  //         prevComments.map(comment =>
  //           comment.id === replyingTo
  //             ? {...comment, replies: [...comment.replies, newReply]}
  //             : comment,
  //         ),
  //       );
  //     } else {
  //       // Yanıtlanmamışsa, ana yorumlara ekleniyor
  //       setComments(prevComments => [
  //         ...prevComments,
  //         {
  //           id: `${Date.now()}`,
  //           user: 'minealada',
  //           text: newComment,
  //           avatar: require('../../assets/profilePhoto.png'),
  //           likes: 0,
  //           liked: false,
  //           replies: [],
  //         },
  //       ]);
  //     }

  //     setNewComment('');
  //     setReplyingTo(null);
  //   }
  // };

  const formatLikes = (likes) => {
    // Handle both array of likes and direct number
    const num = Array.isArray(likes) ? likes.length : (typeof likes === 'number' ? likes : 0);

    if (num === 0) return '';
    if (num >= 1_000_000) {
      const formatted = (num / 1_000_000).toFixed(1).replace('.', ',');
      return formatted.endsWith(',0')
        ? formatted.slice(0, -2) + ' M'
        : formatted + ' M';
    } else if (num >= 1_000) {
      const formatted = (num / 1_000).toFixed(1).replace('.', ',');
      return formatted.endsWith(',0')
        ? formatted.slice(0, -2) + ' B'
        : formatted + ' B';
    } else {
      return num.toString();
    }
  };

  const [keyboardHeight, setKeyboardHeight] = useState(0);
  // Initialize with 85% of screen height to provide more space for comments
  const animatedHeight = useRef(new Animated.Value(height * 0.85)).current;

  useEffect(() => {
    const keyboardShowListener = Keyboard.addListener("keyboardDidShow", (event) => {
      const Modalheight = event.endCoordinates.height;
      setKeyboardHeight(Modalheight);

      // Adjust modal height when keyboard appears
      Animated.timing(animatedHeight, {
        toValue: height * 0.85 - Modalheight,
        duration: 150,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    const keyboardHideListener = Keyboard.addListener("keyboardDidHide", () => {
      setKeyboardHeight(0);

      // Restore modal height when keyboard disappears
      Animated.timing(animatedHeight, {
        toValue: height * 0.85,
        duration: 100,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    return () => {
      keyboardShowListener.remove();
      keyboardHideListener.remove();
    };
  }, []);

  const dispatch = useDispatch();
  const { comments: reduxComments, loading, error } = useSelector((state) => state.comments);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState(null);
  const [isReplying, setIsReplying] = useState(false);
  const [expandedComments, setExpandedComments] = useState({});
  const [showAllSubComments, setShowAllSubComments] = useState({}); // Track which comments show all sub-comments


  // Fetch comments when component mounts or postId changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await dispatch(fetchComments(postId));
        console.log('API Yanıtı:', result); // Thunk'tan dönen sonucu kontrol edin
      } catch (err) {
        console.error('API Hatası:', err);
      }
    };
    fetchData();
  }, [postId, dispatch]);

  // This effect will run whenever reduxComments or lastUpdated changes
  // It ensures the UI is updated when a new comment or reply is added
  const { lastUpdated } = useSelector((state) => state.comments);

  useEffect(() => {
    // The lastUpdated is used to trigger a re-render
    // The actual data comes from reduxComments via the formatComments function
    console.log('Comments updated:', {
      reduxCommentsCount: reduxComments?.length || 0,
      lastUpdated
    });

    // Force a re-render of the comments when lastUpdated changes
    if (lastUpdated) {
      // This will trigger a re-render with the latest comments
      const formattedComments = formatComments(reduxComments);
      console.log('Re-rendering comments after update, count:', formattedComments.length);
    }
  }, [reduxComments, lastUpdated]);

  // Format Redux comments to match your local state structure with support for nested replies
  // This function now works directly with the Redux state structure that includes optimistic updates
  const formatComments = useCallback((comments) => {
    if (!comments || !Array.isArray(comments)) {
      return [];
    }

    // Debug: Log comment structure
    console.log('Formatting comments from Redux state:', comments.length);

    // Helper function to format a single comment recursively
    const formatSingleComment = (comment) => {
      const formatted = {
        id: comment._id || comment.id,
        user: comment.user?.username || comment.user || 'Unknown',
        text: comment.text,
        avatar: comment.user?.profilePicture || comment.avatar || defaultAvatar,
        likes: comment.likes || [],
        liked: comment.likes?.includes(currentUserId) || comment.liked || false,
        parent: comment.parent,
        createdAt: comment.createdAt,
        replies: []
      };

      // Process replies if they exist
      if (comment.replies && Array.isArray(comment.replies)) {
        formatted.replies = comment.replies.map(reply => formatSingleComment(reply));

        // Sort replies by creation time (oldest first)
        formatted.replies.sort((a, b) => {
          const dateA = new Date(a.createdAt || 0);
          const dateB = new Date(b.createdAt || 0);
          return dateA - dateB;
        });
      }

      return formatted;
    };

    // Filter and format only root comments (comments without parent)
    const rootComments = comments
      .filter(comment => !comment.parent)
      .map(comment => formatSingleComment(comment));

    // Sort root comments by creation time (oldest first)
    rootComments.sort((a, b) => {
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return dateA - dateB; // Oldest first for root comments
    });

    // Debug: Log final structure
    console.log('Formatted comments - Root comments:', rootComments.length, 'Total replies:',
      rootComments.reduce((total, comment) => total + (comment.replies ? comment.replies.length : 0), 0));

    return rootComments;
  }, [currentUserId]);

  // const formatReplies = (replies) => {
  //   if (!replies || !Array.isArray(replies)) return [];
  //   return replies.map(reply => ({
  //     id: reply._id, // API yanıtında _id yerine id kullanılıyor
  //     user: reply.user?.username || 'Unknown',
  //     text: reply.text,
  //     avatar: reply.user?.profilePicture || 'https://default-profile.jpg',
  //     likes: reply.likes || [],
  //     liked: reply.likes?.some(like => like.user === currentUserId) || false,
  //     parentId: reply.parentComment // Yanıtın hangi yoruma ait olduğu
  //   }));
  // };


  const comments = useMemo(() => {
    const formattedComments = formatComments(reduxComments);
    return formattedComments;
  }, [reduxComments, lastUpdated, formatComments]);


  const toggleLike = (commentId) => {
    console.log('Toggling like for comment:', commentId);

    if (!commentId || !postId) {
      console.warn('Missing commentId or postId for like toggle');
      return;
    }

    // Dispatch the toggle comment like action
    dispatch(toggleCommentLike({
      postId,
      commentId
    }))
    .unwrap()
    .then((result) => {
      console.log('Comment like toggled successfully:', result);
    })
    .catch((error) => {
      console.error('Error toggling comment like:', error);
    });
  };

  const toggleReplies = (commentId) => {
    setExpandedComments(prev => ({
      ...prev,
      [commentId]: !prev[commentId]
    }));
  };

  const toggleShowAllSubComments = (commentId) => {
    setShowAllSubComments(prev => ({
      ...prev,
      [commentId]: !prev[commentId]
    }));
  };
  const handleReplyClick = (commentId) => {
    setReplyingTo(commentId);
    setNewComment(''); // Clear any existing comment text

    // Update the placeholder text to indicate replying
    const replyingToComment = findCommentById(commentId, comments);
    if (replyingToComment) {
      // Optional: Set a different placeholder text for the input
      // e.g., "Replying to @username..."
    }

    // Optional: focus the input field and scroll to it
    // textInputRef.current.focus();
  };

  // Helper function to find a comment by ID in the nested structure
  const findCommentById = (commentId, commentsArray) => {
    console.log(`Looking for comment with ID: ${commentId}`);

    // Recursive function to search through nested comments
    const findInComments = (comments) => {
      if (!comments || !Array.isArray(comments)) return null;

      for (const comment of comments) {
        // Check if this is the comment we're looking for
        if (comment.id === commentId) {
          return comment;
        }

        // If this comment has replies, search through them
        if (comment.replies && comment.replies.length > 0) {
          const foundInReplies = findInComments(comment.replies);
          if (foundInReplies) return foundInReplies;
        }
      }

      return null;
    };

    // Start the search from the root comments
    const result = findInComments(commentsArray);

    if (result) {
      console.log(`Found comment with ID: ${result.id}`);
      return result;
    }

    console.log(`Comment with ID ${commentId} not found`);
    return null;
  };

  const handleAddComment = () => {
    if (newComment.trim() === '') return;

    if (replyingTo) {
      // Find if we're replying to a main comment or a sub-comment
      const replyingToComment = findCommentById(replyingTo, comments);
      const isSubReply = replyingToComment && replyingToComment.parent ? true : false;

      console.log('Replying to comment:', {
        commentId: replyingTo,
        isSubReply,
        originalParentId: replyingToComment && replyingToComment.parent ? replyingToComment.parent : null,
        replyingToComment
      });

      // Create an optimistic reply object for immediate UI update
      const tempId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const optimisticReply = {
        id: tempId, // Temporary ID that will be replaced when the API responds
        _id: tempId, // Ensure both id and _id are exactly the same
        user: userInfo?.userInfo?.username || 'You',
        text: newComment,
        avatar: userInfo?.userInfo?.profilePicture || defaultAvatar,
        likes: [],
        liked: false,
        parent: replyingTo,
        replies: [],
        createdAt: new Date().toISOString() // Add timestamp for proper sorting
      };

      // Add optimistic reply to Redux state immediately for instant UI update
      dispatch(addReplyToComment({
        parentCommentId: replyingTo,
        reply: optimisticReply,
        isOptimistic: true // Flag to identify optimistic updates
      }));

      // Expand the replies section if it's not already expanded
      if (!expandedComments[replyingTo]) {
        setExpandedComments(prev => ({
          ...prev,
          [replyingTo]: true
        }));
      }

      // Add reply to Redux via API
      dispatch(addReply({
        postId,
        parentCommentId: replyingTo,
        text: newComment,
        // If we're replying to a sub-comment, include additional info
        isSubReply,
        // If it's a sub-reply, also include the original parent comment ID
        originalParentId: replyingToComment && replyingToComment.parent ? replyingToComment.parent : null
      }))
      .unwrap()
      .then((result) => {
        // Get the actual reply from the API response
        const actualReply = result.data.reply;
        console.log('Reply added successfully:', actualReply);

        // Replace the optimistic reply with the actual reply from the API
        dispatch(addReplyToComment({
          parentCommentId: replyingTo,
          reply: actualReply,
          replaceOptimistic: optimisticReply.id // Replace the optimistic update
        }));

        setNewComment('');
        setReplyingTo(null);
      })
      .catch(error => {
        console.error('Error adding reply:', error);

        // Remove the optimistic reply on error
        dispatch(addReplyToComment({
          parentCommentId: replyingTo,
          reply: optimisticReply,
          removeOptimistic: true // Flag to remove optimistic update
        }));
      });
    } else {
      // Add main comment
      console.log('Adding main comment to post:', postId);

      // If we have a callback for optimistic updates, use it
      if (onCommentCountChange) {
        onCommentCountChange(1); // Increment by 1
      }

      dispatch(addComment({
        postId,
        text: newComment,
        parentComment: null // Explicitly set to null for main comments
      }))
      .unwrap()
      .then(() => {
        setNewComment('');
      })
      .catch(error => {
        // If there's an error and we have a callback, revert the optimistic update
        if (onCommentCountChange) {
          onCommentCountChange(-1); // Decrement back by 1
        }
        console.error('Error adding comment:', error);
      });
    }
  };
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);

  const handleDeletePress = (id, isReply = false, parentCommentId = null) => {
    // If we have a callback for optimistic updates, use it only for main comments
    // Check if this is a main comment (not a reply) by checking if parentCommentId is null
    if (onCommentCountChange && !parentCommentId) {
      // Only decrement count for main comments, not replies
      onCommentCountChange(-1); // Decrement by 1
    }

    dispatch(deleteComment({
      postId: postId,
      commentId: id,
      isReply,
      parentCommentId
    }))
    .unwrap()
    .then(() => {
      console.log("Yorum başarıyla silindi");
    })
    .catch((error) => {
      // If there's an error and we have a callback, revert the optimistic update
      // Only for main comments (where parentCommentId is null)
      if (onCommentCountChange && !parentCommentId) {
        onCommentCountChange(1); // Increment back by 1 to revert
      }
      console.error('Yorum silinirken hata:', error);
    });
  };

  const cancelDelete = () => {
    setModalVisible(false);
    setSelectedMessage(null);
  };

  const confirmDelete = () => {
    if (selectedMessage) {
      // Find the comment to determine if it's a main comment or a reply
      const commentToDelete = findCommentById(selectedMessage, comments);
      const isMainComment = commentToDelete && !commentToDelete.parent;

      // If we have a callback for optimistic updates, use it only for main comments
      if (onCommentCountChange && isMainComment) {
        onCommentCountChange(-1); // Decrement by 1
      }

      dispatch(deleteComment({
        postId: postId, // Make sure to pass your post ID here
        commentId: selectedMessage,
        isReply: !isMainComment, // Set based on whether it's a main comment or reply
        parentCommentId: commentToDelete?.parent || null // Include parent ID if it's a reply
      }))
      .unwrap()
      .then(() => {
        setModalVisible(false);
        setSelectedMessage(null);
      })
      .catch((error) => {
        // If there's an error and we have a callback, revert the optimistic update
        // Only for main comments
        const commentToDelete = findCommentById(selectedMessage, comments);
        const isMainComment = commentToDelete && !commentToDelete.parent;

        if (onCommentCountChange && isMainComment) {
          onCommentCountChange(1); // Increment back by 1 to revert
        }
        console.error('Error deleting comment:', error);
        setModalVisible(false);
        setSelectedMessage(null);
      });
    }
  };

  const renderRightActions = (id, progress, dragX) => {
    const trans = dragX.interpolate({
      inputRange: [0, 50, 100, 101],
      outputRange: [0, 0, 0, 1],
    });

    return (
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeletePress(id)}>
        <Animated.View style={{ transform: [{ translateX: trans }] }}>
          <SvgTrash style={styles.trash} />
        </Animated.View>
      </TouchableOpacity>
    );
  };

    const renderCommentItem = ({item}) => (
      <Swipeable
        renderRightActions={(progress, dragX) => renderRightActions(item.id || item._id, progress, dragX)}
        rightThreshold={40}
      >
        <View style={{marginBottom: 20}}>
          <View style={styles.comment}>
            <Image
              source={{ uri: item.avatar }}
              style={styles.commentProfilePhoto}
            />
            <View style={styles.commentBody}>
              <View style={styles.commentTextContainer}>
                <Text style={styles.username}>{item.user}</Text>
                <Text style={styles.text}>{item.text}</Text>
              </View>

              <TouchableOpacity
                onPress={() => toggleLike(item.id || item._id)}
                style={styles.likeButton}>
                {item.liked ? <SvgLikeFill /> : <SvgLike />}
                <Text style={styles.likeText}>
                  {formatLikes(item.likes)}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* Reply and Show Other Comments buttons container */}
          <View style={styles.replyButtonsContainer}>
            <TouchableOpacity onPress={() => handleReplyClick(item.id || item._id)}>
              <Text style={styles.comText}>Yanıtla</Text>
            </TouchableOpacity>

            {/* Show "see other comments" button when there are replies and they're not expanded */}
            {item.replies && item.replies.length > 0 && !expandedComments[item.id || item._id] && (
              <TouchableOpacity
                onPress={() => toggleReplies(item.id || item._id)}
                style={styles.showRepliesButton}>
                <Text style={styles.showRepliesText}>
                  {item.replies.length === 1
                    ? '1 diğer yorumu gör'
                    : `${item.replies.length} diğer yorumu gör`}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {expandedComments[item.id || item._id] && (
            <>
              <FlatList
                data={showAllSubComments[item.id || item._id] ? item.replies : item.replies.slice(0, 5)} // Show only first 5 unless expanded
                keyExtractor={reply => reply.id || reply._id}
                style={styles.repliesList}
                scrollEnabled={true}
                nestedScrollEnabled={true}
                key={`replies-list-${item.id || item._id}-${lastUpdated || 'initial'}`} // Add a key to force re-render
                extraData={lastUpdated} // Add extraData to force re-render when lastUpdated changes
              renderItem={({item: reply}) => (
                <Swipeable
                  renderRightActions={(progress, dragX) => renderRightActions(reply.id || reply._id, progress, dragX)}
                  rightThreshold={40}
                >
                  {/* <View style={styles.comment}>

                    <View style={styles.commentBody}>
                      <Image
                        source={{ uri: reply.avatar }}
                        style={styles.replyAvatar}
                      />
                      <View
                      // style={styles.replyComment}
                      >
                        <View style={{gap: 2}}>
                          <Text style={styles.username}>
                            {reply.user}
                          </Text>

                          <Text style={styles.text}>{reply.text}</Text>
                        </View>
                        <TouchableOpacity
                          onPress={() => toggleLike(reply.id)}
                          style={styles.replyLikeButton}>
                          {reply.liked ? <SvgLikeFill /> : <SvgLike />}
                          <Text style={styles.likeText}>
                            {formatLikes(reply.likes)}
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View> */}
                    <View style={{marginLeft: 50}}>
                      <View style={styles.comment}>
                        <Image
                          source={{ uri: reply.avatar }}
                          style={styles.commentProfilePhoto}
                        />
                        <View style={styles.commentBody}>
                          <View style={styles.commentTextContainer}>
                            <Text style={styles.username}>{reply.user}</Text>
                            <Text style={styles.text}>{reply.text}</Text>
                          </View>
                          <TouchableOpacity
                            onPress={() => toggleLike(reply.id || reply._id)}
                            style={styles.likeButton}>
                            {reply.liked ? <SvgLikeFill /> : <SvgLike />}
                            <Text style={styles.likeText}>
                              {formatLikes(reply.likes)}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>

                      {/* Reply and Show Other Comments buttons container for sub-comments */}
                      <View style={[styles.replyButtonsContainer, {marginLeft: 50}]}>
                        <TouchableOpacity onPress={() => handleReplyClick(reply.id || reply._id)}>
                          <Text style={styles.comText}>Yanıtla</Text>
                        </TouchableOpacity>

                        {/* Display sub-replies if they exist */}
                        {reply.replies && reply.replies.length > 0 && !expandedComments[reply.id || reply._id] && (
                          <TouchableOpacity
                            onPress={() => toggleReplies(reply.id || reply._id)}
                            style={styles.showRepliesButton}>
                            <Text style={styles.showRepliesText}>
                              {reply.replies.length === 1
                                ? '1 diğer yorumu gör'
                                : `${reply.replies.length} diğer yorumu gör`}
                            </Text>
                          </TouchableOpacity>
                        )}
                      </View>

                      {/* Show sub-replies when expanded */}
                      {reply.replies && reply.replies.length > 0 && expandedComments[reply.id || reply._id] && (
                        <>
                          <FlatList
                            data={showAllSubComments[reply.id || reply._id] ? reply.replies : reply.replies.slice(0, 5)} // Show only first 5 unless expanded
                            keyExtractor={subReply => subReply.id || subReply._id}
                            style={[
                              styles.repliesList,
                              {marginLeft: 30},
                              reply.replies.length > 5 && {maxHeight: 250} // Limit height when more than 5 sub-comments
                            ]}
                            scrollEnabled={true}
                            nestedScrollEnabled={true}
                            key={`sub-replies-list-${reply.id || reply._id}-${lastUpdated || 'initial'}`} // Add a key to force re-render
                            extraData={lastUpdated} // Add extraData to force re-render when lastUpdated changes
                          renderItem={({item: subReply}) => (
                            <Swipeable
                              renderRightActions={(progress, dragX) =>
                                renderRightActions(subReply.id || subReply._id, progress, dragX)
                              }
                              rightThreshold={40}>
                              <View style={{marginLeft: 30}}>
                                <View style={styles.comment}>
                                  <Image
                                    source={{uri: subReply.avatar}}
                                    style={styles.commentProfilePhoto}
                                  />
                                  <View style={styles.commentBody}>
                                    <View style={styles.commentTextContainer}>
                                      <Text style={styles.username}>{subReply.user}</Text>
                                      <Text style={styles.text}>{subReply.text}</Text>
                                    </View>
                                    <TouchableOpacity
                                      onPress={() => toggleLike(subReply.id || subReply._id)}
                                      style={styles.likeButton}>
                                      {subReply.liked ? <SvgLikeFill /> : <SvgLike />}
                                      <Text style={styles.likeText}>
                                        {formatLikes(subReply.likes)}
                                      </Text>
                                    </TouchableOpacity>
                                  </View>
                                </View>

                                {/* Reply and Show Other Comments buttons container for nested sub-replies */}
                                <View style={[styles.replyButtonsContainer, {marginLeft: 50}]}>
                                  <TouchableOpacity onPress={() => handleReplyClick(subReply.id || subReply._id)}>
                                    <Text style={styles.comText}>Yanıtla</Text>
                                  </TouchableOpacity>

                                  {/* Handle nested sub-replies if they exist */}
                                  {subReply.replies && subReply.replies.length > 0 && !expandedComments[subReply.id || subReply._id] && (
                                    <TouchableOpacity
                                      onPress={() => toggleReplies(subReply.id || subReply._id)}
                                      style={styles.showRepliesButton}>
                                      <Text style={styles.showRepliesText}>
                                        {subReply.replies.length === 1
                                          ? '1 diğer yorumu gör'
                                          : `${subReply.replies.length} diğer yorumu gör`}
                                      </Text>
                                    </TouchableOpacity>
                                  )}
                                </View>
                              </View>

                              {/* Show nested sub-replies when expanded */}
                              {subReply.replies && subReply.replies.length > 0 && expandedComments[subReply.id || subReply._id] && (
                                <>
                                  <FlatList
                                    data={showAllSubComments[subReply.id || subReply._id] ? subReply.replies : subReply.replies.slice(0, 5)} // Show only first 5 unless expanded
                                    keyExtractor={nestedReply => nestedReply.id || nestedReply._id}
                                    style={[
                                      styles.repliesList,
                                      {marginLeft: 40},
                                      subReply.replies.length > 5 && {maxHeight: 250} // Limit height when more than 5 nested sub-comments
                                    ]}
                                    scrollEnabled={true}
                                    nestedScrollEnabled={true}
                                    key={`nested-replies-list-${subReply.id || subReply._id}-${lastUpdated || 'initial'}`} // Add a key to force re-render
                                    extraData={lastUpdated} // Add extraData to force re-render when lastUpdated changes
                                  renderItem={({item: nestedReply}) => (
                                    <Swipeable
                                      renderRightActions={(progress, dragX) =>
                                        renderRightActions(nestedReply.id || nestedReply._id, progress, dragX)
                                      }
                                      rightThreshold={40}>
                                      <View style={{marginLeft: 20}}>
                                        <View style={styles.comment}>
                                          <Image
                                            source={{uri: nestedReply.avatar}}
                                            style={styles.commentProfilePhoto}
                                          />
                                          <View style={styles.commentBody}>
                                            <View style={styles.commentTextContainer}>
                                              <Text style={styles.username}>{nestedReply.user}</Text>
                                              <Text style={styles.text}>{nestedReply.text}</Text>
                                            </View>
                                            <TouchableOpacity
                                              onPress={() => toggleLike(nestedReply.id || nestedReply._id)}
                                              style={styles.likeButton}>
                                              {nestedReply.liked ? <SvgLikeFill /> : <SvgLike />}
                                              <Text style={styles.likeText}>
                                                {formatLikes(nestedReply.likes)}
                                              </Text>
                                            </TouchableOpacity>
                                          </View>
                                        </View>

                                        {/* Reply button container for nested replies */}
                                        <View style={[styles.replyButtonsContainer, {marginLeft: 50}]}>
                                          <TouchableOpacity onPress={() => handleReplyClick(nestedReply.id || nestedReply._id)}>
                                            <Text style={styles.comText}>Yanıtla</Text>
                                          </TouchableOpacity>
                                        </View>
                                      </View>
                                    </Swipeable>
                                  )}
                                />

                                {/* Show "see other comments" button if there are more than 5 nested sub-comments and not all are shown */}
                                {subReply.replies.length > 5 && !showAllSubComments[subReply.id || subReply._id] && (
                                  <TouchableOpacity
                                    onPress={() => toggleShowAllSubComments(subReply.id || subReply._id)}
                                    style={[styles.showRepliesButton, {marginLeft: 40, marginTop: 10}]}>
                                    <Text style={styles.showRepliesText}>
                                      {`${subReply.replies.length - 5} diğer yorumu gör`}
                                    </Text>
                                  </TouchableOpacity>
                                )}
                                </>
                              )}
                            </Swipeable>
                          )}
                        />

                        {/* Show "see other comments" button if there are more than 5 sub-comments and not all are shown */}
                        {reply.replies.length > 5 && !showAllSubComments[reply.id || reply._id] && (
                          <TouchableOpacity
                            onPress={() => toggleShowAllSubComments(reply.id || reply._id)}
                            style={[styles.showRepliesButton, {marginLeft: 30, marginTop: 10}]}>
                            <Text style={styles.showRepliesText}>
                              {`${reply.replies.length - 5} diğer yorumu gör`}
                            </Text>
                          </TouchableOpacity>
                        )}
                        </>
                      )}
                    </View>
                </Swipeable>
              )}
            />

            {/* Show "see other comments" button if there are more than 5 main replies and not all are shown */}
            {item.replies.length > 5 && !showAllSubComments[item.id || item._id] && (
              <TouchableOpacity
                onPress={() => toggleShowAllSubComments(item.id || item._id)}
                style={[styles.showRepliesButton, {marginLeft: 50, marginTop: 10}]}>
                <Text style={styles.showRepliesText}>
                  {`${item.replies.length - 5} diğer yorumu gör`}
                </Text>
              </TouchableOpacity>
            )}
            </>
          )}
        </View>
      </Swipeable>
    );

    return (
      <>
        {/* Main Comment Modal */}
        <Modal
          animationType="none"
          transparent={true}
          visible={commentModal}
          onRequestClose={() => setCommentModal(false)}
        >
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            style={{ flex: 1 }}
          >
            <View style={styles.overlay}>
              {/* Overlay touchable area - closes modal when tapping outside */}
              <TouchableWithoutFeedback onPress={() => {
                Keyboard.dismiss();
                setCommentModal(false);
              }}>
                <View style={styles.overlayTouchable} />
              </TouchableWithoutFeedback>

              {/* Modal Content - Not affected by the TouchableWithoutFeedback */}
              <Animated.View style={[styles.modalContainer, { height: animatedHeight }]}>
                {/* Header */}
                <View style={styles.commentHeader}>
                  <Text style={styles.title}>Yorumlar</Text>
                  <TouchableOpacity
                    onPress={() => setCommentModal(false)}
                    activeOpacity={0.7}
                    style={styles.closeButton}
                  >
                    <SvgLess style={styles.closeBtn} />
                  </TouchableOpacity>
                </View>

                {/* Comments Section - Always scrollable */}
                <View style={styles.commentsSection}>
                  {comments.length === 0 ? (
                    <View style={styles.noCommentsContainer}>
                      <Text style={styles.noCommentsText}>Henüz yorum yapılmamış</Text>
                    </View>
                  ) : (
                    <FlatList
                      data={comments} // Display comments as sorted by formatComments (oldest first for main comments)
                      keyExtractor={item => (item.id || item._id).toString()}
                      showsVerticalScrollIndicator={true}
                      renderItem={renderCommentItem}
                      contentContainerStyle={styles.commentsList}
                      scrollEnabled={true}
                      nestedScrollEnabled={true}
                      alwaysBounceVertical={true}
                      key={`comments-list-${lastUpdated || 'initial'}`} // Add a key to force re-render
                      extraData={lastUpdated} // Add extraData to force re-render when lastUpdated changes
                    />
                  )}
                </View>

                  {/* Bottom Section */}
                  <View style={styles.bottomSection}>
                    <View style={styles.border} />
                    <View style={styles.emojiContainer}>
                      {emojis.map((emoji, index) => (
                        <TouchableOpacity
                          key={index}
                          onPress={() => addEmojiToInput(emoji)}
                        >
                          <Text style={styles.emoji}>{emoji}</Text>
                        </TouchableOpacity>
                      ))}
                    </View>

                    <View style={styles.commentInputContainer}>
                      <Image
                        source={{ uri: userInfo.userInfo.profilePicture }}
                        style={styles.userImage}
                      />
                      <TextInput
                        style={styles.commentInput}
                        placeholder="Yorum ekleyin..."
                        value={newComment}
                        onChangeText={setNewComment}
                        selectionColor="#D134AA"
                      />
                      {newComment.trim() !== '' && (
                        <TouchableOpacity
                          onPress={handleAddComment}
                          style={styles.sendButton}>
                          <SvgSendBtn />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </Animated.View>
              </View>
            </KeyboardAvoidingView>
        </Modal>

        {/* Delete Confirmation Modal - Now at root level */}
        <RNModal
          isVisible={modalVisible}
          onBackdropPress={cancelDelete}
          backdropOpacity={0.7}
          animationIn="fadeIn"
          animationOut="fadeOut"
        >
          <View style={styles.deleteModalOverlay}>
            <View style={styles.deleteModalContainer}>
              <View style={styles.deleteModalContent}>
                <Text style={styles.modalText}>Yorumu kalıcı olarak sil?</Text>
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.deleteButton]}
                    onPress={confirmDelete}
                  >
                    <Text style={styles.deleteButtonText}>Sil</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.cancelButton]}
                    onPress={cancelDelete}
                  >
                    <Text style={styles.cancelButtonText}>İptal</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </RNModal>
      </>
    );
};

export default CommentModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1
  },
  overlayTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },

  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 20,
    maxHeight: height * 0.55, // Increased height for more space
    flexDirection: 'column',
    overflow: 'hidden', // Ensure content doesn't overflow
  },
  commentsSection: {
    flex: 1, // Take up all available space
    marginBottom: 10,
    overflow: 'hidden', // Ensure content doesn't overflow
  },
  commentsList: {
    paddingBottom: 10,
  },
  bottomSection: {
    width: '100%',
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    marginBottom: 25,
    position: 'relative',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    right: 16,
  },
  closeBtn: {
    width: 24,
    height: 24,
  },
  userImage: {
    // width: width * 0.11,
    // height: width * 0.11,
    width: 42,
    height: 42,
    borderRadius: 20,
    marginRight: 10,
  },
  commentProfilePhoto: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },

  username: {
    fontWeight: '500',
    fontSize: 12,
  },
  likeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 5,
    position: 'absolute',
    right: 10, // Adjusted from 0 to provide proper spacing
  },


  likeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#9D9C9C',
  },
  replyButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 50,
    marginTop: 5,
    gap: 15, // Space between reply button and show other comments button
  },
  showRepliesButton: {
    marginTop: 2,
  },
  showRepliesText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#9D9C9C',
  },
  comText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#9D9C9C',
  },
  replyContainer: {
    flexDirection: 'row',
    marginTop: 15,
    marginLeft: 0,
    width: '100%',
    alignItems: 'flex-start',
    paddingRight: 10,
    paddingBottom: 10,
    borderLeftWidth: 0,
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 10,
    position: 'relative',
  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 59,
    paddingHorizontal: 15,
    paddingRight: 40, // Add space for the send button
    height: 44,
    marginTop: 2,
  },
  sendButton: {
    position: 'absolute',
    right: 15,
    marginTop: '3%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  // replyInputContainer: {
  //   flexDirection: "row",
  //   alignItems: "center",
  //   marginTop: 10,
  //   borderTopWidth: 1,
  //   borderTopColor: "#ddd",
  //   paddingTop: 5,
  // },
  // replyInput: {
  //   flex: 1,
  //   borderWidth: 1,
  //   borderColor: "#ddd",
  //   borderRadius: 20,
  //   paddingHorizontal: 15,
  //   height: 40,
  // },
  // replySubmitButton: {
  //   marginLeft: 10,
  // },
  text: {
    maxWidth: 236,
    fontSize: 12,
    fontWeight: 400,
  },
  comment: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  commentBody: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    position: 'relative',
    paddingRight: 50, // Increased padding for like button space
  },
  commentTextContainer: {
    flex: 1,
    gap: 2,
    maxWidth: '85%', // Increased from 75% to show more content
  },

  border: {
    borderTopWidth: 1,
    borderTopColor: '#F1F1F1',
  },
  emojiContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 5,

    gap: 38,
  },
  emoji: {
    fontSize: 24,
  },
  deleteButton: {
    backgroundColor: 'red',
    justifyContent: 'center',
    alignItems: 'flex-end',
    width: 80,
    paddingRight: 20,
    marginBottom: 20,
  },
  trash: {
    width: 24,
    height: 24,
    tintColor: 'white',
  },
  deleteModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteModalContainer: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
  },
  deleteModalContent: {
    width: '100%',
  },

  modalText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    padding: 10,
    borderRadius: 5,
    width: '48%',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: '#E33629',
    justifyContent: 'center',
    alignItems: 'center',
    width: 35,
    height: 55,
  },
  cancelButton: {
    backgroundColor: '#ccc',
  },
  deleteButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  cancelButtonText: {
    color: 'black',
    fontWeight: 'bold',
  },
  noCommentsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    minHeight: 200, // Ensure minimum height even when empty
  },
  noCommentsText: {
    fontSize: 16,
    color: '#999',
  },
  repliesList: {
    alignSelf: 'flex-start',
    width: '100%',
    marginLeft: 0,
    paddingRight: 10,
    marginTop: 5,
    // maxHeight removed - now applied conditionally when more than 5 sub-comments
  },
  replyingToContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: '#F0F0F0',
    borderRadius: 5,
    marginBottom: 5,
    marginTop: 0,
    zIndex: 2,
  },
  replyingToText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  cancelReplyText: {
    fontSize: 12,
    color: '#D134AA',
    fontWeight: '600',
  },
  subReplyIndicator: {
    width: 2,
    backgroundColor: '#E0E0E0',
    marginLeft: 20,
    marginRight: 10,
    height: '100%',
  },

});


