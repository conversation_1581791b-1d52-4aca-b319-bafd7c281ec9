import {useState, useEffect, useRef} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Text,
  Animated,
  TouchableWithoutFeedback,
  useWindowDimensions,
  Dimensions,
  FlatList,
  Image,
} from 'react-native';
// Temporarily comment out FastImage
// import FastImage from 'react-native-fast-image';
import SvgCloseLight from '../../assets/closeLight';
import SvgHeart from '../../assets/heart';
import SvgComments from '../../assets/comments';
import SvgBookmark from '../../assets/bookmark';
import SvgShare from '../../assets/share';
import SvgHanger from '../../assets/hanger';
import SvgPlusPinkB from '../../assets/plusPinkB';
import LinearGradient from 'react-native-linear-gradient';
import ShareModal from '../../components/Home/ShareModal';
import CommentModal from '../../components/Home/CommentModal';
import CollectionsModal from '../../components/Home/CollectionsModal';
import HangerModal from '../../components/Home/HangerModal';
// import {height, width} from '../../utils/helpers';
import { useDispatch, useSelector } from 'react-redux';
import { addLike, removeLike } from '../../redux/slices/likesSlice';
import { savePost, unsavePost } from '../../redux/slices/savedPostSlice';
// Import for comment functionality
// import { addComment } from '../../redux/slices/commentsSlice';
import { useNavigation, useRoute } from '@react-navigation/native';
import { fetchPostDetail } from '../../redux/actions/getPostDetailActions';
import { optimisticLikePost, toggleLike } from '../../redux/actions/postActions';
import { selectIsPostLiked } from '../../redux/slices/postSlice';
import store from '../../redux/store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import SvgHeartFill from "../../assets/heartFill"
import ProfileHangerModal from './ProfileHangerModal';
import { addComment } from '../../redux/actions/commentActions';

const UserPost = ({ onClose }) => {
  const dispatch = useDispatch();
  const navigation = useNavigation()
  const route = useRoute();
  const { image, item } = route.params;
  const likesCount = useSelector((state) => state.likes.likesCount);
  const commentsCount = useSelector((state) => state.comments.commentsCount);
  const [scrollY] = useState(new Animated.Value(0));
  const [fadeAnim] = useState(new Animated.Value(1));
  const [translateY] = useState(new Animated.Value(0));
  const [modalVisible, setModalVisible] = useState(false);
  const [commentModal, setCommentModal] = useState(false);
  const [collectionModal, setCollectionModal] = useState(false);
  const [hangerModal, setHangerModal] = useState(false);
  const [isHangerOpen, setIsHangerOpen] = useState(false);
  const [loaderVisible, setLoaderVisible] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const likes = useSelector((state) => state.likes.likes);
  const likedBy = useSelector((state) => state.likes.likedBy);
  // const userId = 1;
  const commentCount = useSelector((state) => state.comments.commentCount);
  const savedCounts = useSelector(state => state.savedPosts.savedCounts);
  // const isSaved = savedPosts.includes(item.id);
// const saveCount = savedCounts[item.id] || 0;
const savedPosts = useSelector((state) => state.savedPosts.savedPosts);
  // const shareCount = useSelector((state) => state.share.shareCount);
const { postId } = route.params; // Initial postId from navigation
const { currentPost, loading, error } = useSelector(state => state.getPostDetail);
// Şu anki kullanıcıyı al
const currentUser = useSelector(state => state.auth.user);
const currentUserId= currentUser?.id || currentUser?._id

// Local state for optimistic UI updates
const [optimisticLikeCount, setOptimisticLikeCount] = useState(null);
const [optimisticIsLiked, setOptimisticIsLiked] = useState(null);
const [optimisticCommentCount, setOptimisticCommentCount] = useState(null);

// Track the currently visible post ID
const [currentPostId, setCurrentPostId] = useState(postId);

console.log("like sayısı", currentPost?.likeCount)

useEffect(() => {
  if (currentPostId) {
    // Only fetch post detail if we don't already have it in Redux
    // This prevents overwriting optimistic updates when re-entering the post
    if (!currentPost || currentPost._id !== currentPostId) {
      console.log('Fetching post detail for postId:', currentPostId);
      dispatch(fetchPostDetail(currentPostId));
    } else {
      console.log('Post detail already available in Redux, skipping fetch');
    }
  }
}, [currentPostId, dispatch, currentPost]);

// Note: Like/comment display values are now calculated per item in renderItem function

// Log the current post state for debugging
useEffect(() => {
  console.log('Current post state:', {
    currentPostId,
    optimisticIsLiked,
    optimisticLikeCount,
    optimisticCommentCount,
    reduxLikeCount: currentPost?.likeCount,
    reduxIsLiked: currentPost?.likes?.includes(currentUserId)
  });
}, [currentPostId, optimisticIsLiked, optimisticLikeCount, optimisticCommentCount, currentPost]);
// Inside your UserPost component
console.log("Route params:", route.params);
console.log("Redux post detail state:", { currentPost, loading, error });

// If you want to see the detailed structure of the currentPost
useEffect(() => {
  if (currentPost) {
    console.log("Current post details:", JSON.stringify(currentPost, null, 2));
    console.log("Post images:", currentPost.images || currentPost.postPhoto);
    console.log("Post owner info:", currentPost.userId || currentPost.owner);

    // When Redux state changes and we have optimistic values for likes
    if (optimisticLikeCount !== null || optimisticIsLiked !== null) {
      // Check if the Redux state matches our optimistic prediction
      const reduxIsLiked = currentPost?.likes?.includes(currentUserId) || false;

      console.log('Comparing like states:', {
        optimisticLikeCount,
        reduxLikeCount: currentPost?.likeCount,
        optimisticIsLiked,
        reduxIsLiked
      });

      // If the Redux state has been updated to match our optimistic prediction,
      // we can safely reset the optimistic values
      if (optimisticIsLiked === reduxIsLiked &&
          (optimisticLikeCount === currentPost?.likeCount ||
           Math.abs(optimisticLikeCount - currentPost?.likeCount) < 0.1)) {
        console.log('Redux like state matches optimistic prediction, resetting optimistic values');
        setOptimisticLikeCount(null);
        setOptimisticIsLiked(null);
      }
    }

    // When Redux state changes and we have optimistic values for comments
    if (optimisticCommentCount !== null) {
      console.log('Comparing comment states:', {
        optimisticCommentCount,
        reduxCommentCount: currentPost?.commentCount
      });

      // If the Redux state has been updated to match our optimistic prediction,
      // we can safely reset the optimistic values
      if (optimisticCommentCount === currentPost?.commentCount) {
        console.log('Redux comment state matches optimistic prediction, resetting optimistic values');
        setOptimisticCommentCount(null);
      }
    }
  }
}, [currentPost, optimisticLikeCount, optimisticIsLiked, optimisticCommentCount, currentUserId]);

const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      navigation.goBack();
    }
  };

  // const isLiked = useSelector(state =>
  //   state.posts.likedPosts.includes(postId)
  // );


  const handleLike = () => {
    console.log('Beğen butonuna tıklandı, currentPostId:', currentPostId);

    // Get current like state for the current post
    const currentIsLiked = optimisticIsLiked !== null
      ? optimisticIsLiked
      : (currentPost?.likes?.includes(currentUserId) || false);

    const currentLikeCount = optimisticLikeCount !== null
      ? optimisticLikeCount
      : (currentPost?.likeCount || 0);

    // Calculate new optimistic values
    const newIsLiked = !currentIsLiked;
    const newLikeCount = newIsLiked
      ? currentLikeCount + 1
      : Math.max(currentLikeCount - 1, 0);

    // Log current state for debugging
    console.log('Before like action:', {
      currentLikeCount,
      currentIsLiked,
      newIsLiked,
      newLikeCount
    });

    // Update local state for immediate UI feedback
    setOptimisticIsLiked(newIsLiked);
    setOptimisticLikeCount(newLikeCount);

    // Dispatch the action to update the server and Redux store using current post ID
    dispatch(toggleLike(currentPostId))
      .then((result) => {
        // Log the API response for debugging
        console.log('API response:', result.payload);

        // Don't reset optimistic values - let the Redux state take over naturally
        // when the component re-renders with the new Redux state
      })
      .catch(error => {
        // If there's an error, revert optimistic updates
        setOptimisticIsLiked(null);
        setOptimisticLikeCount(null);
        console.error('Error toggling like:', error);
      });
  };

  // const handleLike = async (post) => {
  //   try {
  //     console.log('[UI] Like button clicked for post:', postId);
  //     const result = await dispatch(toggleLike(postId));

  //     if (toggleLike.fulfilled.match(result)) {
  //       console.log('[UI] Like updated:', result.payload);
  //     } else {
  //       console.warn('[UI] Like update failed:', result.payload);
  //     }
  //   } catch (err) {
  //     console.error('[UI] Error in handleLike:', err.message);
  //   }
  // };


  // const handleLike = async () => {
  //   console.log('[UI] Like button clicked for post:', post._id);
  //   console.log('[UI] Current post data:', {
  //     isLiked: post.isLiked,
  //     likeCount: post.likeCount
  //   });

  //   try {
  //     console.log('[UI] Dispatching toggleLike action');
  //     const result = await dispatch(toggleLike(post._id));

  //     if (toggleLike.fulfilled.match(result)) {
  //       console.log('[UI] Like action successful:', result.payload);
  //     } else {
  //       console.warn('[UI] Like action rejected');
  //     }
  //   } catch (error) {
  //     console.error('[UI] Error in handleLike:', error.message);
  //     Alert.alert('Hata', error.message);
  //   }

  //   // Son state'i kontrol
  //   const newState = store.getState();
  //   const updatedPost = newState.posts.feedPosts.find(p => p._id === post._id);
  //   console.log('[UI] Updated post data:', {
  //     isLiked: updatedPost?.isLiked,
  //     likeCount: updatedPost?.likeCount
  //   });
  // };

  // Yorum butonuna tıklandığında
  const handleComment = () => {
    setCommentModal(true);
  };
  // Yorum gönderildiğinde
  const handleSubmitComment = (commentText) => {
    if (commentText.trim() === '') return;

    // Calculate new optimistic comment count
    const newCommentCount = (currentPost?.commentCount || 0) + 1;

    // Update local state for immediate UI feedback
    setOptimisticCommentCount(newCommentCount);

    console.log('Optimistic comment count update:', {
      oldCommentCount: currentPost?.commentCount || 0,
      newCommentCount
    });

    // Correct implementation using the proper addComment action with current post ID
    dispatch(addComment({
      postId: currentPostId,
      text: commentText,
      parentComment: null // Main comment, not a reply
    }))
    .unwrap()
    .then(() => {
      console.log('Comment added successfully');
      // The Redux state will be updated, but we'll keep our optimistic value
      // until we confirm the Redux state matches our prediction
    })
    .catch(error => {
      // If there's an error, revert optimistic update
      setOptimisticCommentCount(null);
      console.error('Error adding comment:', error);
    });

    setCommentModal(false);
  };


  const handleSave = () => {
    setCollectionModal(true);

    if (savedPosts && savedPosts.includes(image.id)) {
      dispatch(unsavePost(image.id));
    } else {
      dispatch(savePost(image.id));
    }
  };

    // Paylaş butonuna tıklandığında
    const handleShare = () => {
      setModalVisible(true);
    };

    // Handle share modal close - clear selected users when modal is closed
    const handleShareModalClose = () => {
      console.log('UserPost: Share modal closing, clearing selected users:', selectedUsers);
      setSelectedUsers([]);
      setModalVisible(false);
    };




//   useEffect(() => {
//     const listener = scrollY.addListener(({ value }) => {
//       if (value < -150) {
//         Animated.parallel([
//           Animated.timing(fadeAnim, {
//             toValue: 0,
//             duration: 300,
//             useNativeDriver: true,
//           }),
//           Animated.timing(translateY, {
//             toValue: -500,
//             duration: 300,
//             useNativeDriver: true,
//           }),
//         ]).start(() => {
//           onClose();
//         });
//       }
//     });

//     return () => {
//       scrollY.removeListener(listener);
//     };
//   }, [scrollY, fadeAnim, translateY, onClose]);

  const onScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    { useNativeDriver: false },
  );

  const toggleHangerModal = () => {
    if (!hangerModal) {
      setIsHangerOpen(true);
    } else {
      setTimeout(() => setIsHangerOpen(false), 300);
    }
    setHangerModal(!hangerModal);
  };
  const { sheight, swidth } = useWindowDimensions();

  // const { selectedImage, allPosts } = route.params; // selectedImage ve allPosts alınıyor
  const flatListRef = useRef(null);
  const screenHeight = Dimensions.get('window').height;

  // // Görsellerin sırasını ayarlamak için
  // const selectedIndex = allPosts.findIndex((img) => img.id === selectedImage.id);
  const { selectedImage, userPosts = [], initialIndex } = route.params;
  const [currentIndex, setCurrentIndex] = useState(initialIndex || 0);

  // Use the userPosts array passed from ProfilePost instead of just the current post
  const postsToDisplay = userPosts && userPosts.length > 0 ? userPosts : (currentPost ? [currentPost] : []);

  // Find the index of the selected image in the posts array
  const safeSelectedIndex = userPosts.findIndex(
    post => post?._id === selectedImage?._id
  );

  // Log for debugging
  console.log("Posts to display count:", postsToDisplay.length);
  console.log("Selected index:", safeSelectedIndex);

  // Initialize current post ID based on the initial index
  useEffect(() => {
    if (postsToDisplay.length > 0 && safeSelectedIndex !== -1) {
      const initialPost = postsToDisplay[safeSelectedIndex];
      if (initialPost?._id && initialPost._id !== currentPostId) {
        console.log('Setting initial current post ID:', initialPost._id);
        setCurrentPostId(initialPost._id);
      }
    }
  }, [postsToDisplay, safeSelectedIndex]);

  useEffect(() => {
    // Scroll to the selected post when component mounts
    if (safeSelectedIndex !== -1 && flatListRef.current && postsToDisplay.length > 0) {
      setTimeout(() => {
        // Double check that the ref is still valid and the index is within bounds
        if (flatListRef.current && safeSelectedIndex < postsToDisplay.length) {
          try {
            flatListRef.current.scrollToIndex({
              index: safeSelectedIndex,
              animated: false,
              viewPosition: 0
            });
          } catch (error) {
            console.warn('Error scrolling to index:', error);
            // Fallback: try scrolling with a longer delay
            setTimeout(() => {
              if (flatListRef.current && safeSelectedIndex < postsToDisplay.length) {
                try {
                  flatListRef.current.scrollToIndex({
                    index: safeSelectedIndex,
                    animated: false,
                    viewPosition: 0
                  });
                } catch (retryError) {
                  console.warn('Retry scroll failed:', retryError);
                }
              }
            }, 500);
          }
        }
      }, 100);
    }
  }, [safeSelectedIndex, postsToDisplay.length]);

  // Handle scroll events to update current index and current post
  const handleScroll = (event) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const index = Math.floor(offsetY / screenHeight);
    if (index !== currentIndex && index >= 0 && index < postsToDisplay.length) {
      setCurrentIndex(index);

      // Update current post ID when scrolling to a different post
      const newPostId = postsToDisplay[index]?._id;
      if (newPostId && newPostId !== currentPostId) {
        console.log('Switching to post:', newPostId, 'at index:', index);
        setCurrentPostId(newPostId);

        // Reset optimistic states when switching posts
        setOptimisticLikeCount(null);
        setOptimisticIsLiked(null);
        setOptimisticCommentCount(null);
      }
    }
  };

  return (
    <>
      <FlatList
        data={postsToDisplay}
        keyExtractor={(item) => item._id}
        ref={flatListRef}
        pagingEnabled
        horizontal={false}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        getItemLayout={(data, index) => ({
          length: screenHeight,
          offset: screenHeight * index,
          index,
        })}
        onScrollToIndexFailed={(info) => {
          console.log("UserPost scroll to index failed:", info);
          // Try again with a delay, but with better error handling
          setTimeout(() => {
            if (flatListRef.current && info.index < postsToDisplay.length) {
              try {
                flatListRef.current.scrollToIndex({
                  index: info.index,
                  animated: false,
                  viewPosition: 0
                });
              } catch (retryError) {
                console.warn("UserPost retry scroll to index failed:", retryError);
                // As a last resort, try scrolling to offset
                try {
                  const offset = info.index * screenHeight;
                  flatListRef.current.scrollToOffset({
                    offset: offset,
                    animated: false
                  });
                } catch (offsetError) {
                  console.warn("UserPost scroll to offset also failed:", offsetError);
                }
              }
            }
          }, 500);
        }}
        renderItem={({ item }) => {
          // Ensure we have a valid image URL
          const imageUrl = item?.image || item?.postPhoto || '';
          // Get user data from the item or from selectedImage as fallback
          const userData = item?.user || selectedImage?.user || {};
          const profilePicture = userData?.profilePicture || '';
          const username = userData?.username || '';

          // Calculate display values for this specific item
          const isCurrentItem = item._id === currentPostId;

          // For the current item, use optimistic values or Redux state
          // For other items, use the item's own data
          const itemIsLiked = isCurrentItem
            ? (optimisticIsLiked !== null ? optimisticIsLiked : (currentPost?.likes?.includes(currentUserId) || false))
            : (item?.likes?.includes(currentUserId) || false);

          const itemLikeCount = isCurrentItem
            ? (optimisticLikeCount !== null ? optimisticLikeCount : (currentPost?.likeCount || item?.likeCount || 0))
            : (item?.likeCount || 0);

          const itemCommentCount = isCurrentItem
            ? (optimisticCommentCount !== null ? optimisticCommentCount : (currentPost?.commentCount || item?.commentCount || 0))
            : (item?.commentCount || 0);

          return (
            <View style={{ height: screenHeight }}>
              <TouchableWithoutFeedback onPress={() => setModalVisible(false)}>
                <View style={{ flex: 1 }}>
                  <Animated.ScrollView
                    contentContainerStyle={styles.scrollContainer}
                    showsVerticalScrollIndicator={false}
                    onScroll={onScroll}
                    scrollEventThrottle={16}
                  >
                    <Animated.View
                      style={[
                        styles.container,
                        {
                          opacity: fadeAnim,
                          transform: [{ translateY }],
                        },
                      ]}
                    >
                      {/* Üst Gölge */}
                      <LinearGradient
                        colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0)']}
                        style={styles.topGradient}
                      />

                      {/* Fotoğraf */}
                      {imageUrl ? (
                        <Image
                          source={{ uri: imageUrl }}
                          style={styles.image}
                          resizeMode="contain"
                        />
                      ) : (
                        <View style={[styles.image, {backgroundColor: '#f0f0f0', justifyContent: 'center', alignItems: 'center'}]}>
                          <Text style={{color: '#999'}}>No image available</Text>
                        </View>
                      )}

                      {/* Alt Gölge */}
                      <LinearGradient
                        colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.7)']}
                        style={styles.bottomGradient}
                      />
                    </Animated.View>

                    <TouchableOpacity onPress={handleClose} style={styles.closeBtn}>
                      <SvgCloseLight />
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.action}>
                      {hangerModal ? null : (
                        <>
                          {/* Beğeni Butonu */}
                          <TouchableOpacity onPress={handleLike} style={styles.actionBtn}>
                            {itemIsLiked ? <SvgHeartFill /> : <SvgHeart />}
                            <Text style={styles.actionText} >{itemLikeCount}</Text>
                          </TouchableOpacity>

                          {/* Yorum Butonu */}
                          <View>
                            <TouchableOpacity style={styles.actionBtn} onPress={handleComment}>
                              <SvgComments />
                              <Text style={styles.actionText}>{itemCommentCount}</Text>
                            </TouchableOpacity>
                            <CommentModal
                              commentModal={commentModal}
                              setCommentModal={setCommentModal}
                              onSubmitComment={handleSubmitComment}
                              postId={currentPostId}
                              onCommentCountChange={(change) => {
                                // Update optimistic comment count
                                const currentCount = optimisticCommentCount !== null
                                  ? optimisticCommentCount
                                  : (currentPost?.commentCount || 0);
                                setOptimisticCommentCount(Math.max(0, currentCount + change));
                              }}
                            />
                          </View>

                          {/* Kaydet Butonu */}
                          <View>
                            <TouchableOpacity style={styles.actionBtn} onPress={handleSave}>
                              <SvgBookmark />
                            </TouchableOpacity>
                            <CollectionsModal
                              collectionModal={collectionModal}
                              setCollectionModal={setCollectionModal}
                            />
                          </View>

                          {/* Paylaş Butonu */}
                          <View>
                            <TouchableOpacity style={styles.actionBtn} onPress={handleShare}>
                              <SvgShare />
                            </TouchableOpacity>
                            <ShareModal
                              modalVisible={modalVisible}
                              setModalVisible={handleShareModalClose}
                              selectedUsers={selectedUsers}
                              setSelectedUsers={setSelectedUsers}
                            />
                          </View>
                        </>
                      )}
                      {!hangerModal && (
                        <View>
                          <TouchableOpacity style={styles.actionBtn} onPress={toggleHangerModal}>
                            <SvgHanger />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>

                    {/* Profil */}
                    {hangerModal ? null : (
                      <View style={styles.profile}>
                        <View style={styles.profileContainer}>
                          <View style={styles.imageWrapper}>
                            {/* Kullanıcı profil fotoğrafı - user objesinden çekiyoruz */}
                            <Image
                              source={{ uri: profilePicture }}
                              style={styles.profileImage}
                              resizeMode="cover"
                            />
                          </View>
                          <TouchableOpacity style={styles.plusButton}>
                            <SvgPlusPinkB />
                          </TouchableOpacity>
                        </View>

                        <View style={styles.title}>
                          <Text style={styles.username}>
                            @<Text style={styles.boldUsername}>{username}</Text>
                          </Text>
                          <Text style={styles.caption}>
                            {/* Parse description and style hashtags with pink color */}
                            {(item.description || '').split(/(\s+)/).map((word, index) => {
                              if (word.startsWith('#')) {
                                return <Text key={index} style={styles.hashtag}>{word} </Text>;
                              }
                              return word;
                            })}
                          </Text>
                        </View>
                      </View>
                    )}
                  </Animated.ScrollView>
                </View>
              </TouchableWithoutFeedback>
            </View>
          );
        }}
      />

      {/* ProfileHangerModal outside of renderItem to ensure it gets current post data */}
      <ProfileHangerModal
        hangerModal={hangerModal}
        setHangerModal={toggleHangerModal}
        post={currentPost || (postsToDisplay[currentIndex] || {})}
      />
    </>
  );
};

export default UserPost;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
  },
  topGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 180,
  },
  bottomGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 180,
  },
  closeBtn: {
    position: 'absolute',
    top: 80,
    right: 30,
    zIndex: 10,
  },
  scrollContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
  action: {
    position: 'absolute',
    zIndex: 10,
    bottom: 60,
    right: 20,
    gap: 16,
  },
  actionBtn: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  profile: {
    position: 'absolute',
    zIndex: 10,
    bottom: 60,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  imageWrapper: {
    position: 'relative',
    width: 55,
    height: 55,
    borderRadius: 50,
    overflow: 'hidden',
  },
  profileImage: {
    width: 55,
    height: 55,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: '#FFF',
  },
  plusButton: {
    position: 'absolute',
    top: -3,
    right: -3,
    width: 19,
    height: 19,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    maxWidth: 220,
    flexDirection: 'column',
  },
  username: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '600',
  },
  boldUsername: {
    fontWeight: '700',
  },
  description: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  caption: {
    color: '#FFF',
    fontSize: 14,
    fontWeight: '400',
  },
  hashtag: {
    color: '#FFC2F0',
    fontSize: 14,
    fontWeight: '400',
  },
  additionalData: {
    marginTop: 20,
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    width: '90%',
  },
  item: {
    marginBottom: 10,
  },
  itemText: {
    color: '#fff',
    fontSize: 16,
  },
});