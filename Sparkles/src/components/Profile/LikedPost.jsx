import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux';
import { fetchPostDetail } from '../../redux/actions/getPostDetailActions';
import { useNavigation } from '@react-navigation/native';
import Loader from '../Loader';

const LikedPost = ({userInfo}) => {
    const navigation = useNavigation()
    const [numColumns, setNumColumns] = useState(3);
    const dispatch = useDispatch();
    const [likedPostsDetails, setLikedPostsDetails] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingStartTime, setLoadingStartTime] = useState(null);
    const [loadingDuration, setLoadingDuration] = useState(0);

    // Update loading duration when loading state changes
    useEffect(() => {
      if (loading && !loadingStartTime) {
        // Loading just started
        setLoadingStartTime(Date.now());
        setLoadingDuration(0);
      } else if (!loading && loadingStartTime) {
        // Loading just finished
        const duration = Date.now() - loadingStartTime;
        setLoadingDuration(duration);
        setLoadingStartTime(null);
      }
    }, [loading, loadingStartTime]);

    // Determine loader speed based on previous loading duration
    const getLoaderSpeed = () => {
      if (loadingDuration === 0) return 'normal'; // Default for first load
      if (loadingDuration < 500) return 'fast';
      if (loadingDuration > 2000) return 'slow';
      return 'normal';
    };

    useEffect(() => {
      const fetchLikedPosts = async () => {
        if (userInfo?.userInfo?.likedPosts?.length > 0) {
          setLoading(true);
          try {
            const postsDetails = await Promise.all(
              userInfo.userInfo.likedPosts.map(postId =>
                dispatch(fetchPostDetail(postId)).unwrap()
              )
            );
            setLikedPostsDetails(postsDetails);
          } catch (error) {
            console.error('Error fetching liked posts:', error);
          } finally {
            setLoading(false);
          }
        }
      };

      fetchLikedPosts();
    }, [userInfo?.userInfo?.likedPosts, dispatch]);

    const handleImagePress = async (item) => {
      try {
        // if (!userId) {
        //   throw new Error("User ID is missing in the post data");
        // }

        // Find the index of the clicked post in the existing posts array
        // const postIndex = posts.findIndex(post => post._id === item._id);

        // if (postIndex === -1) {
        //   throw new Error("Post not found in the current posts array");
        // }

        navigation.navigate('UserPost', {
          postId: item._id,
          selectedImage: item,
          // initialIndex: postIndex,
          // userPosts: posts // Pass the entire posts array if needed in UserPost screen
        });
      } catch (error) {
        console.error('Failed to navigate to post details:', error.message);
      }
    };
    return (
      <View style={{ position: 'relative', flex: 1 }}>
        {loading && (
          <View style={styles.loaderContainer}>
            <Loader isVisible={true} speed={getLoaderSpeed()} />
          </View>
        )}

        {!loading && likedPostsDetails && likedPostsDetails.length > 0 ? (
          <FlatList
            data={likedPostsDetails}
            keyExtractor={item => item.id}
            numColumns={3}
            columnWrapperStyle={styles.columnWrapper}
            renderItem={({ item }) => (
              item.image ? (
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={styles.itemContainer}
                  onPress={() => handleImagePress(item)}
                >
                  <Image
                    source={{ uri: item.image }}
                    style={styles.image}
                  />
                </TouchableOpacity>
              ) : null
            )}
          />
        ) : !loading && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Henüz beğenilen gönderi yok</Text>
          </View>
        )}
      </View>
    );
  };

  const styles = StyleSheet.create({
    columnWrapper: {
      justifyContent: "space-between",
      marginHorizontal: -2,
    },
    itemContainer: {
      flex: 1,
      margin: 1
    },
    image: {
      width: 120,
      height: 177,
      borderRadius: 4,
    },
    loaderContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 10,
      height: 'auto',
    },
    emptyContainer: {
      height: 300,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: '#666',
    }
  });

  export default LikedPost;