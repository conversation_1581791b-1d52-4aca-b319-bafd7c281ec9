import { FlatList, StyleSheet, Text, TouchableOpacity, View, Image } from 'react-native'
// Temporarily comment out FastImage
// import FastImage from 'react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { useState, useEffect } from 'react';
import { fetchPostDetail } from '../../redux/actions/getPostDetailActions';
import { fetchUserPosts } from '../../redux/actions/postActions';
import Loader from '../Loader';
// Import filter components
import {
  Grayscale,
  Sepia,
  Saturate,
  Brightness,
  Invert,
  Normal,
  ColorMatrix,
  Vintage,
  Polaroid,
  Warm,
  Cool,
  Night,
  LSD,
  saturate,
  brightness,
  invert,
  sepia,
  grayscale,
  vintage,
  polaroid,
  warm,
  cool,
  night,
  lsd
} from 'react-native-color-matrix-image-filters';

const ProfilePost = ({posts, loading: propLoading, userId: propUserId}) => {
  const navigation = useNavigation()
  const dispatch = useDispatch()
  const userInfo = useSelector((state) => state.user);
  // Use the userId passed from props if available, otherwise use from state
  const userId = propUserId || userInfo?.userInfo.id
  // Use loading from props if provided, otherwise from state
  const stateLoading = useSelector((state) => state.posts.loading);
  const loading = propLoading !== undefined ? propLoading : stateLoading;

  const getFirstImage = (item) => {
    if (item.postPhoto) return item.postPhoto;
    if (item.images && item.images.length > 0) return item.images[0];
    if (item.image) return item.image;
    if (item.postImage) return item.postImage;
    return null;
  };

  // Define filter components using color matrix filters
  const filters = [
    { name: "No Filter", component: Normal, matrix: null },
    { name: "Invert", component: Invert, matrix: invert() },
    { name: "Saturate", component: Saturate, amount: 2.0, matrix: saturate(2.0) },
    { name: "Brightness", component: Brightness, amount: 1.5, matrix: brightness(1.5) },
    { name: "Grayscale", component: Grayscale, matrix: grayscale() },
    { name: "Sepia", component: Sepia, matrix: sepia() },
    { name: "Vintage", component: Vintage, matrix: vintage() },
    { name: "Polaroid", component: Polaroid, matrix: polaroid() },
    { name: "Warm", component: Warm, matrix: warm() },
    { name: "Cool", component: Cool, matrix: cool() },
    { name: "Night", component: Night, matrix: night(0.1) },
    { name: "LSD", component: LSD, matrix: lsd() }
  ];

  // Function to get the filter component based on filter name
  const getFilterComponent = (filterName) => {
    const filter = filters.find(f => f.name === filterName);
    return filter ? filter : filters[0]; // Default to "No Filter" if not found
  };
console.log("profile post", posts)

const handleImagePress = async (item) => {
  try {
    if (!userId) {
      throw new Error("User ID is missing in the post data");
    }

    // Find the index of the clicked post in the existing posts array
    const postIndex = posts.findIndex(post => post._id === item._id);

    if (postIndex === -1) {
      throw new Error("Post not found in the current posts array");
    }

    navigation.navigate('UserPost', {
      postId: item._id,
      selectedImage: item,
      initialIndex: postIndex,
      userPosts: posts
    });
  } catch (error) {
    console.error('Failed to navigate to post details:', error.message);
  }
};

  // Track loading state for better UX
  const [loadingStartTime, setLoadingStartTime] = useState(null);
  const [loadingDuration, setLoadingDuration] = useState(0);

  // Update loading duration when loading state changes
  useEffect(() => {
    if (loading && !loadingStartTime) {
      // Loading just started
      setLoadingStartTime(Date.now());
      setLoadingDuration(0);
    } else if (!loading && loadingStartTime) {
      // Loading just finished
      const duration = Date.now() - loadingStartTime;
      setLoadingDuration(duration);
      setLoadingStartTime(null);
    }
  }, [loading, loadingStartTime]);

  // Determine loader speed based on previous loading duration
  const getLoaderSpeed = () => {
    if (loadingDuration === 0) return 'normal'; // Default for first load
    if (loadingDuration < 500) return 'fast';
    if (loadingDuration > 2000) return 'slow';
    return 'normal';
  };

  console.log("ProfilePost - Rendering with loading:", loading, "posts length:", posts?.length || 0, "duration:", loadingDuration);

  return (
    <View style={{ position: 'relative', flex: 1 }}>
      {loading && (
        <View style={styles.loaderContainer}>
          <Loader isVisible={true} speed={getLoaderSpeed()} />
        </View>
      )}

      {!loading && Array.isArray(posts) && posts.length > 0 ? (
        <FlatList
          data={posts}
          keyExtractor={(item) => item._id}
          numColumns={3}
          columnWrapperStyle={styles.columnWrapper}
          renderItem={({ item }) => {
            const imageUri = getFirstImage(item);
            if (!imageUri) return null;

            // Get the filter for this post
            const filterObj = getFilterComponent(item.filter || 'No Filter');

            return (
              <TouchableOpacity
                activeOpacity={0.7}
                style={styles.itemContainer}
                onPress={() => handleImagePress(item)}
              >
                {filterObj.matrix ? (
                  <ColorMatrix matrix={filterObj.matrix}>
                    <Image
                      source={{ uri: imageUri }}
                      style={styles.image}
                      resizeMode="cover"
                    />
                  </ColorMatrix>
                ) : (
                  <Normal>
                    <Image
                      source={{ uri: imageUri }}
                      style={styles.image}
                      resizeMode="cover"
                    />
                  </Normal>
                )}
              </TouchableOpacity>
            );
          }}
        />
      ) : !loading && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Henüz gönderi yok</Text>
        </View>
      )}
    </View>
  );
};

export default ProfilePost

const styles = StyleSheet.create({
    columnWrapper: {
        justifyContent: "space-between",
        marginHorizontal: -2,
    },
    itemContainer: {
        flex: 1,
        margin: 1
    },
    image: {
        width: 120,
        height: 177,
        borderRadius: 4,
    },
    loaderContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        height: 'auto',
    },
    emptyContainer: {
        height: 300,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyText: {
        fontSize: 16,
        color: '#666',
    }
})