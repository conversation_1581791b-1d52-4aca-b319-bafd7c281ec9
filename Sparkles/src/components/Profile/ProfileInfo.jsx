import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {height, width} from '../../utils/helpers';
import SvgDot from '../../assets/dot';
import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import store from '../../redux/store';
import { useNavigation } from '@react-navigation/native';

const ProfileInfo = ({
  user,
  handleFollowToggle,
  isFollowingUser,
  isLoading,
}) => {
  console.log("profileinfo user", user);

  // Navigation hook
  const navigation = useNavigation();

  // Şu anki kullanıcıyı al
  const currentUser = useSelector(state => state.auth.user);

  useEffect(() => {
    // Kullanıcı bilgilerini ve ID karşılaştırmasını logla
    console.log("ProfileInfo - Kullanıcı Bilgileri:", {
      profileUserId: user?.id,
      profileUsername: user?.username,
      followerCount: user?.followersCount,
      followingCount: user?.followingCount,
      isFollowingUser: isFollowingUser,
      currentUserId: currentUser?.id || currentUser?._id
    });
  }, [user, currentUser, isFollowingUser]);

  // Kendi profili mi kontrolü
  const isSelfProfile = (currentUser?.id || currentUser?._id) === user?.id;

  // Navigation functions for followers and following
  const openFollowers = () => {
    console.log("Opening followers list for user:", user.id);
    navigation.navigate('FollowList', {
      activeTab: 'followers',
      userId: user.id
    });
  };

  const openFollowing = () => {
    console.log("Opening following list for user:", user.id);
    navigation.navigate('FollowList', {
      activeTab: 'following',
      userId: user.id
    });
  };

  // Takipçi ve takip edilen sayılarını formatlama
  const formatFollow = num => {
    // Sayı değilse veya geçersizse 0 döndür
    if (num === undefined || num === null || isNaN(Number(num))) {
      console.log("ProfileInfo - Geçersiz takipçi/takip sayısı:", num);
      return '0';
    }

    // String ise sayıya çevir
    const numValue = typeof num === 'string' ? parseInt(num, 10) : num;

    if (numValue >= 1_000_000) {
      const formatted = (numValue / 1_000_000).toFixed(1).replace('.', ',');
      return formatted.endsWith(',0')
        ? formatted.slice(0, -2) + ' M'
        : formatted + ' M';
    } else if (numValue >= 1_000) {
      const formatted = (numValue / 1_000).toFixed(1).replace('.', ',');
      return formatted.endsWith(',0')
        ? formatted.slice(0, -2) + ' B'
        : formatted + ' B';
    }
    return numValue.toString();
  };

  return (
    <>
      <View style={styles.container}>
        {/* Profil Resmi */}
        <View style={styles.profileImageContainer}>
          <Image
            source={{uri: user.profilePicture}}
            style={styles.userImage}
            onError={e =>
              console.log('Resim yüklenemedi:', e.nativeEvent.error)
            }
          />
        </View>

        {/* Kullanıcı Bilgileri */}
        <View style={styles.userInfo}>
          <Text style={styles.username}>
            {user.username || 'Kullanıcı Adı'}
          </Text>

          {/* Tam Ad (fullName) */}
          {user.fullName && (
            <Text style={styles.fullName}>
              {user.fullName}
            </Text>
          )}

          {/* Takipçi/Takip Bilgileri */}
          <View style={styles.follow}>
            <TouchableOpacity onPress={openFollowers} activeOpacity={0.7}>
              <Text style={styles.followCountText}>
                {formatFollow(user.followersCount || user.followerCount)} takipçi
              </Text>
            </TouchableOpacity>
            <SvgDot />
            <TouchableOpacity onPress={openFollowing} activeOpacity={0.7}>
              <Text style={styles.followCountText}>
                {formatFollow(user.followingCount)} takip
              </Text>
            </TouchableOpacity>
          </View>

          {/* Takip Butonu - Sadece başka kullanıcılar için göster */}
          {!isSelfProfile ? (
            <View style={styles.followBtn}>
              <TouchableOpacity
                style={[
                  styles.followButton,
                  isFollowingUser ? styles.unfollowButton : null,
                  isLoading ? styles.disabledButton : null,
                ]}
                onPress={() => {
                  console.log("Takip Butonu - Tıklanıldı:", {
                    action: isFollowingUser ? "takipten_çık" : "takip_et",
                    durumGörünümü: isFollowingUser ? "Takibi Bırak" : "Takip",
                    currentUserId: currentUser?.id || currentUser?._id,
                    profileUserId: user?.id,
                    timestamp: Date.now() // Add timestamp for debugging
                  });

                  // Call the parent component's handleFollowToggle function
                  // This will update the follow status in the parent component
                  handleFollowToggle();

                  // Force button to show loading state immediately
                  // This provides immediate visual feedback to the user
                  if (!isLoading) {
                    requestAnimationFrame(() => {
                      console.log("Forcing button to show loading state");
                    });
                  }
                }}
                disabled={isLoading}
                activeOpacity={0.7} // Add active opacity for better touch feedback
              >
                <Text
                  style={[
                    isFollowingUser ? styles.unfollowText : styles.followText,
                    isLoading ? styles.loadingText : null,
                  ]}>
                  {isFollowingUser ? 'Takibi Bırak' : 'Takip'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.dotContainer} activeOpacity={0.7}>
                <Text style={styles.dot}>...</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.followBtn}>
              <TouchableOpacity
                style={[styles.editProfileButton]}
                onPress={() => console.log("Profil düzenle butonuna tıklandı")}
              >
                <Text style={styles.editProfileText}>Profili Düzenle</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.dotContainer} activeOpacity={0.7}>
                <Text style={styles.dot}>...</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      {/* Kullanıcı Bio */}
      <Text style={styles.userBio}>{user.bio}</Text>
    </>
  );
};

export default ProfileInfo;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  userImage: {
    width: width * 0.26,
    height: width * 0.26,
    borderRadius: 76,
  },
  userInfo: {
    marginLeft: 20,
    gap: 8,
  },
  username: {
    fontSize: 20,
    fontWeight: '600',
  },
  fullName: {
    fontSize: 16,
    color: '#444444',
    fontWeight: '500',
  },
  follow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 7,
  },
  followCountText: {
    fontSize: 14,
    color: '#9D9C9C',
    fontWeight: '500',
  },
  followBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  followButton: {
    borderRadius: 5,
    backgroundColor: '#000',
    width: width * 0.45,
    height: height * 0.04,
    justifyContent: 'center',
    alignItems: 'center',
    display: 'flex',
  },
  followText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: height * 0.04,
  },
  unfollowButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#000',
  },
  unfollowText: {
    color: '#000',
    fontWeight: '500',
    fontSize: 14,
  },
  dotContainer: {
    borderWidth: 1,
    borderColor: ' #000000',
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 5,
  },
  dot: {
    fontSize: 14,
    fontWeight: '600',
    alignSelf: 'center',
  },
  userBio: {
    marginHorizontal: 20,
    lineHeight: 18,
    fontSize: 14,
    color: '#454545',
    marginTop: 20,
  },
  editProfileButton: {
    borderRadius: 5,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#000',
    width: width * 0.45,
    height: height * 0.04,
    justifyContent: 'center',
    alignItems: 'center',
    display: 'flex',
  },
  editProfileText: {
    color: '#000',
    fontWeight: '500',
    fontSize: 14,
  },
  disabledButton: {
    opacity: 0.6,
  },
  loadingText: {
    opacity: 0.7,
  },
});
