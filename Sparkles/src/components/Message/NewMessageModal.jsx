import {Animated,Easing,
  FlatList,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ActivityIndicator,
  Alert,
} from 'react-native';
import SvgLess from '../../assets/less';
import SvgSearchPeople from '../../assets/searchpeople';
import SvgPeople from '../../assets/people';
import {useEffect, useRef, useState} from 'react';
import {height, width} from '../../utils/helpers';
import { useDispatch, useSelector } from 'react-redux';
import { fetchFollowing } from '../../redux/slices/followSlice';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { startNewConversation, startNewGroupConversation } from '../../redux/actions/conversationsActions';
import { SCREENS } from '../../utils/router';
import React from 'react';

const NewMessageModal = ({modalVisible, setModalVisible, selectedUsers, setSelectedUsers}) => {
  const [searchText, setSearchText] = useState('');
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [startingConversation, setStartingConversation] = useState(false);
  // Show the selected count indicator when at least one user is selected
  const hasSelectedUsers = selectedUsers.length >= 1;

  const animatedHeight = useRef(new Animated.Value(height * 0.85)).current;
  const prevModalVisibleRef = useRef(false);

  const dispatch = useDispatch();
  const navigation = useNavigation();

  // Get user info from Redux store
  const { user } = useSelector(state => state.auth);
  const { userInfo } = useSelector(state => state.user);

  // Get the user ID from various possible sources
  const userId = user?._id || user?.id || userInfo?._id || userInfo?.id;

  // Get following list from Redux store
  const { following } = useSelector(state => state.follow);

  // Fetch following list when component mounts
  useFocusEffect(
    React.useCallback(() => {
      setIsLoading(true);

      // Reset following data first
      dispatch({ type: 'following/reset' });

      // Only fetch if we have a valid userId
      if (userId) {
        // Fetch following users
        dispatch(fetchFollowing(userId))
          .then(() => {
            setIsLoading(false);
          })
          .catch(error => {
            console.error("Error fetching following:", error);
            setIsLoading(false);
          });
      } else {
        console.error("Cannot fetch following: No valid user ID available");
        setIsLoading(false);
      }
    }, [dispatch, userId])
  );

  // Update filtered users when following list changes or search text changes
  useEffect(() => {
    if (!following || following.length === 0) {
      setFilteredUsers([]);
      return;
    }

    if (searchText.trim() === '') {
      setFilteredUsers(following);
    } else {
      const filtered = following.filter(user =>
        user.username?.toLowerCase().includes(searchText.toLowerCase()) ||
        user.fullName?.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [following, searchText]);

  // Update modal visibility ref
  useEffect(() => {
    console.log('Modal visibility changed:', modalVisible);
    console.log('Current selected users:', selectedUsers);

    // Update the ref with current modal state for next render
    prevModalVisibleRef.current = modalVisible;

    // Cleanup function
    return () => {
      // This will run when the component unmounts
      // No need to do anything special here for this use case
    };
  }, [modalVisible, selectedUsers]);
  // Handle keyboard appearance
  useEffect(() => {
    const keyboardShowListener = Keyboard.addListener("keyboardDidShow", (event) => {
      const modalHeight = event.endCoordinates.height;

      Animated.timing(animatedHeight, {
        toValue: height * 0.85 - modalHeight,
        duration: 100,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    const keyboardHideListener = Keyboard.addListener("keyboardDidHide", () => {
      Animated.timing(animatedHeight, {
        toValue: height * 0.85,
        duration: 50,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    });

    return () => {
      keyboardShowListener.remove();
      keyboardHideListener.remove();
    };
  }, []);

  const toggleUserSelection = userId => {
    setSelectedUsers(prevSelected => {
      console.log('Previous selected users:', prevSelected);

      const newSelected = prevSelected.includes(userId)
        ? prevSelected.filter(id => id !== userId)
        : [...prevSelected, userId];

      console.log('New selected users:', newSelected);
      console.log('Number of selected users:', newSelected.length);
      return newSelected;
    });
  };

  // Find selected user details from the filtered users list
  const findSelectedUserDetails = (userId) => {
    return filteredUsers.find(user => (user._id || user.id) === userId);
  };

  // Handle starting a new conversation
  const handleStartConversation = async () => {
    // Check if any users are selected
    if (selectedUsers.length === 0) {
      Alert.alert('Uyarı', 'Lütfen en az bir kişi seçin.');
      return;
    }

    setStartingConversation(true);

    try {
      if (selectedUsers.length === 1) {
        // Individual chat
        const recipientId = selectedUsers[0];
        const selectedUser = findSelectedUserDetails(recipientId);

        if (!selectedUser) {
          throw new Error('Seçilen kullanıcı bilgileri bulunamadı.');
        }

        // Start a new conversation with the selected user
        const result = await dispatch(startNewConversation({ recipientId })).unwrap();

        // Clear selected users when conversation is started successfully
        setSelectedUsers([]);

        // Close the modal
        setModalVisible(false);

        // Navigate to the chat screen with the conversation data
        navigation.navigate(SCREENS.CHATSCREEN, {
          messageId: result.data.conversation._id,
          conversationId: result.data.conversation._id,
          isApiConversation: true,
          recipientId: recipientId,
          userInfo: {
            userId: recipientId,
            userImage: { uri: selectedUser.profilePicture || null },
            name: selectedUser.fullName || selectedUser.username,
            username: selectedUser.username,
            followers: selectedUser.followerCount || 0,
            post: selectedUser.postCount || 0,
          }
        });
      } else {
        // Group chat
        // Make sure we have a clean array of participant IDs
        const participantIds = [...selectedUsers];

        console.log('Creating group chat with participants:', participantIds);
        console.log('Number of participants:', participantIds.length);

        // Get the names of selected users for the group name
        const selectedUserDetails = participantIds.map(userId => findSelectedUserDetails(userId));
        console.log('Selected user details:', selectedUserDetails);

        const groupName = selectedUserDetails
          .filter(user => user)
          .map(user => user.username)
          .slice(0, 3)
          .join(', ');

        console.log('Group name:', groupName);

        // Start a new group conversation using the dedicated group endpoint
        const result = await dispatch(
          startNewGroupConversation({
            name: groupName,
            participants: participantIds
          })
        ).unwrap();

        console.log('Group conversation created:', result);

        // Clear selected users when group conversation is started successfully
        setSelectedUsers([]);

        // Close the modal
        setModalVisible(false);

        // Navigate to the chat screen with the group conversation data
        // Make sure we're passing the populated participants data
        const populatedParticipants = result.data.group.participants || [];

        console.log('Group conversation created with participants:', {
          count: populatedParticipants.length,
          isPopulated: populatedParticipants.length > 0 && typeof populatedParticipants[0] === 'object',
          firstParticipant: populatedParticipants[0] ?
            (typeof populatedParticipants[0] === 'object' ?
              { id: populatedParticipants[0]._id, username: populatedParticipants[0].username } :
              populatedParticipants[0])
            : 'None'
        });

        navigation.navigate(SCREENS.CHATSCREEN, {
          messageId: result.data.group._id,
          conversationId: result.data.group._id,
          isApiConversation: true,
          isGroupChat: true,
          groupInfo: {
            _id: result.data.group._id, // Use _id to match the backend model
            name: groupName, // Use name to match the backend model
            participants: populatedParticipants.length > 0 ? populatedParticipants : participantIds
          }
        });
      }
    } catch (error) {
      console.error('Sohbet başlatma hatası:', error);
      console.error('Error details:', error.response?.data || error.message);
      Alert.alert(
        'Hata',
        error.message || 'Sohbet başlatılırken bir hata oluştu. Lütfen tekrar deneyin.'
      );
    } finally {
      setStartingConversation(false);
    }
  };
  return (
    <Modal
  animationType="none"
  transparent={true}
  visible={modalVisible}
  onRequestClose={() => setModalVisible(false)}>

  <KeyboardAvoidingView
    behavior={Platform.OS === "ios" ? "padding" : "height"}
    style={{ flex: 1 }}>

    <TouchableWithoutFeedback onPress={() => {
        Keyboard.dismiss();
        // Close the modal and clear selected users
        setSelectedUsers([]);
        setModalVisible(false);
      }}>
      <View style={styles.overlay}>
        <TouchableWithoutFeedback onPress={(e) => {
          // Prevent the click from bubbling up to the overlay
          e.stopPropagation();
        }}>
          <Animated.View style={[styles.modalContainer, { height: animatedHeight }]}>

            {/* Başlık ve Kapatma Butonu */}
            <View style={styles.share}>
              <Text style={styles.title}>Yeni Mesaj</Text>
              <TouchableOpacity
                onPress={() => {
                  // Close the modal and clear selected users
                  setSelectedUsers([]);
                  setModalVisible(false);
                }}
                activeOpacity={0.7}
                style={styles.closeButton}>
                <SvgLess style={styles.closeBtn} />
              </TouchableOpacity>
            </View>

          {/* Arama ve Seçili Kullanıcılar */}
          <View style={styles.scontainer}>
            <View
              style={[
                styles.searchContainer,
                hasSelectedUsers && { width: "75%" },
              ]}>
              <SvgSearchPeople style={styles.searchIcon}/>
              <TextInput
                style={styles.searchInput}
                placeholder="Kişi ara"
                placeholderTextColor="#BBBBBB"
                onChangeText={setSearchText}
                value={searchText}
                selectionColor='#D134AA'
              />
            </View>

            {hasSelectedUsers && (
              <View style={styles.selectedCountContainer}>
                <Text style={styles.selectedCountText}>{selectedUsers.length}</Text>
                <SvgPeople />
              </View>
            )}
          </View>

          {/* Kullanıcı Listesi */}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#D134AA" />
              <Text style={styles.loadingText}>Takip ettiğiniz kişiler yükleniyor...</Text>
            </View>
          ) : !userId ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                Kullanıcı bilgilerinize erişilemiyor. Lütfen tekrar giriş yapın.
              </Text>
            </View>
          ) : filteredUsers.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {following.length === 0
                  ? "Henüz kimseyi takip etmiyorsunuz."
                  : "Arama sonucu bulunamadı."}
              </Text>
            </View>
          ) : (
            <FlatList
              data={filteredUsers}
              keyExtractor={item => item._id || item.id || Math.random().toString()}
              numColumns={3}
              showsVerticalScrollIndicator={false}
              renderItem={({item}) => {
                const userId = item._id || item.id;
                const isSelected = selectedUsers.includes(userId);
                return (
                  <TouchableOpacity
                    style={styles.userItem}
                    onPress={() => toggleUserSelection(userId)}
                    activeOpacity={0.7}>

                    <View style={styles.userImageContainer}>
                      <View style={styles.userImageWrapper}>
                        <Image
                          source={
                            item.profilePicture
                              ? { uri: item.profilePicture }
                              : require('../../assets/profilePhoto.png')
                          }
                          style={styles.userImage}
                          onError={(e) => console.log('Image loading error:', e.nativeEvent.error)}
                        />
                      </View>
                      <View style={[
                        styles.borderOverlay,
                        isSelected && styles.selectedBorderOverlay
                      ]} />
                      {isSelected && <View style={styles.shadowOverlay} />}
                    </View>

                    <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
                  </TouchableOpacity>
                );
              }}
            />
          )}

          {/* Buton */}
          <TouchableOpacity
            style={[
              styles.button,
              (startingConversation || selectedUsers.length === 0) && styles.disabledButton
            ]}
            activeOpacity={0.7}
            onPress={handleStartConversation}
            disabled={startingConversation || selectedUsers.length === 0}
          >
            {startingConversation ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.buttonText}>
                {selectedUsers.length > 1
                  ? 'Grup Sohbetini Başlat'
                  : 'Sohbeti Başlat'}
              </Text>
            )}
          </TouchableOpacity>

          </Animated.View>
        </TouchableWithoutFeedback>
      </View>
    </TouchableWithoutFeedback>
  </KeyboardAvoidingView>
</Modal>
  );
};

export default NewMessageModal;

const styles = StyleSheet.create({
  selectedUserItem: {
    padding: 5,
  },
  userItem: {
    alignItems: 'center',
    justifyContent: "center",
    marginBottom: 15,
    width: width * 0.30,
  },
  userImageContainer: {
    width: 84,
    height: 84,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  userImageWrapper: {
    width: 78,
    height: 78,
    borderRadius: 39,
    overflow: 'hidden',
  },
  userImage: {
    width: '100%',
    height: '100%',
  },
  borderOverlay: {
    position: 'absolute',
    width: 84,
    height: 84,
    borderRadius: 42,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  selectedBorderOverlay: {
    borderColor: '#D134AA',
  },
  shadowOverlay: {
    position: 'absolute',
    width: 84,
    height: 84,
    borderRadius: 42,
    backgroundColor: '#D134AA33',
    shadowColor: '#D134AA',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 8,
  },
  username: {
    marginTop: 5,
    fontSize: 12,
    color: '#000',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  scontainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom:20,
    width: width * 0.90,
    height:38
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#000",
    paddingHorizontal: 10,
    height: 38,
    flex: 1,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: "#000",
    fontWeight:"500",
    textAlignVertical:"center"
  },
  selectedCountContainer: {
    backgroundColor: "#000",
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 10,
    paddingVertical: 5,
    height: 38,
    marginLeft: 10,
  },
  selectedCountText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 14,
    marginRight: 5,
  },

  button: {
    backgroundColor: '#000',
    padding: 15,
    borderRadius: 33,
    alignItems: 'center',
    marginTop: 10,
    width: width * 0.90,
    height:47
  },
  disabledButton: {
    backgroundColor: '#999',
    opacity: 0.7
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    alignItems:"center",
    justifyContent:"center"
  },
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 40,
    maxHeight: height * 0.83
  },
  share: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    marginBottom: 25,
    position: 'relative',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    right: 16,
  },
  closeBtn: {
    width: 24,
    height: 24,
  },
  // Loading state styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    minHeight: 200,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});


