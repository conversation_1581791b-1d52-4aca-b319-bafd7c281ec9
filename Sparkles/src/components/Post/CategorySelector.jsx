import { View, Text, TouchableOpacity, StyleSheet, FlatList } from "react-native";
import { useSelector, useDispatch } from "react-redux";
import { selectBrand, toggleCategory } from "../../redux/slices/categoriesSlice";
import { setProductType } from "../../redux/slices/productLinkSlice";
import { height, width } from "../../utils/helpers";

const CategorySelector = ({ categories, selectedCategories, dispatch }) => {
  const numColumns = 4;
  const numRows = Math.ceil(categories.length / numColumns);
  const columns = Array.from({ length: numColumns }, (_, colIndex) =>
    categories.filter((_, index) => index % numColumns === colIndex)
  );
  
  const { brands } = useSelector(state => state.category);

  const handleCategoryToggle = (category) => {
    // Dispatch toggle action with just the category name
    dispatch(toggleCategory(category));
    
    // If you need to associate brands with categories, you might want to:
    // 1. Find brands that belong to this category
    // 2. Dispatch selectBrand for those brands
    // Example:
    const relatedBrands = brands.filter(brand => brand.category === category);
    relatedBrands.forEach(brand => {
      dispatch(selectBrand({ id: brand.id }));
    });
  };

  return (
    <View style={styles.container}>
      {columns.map((column, colIndex) => (
        <View key={colIndex} style={styles.column}>
          {column.map((category, index) => (
            <TouchableOpacity
              activeOpacity={0.7}
              key={index}
              style={styles.categoryContainer}
              onPress={() => handleCategoryToggle(category)}
            >
              <View
                style={[
                  styles.checkbox,
                  selectedCategories.includes(category) && styles.checkedBox,
                ]}
              />
              <Text style={styles.text}>{category}</Text>
            </TouchableOpacity>
          ))}
        </View>
      ))}
    </View>
  );
};
      {/* <FlatList
      numColumns={4}
      data={brands}
      renderItem={({item})=>(
        <TouchableOpacity
        activeOpacity={0.7}
        style={styles.categoryContainer}
        onPress={() => handleCategoryToggle(item)}
      >
        <View
          style={[
            styles.checkbox,
            item.selected && styles.checkedBox,
          ]}
        />
        <Text style={styles.text}>{item.brand}</Text>
      </TouchableOpacity>
      )}
      /> */}


const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 15,
    marginHorizontal:23,
    paddingLeft:7
  },
  column: {
    width: "22%", 
  },
  categoryContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
    width:width/4
  },
  checkbox: {
    width: 12,
    height: 12,
    borderWidth: 1,
    borderColor: "black",
    marginRight: 5,
    borderRadius: 2,
  },
  checkedBox: {
    backgroundColor: "black",
  },
  text: {
    fontSize: 12,
    color: "#000000",
  },
});

export default CategorySelector;
