import {useEffect, useState} from 'react';
import {
  Keyboard,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import SvgSearch from '../../assets/search';
import {FlatList, TextInput} from 'react-native-gesture-handler';
import CategorySelector from './CategorySelector';
import CategoryDetail from './CategoryDetail';
import {
  selectCategories,
  selectCategoryData,
  selectSelectedCategories,
  toggleCategory,
  updateCategoryBrand,
  updateCategoryCollarType,
  updateCategoryColor,
  updateCategoryUrl,
} from '../../redux/slices/categoriesSlice';
import {
  setColor,
  setProductBrand,
  setProductType,
  setProductUrl,
  setYakaType,
} from '../../redux/slices/productLinkSlice';
import {useNavigation, useRoute} from '@react-navigation/native';
import {setSelectedBrands} from '../../redux/slices/brandSlice';
import SvgBack from '../../assets/back';
import SvgNext from '../../assets/nextSm';

const CategorySearchComponent = () => {
  const categories = useSelector(selectCategories);
  const categoryData = useSelector(selectCategoryData);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCategories, setFilteredCategories] = useState(categories);
  const dispatch = useDispatch();

  // Seçili kategorileri al (selected true olanlar)
  const selectedCategories = useSelector(selectSelectedCategories);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCategories(categories);
    } else {
      const filtered = categories.filter(category =>
        category.toLowerCase().includes(searchQuery.toLowerCase()),
      );
      setFilteredCategories(filtered);
    }
  }, [searchQuery, categories]);

  // Kategorileri arama ve seçili kategorilerle birleştir
  const combinedCategories = [
    ...selectedCategories,
    ...filteredCategories.filter(
      category => !selectedCategories.includes(category),
    ),
  ];

  const handleCategoryToggle = category => {
    dispatch(toggleCategory(category));
    // Kategori seçildiğinde productLinkSlice'taki type'ı güncelle
    dispatch(setProductType(category));
  };

  // Kategori detaylarını güncelleme fonksiyonları
  //   const handleBrandChange = (category, brand) => {
  //     dispatch(updateCategoryBrand({category, brand}));
  //     dispatch(setProductBrand(brand));
  //   };

  const handleColorChange = (category, color) => {
    dispatch(updateCategoryColor({category, color}));
    dispatch(setColor(color));
  };

  const handleCollarTypeChange = (category, collarType) => {
    dispatch(updateCategoryCollarType({category, collarType}));
    dispatch(setYakaType(collarType));
  };

  const handleUrlChange = (category, url) => {
    dispatch(updateCategoryUrl({category, url}));
    dispatch(setProductUrl(url));
  };
  const numColumns = 4;

  const navigation = useNavigation();

  return (
    <View>
      <View style={styles.searchContainer}>
        <View style={styles.iconContainer}>
          <SvgSearch style={styles.searchIcon} />
        </View>
        <TextInput
          placeholder="Kategori Ara"
          style={styles.searchInput}
          placeholderTextColor="#9D9C9C"
          selectionColor="#D134AA"
          value={searchQuery}
          onChangeText={text => setSearchQuery(text)}
        />
      </View>

      {/* Kategori seçim bileşeni */}
      <View style={styles.container}>
        {/* Önce sütunları oluşturuyoruz */}
        {Array.from({length: numColumns}).map((_, colIndex) => (
          <View key={`column-${colIndex}`} style={styles.column}>
            {/* Her sütun için kendi kategorilerini filtreliyoruz */}
            {combinedCategories
              .filter((_, index) => index % numColumns === colIndex)
              .map(category => (
                <TouchableOpacity
                  key={`category-${category}`}
                  style={[
                    styles.categoryContainer,
                    selectedCategories.includes(category) &&
                      styles.selectedCategory,
                  ]}
                  onPress={() => handleCategoryToggle(category)}>
                  <View
                    style={[
                      styles.checkbox,
                      selectedCategories.includes(category) &&
                        styles.checkedBox,
                    ]}
                  />
                  <Text style={styles.text}>{category}</Text>
                </TouchableOpacity>
              ))}
          </View>
        ))}
      </View>

      {/* Seçili kategorilerin detaylarını göster */}
      {selectedCategories.map(category => (
        <View key={category} style={styles.detailContainer}>
          <Text style={styles.header}>{category}</Text>

          {/*MARKA SEÇİMİ */}

          {/* Marka Seçimi */}
          <TouchableOpacity
            style={styles.selection}
            onPress={() =>
              navigation.navigate('BrandSearch', {
                category,
                currentBrand: categoryData.brand,
                onBrandSelect: selectedBrand => {
                  // Hem kategoriye özel markayı hem de ürün markasını güncelle
                  dispatch(
                    updateCategoryBrand({category, brand: selectedBrand}),
                  );
                  dispatch(setProductBrand(selectedBrand));
                },
              })
            }>
            <Text style={styles.detailText}>
              {categoryData.brand || 'Marka'}
            </Text>
            <SvgNext style={styles.button} />
          </TouchableOpacity>

          {/*RENK SEÇİMİ */}

          <TouchableOpacity
            style={styles.selection}
            onPress={() =>
              navigation.navigate('ColorPicker', {
                category,
                currentColor: categoryData.color,
                onColorSelect: selectedColor => {
                  // Hem kategoriye özel rengi hem de ürün rengini güncelle
                  dispatch(
                    updateCategoryColor({category, color: selectedColor}),
                  );
                  dispatch(setColor(selectedColor));
                },
              })
            }>
            <Text style={styles.detailText}>
              {categoryData.color || 'Renk'}
            </Text>
            <SvgNext style={styles.button} />
          </TouchableOpacity>

          {/* YAKA TİPİ  */}

          <TouchableOpacity
            style={styles.selection}
            onPress={() =>
              navigation.navigate('CollarType', {
                category,
                currentCollarType: categoryData.collarType,
                onCollarTypeSelect: selectedCollarType => {
                  // Update Redux state when a collar type is selected
                  dispatch(
                    updateCategoryCollarType({
                      category,
                      collarType: selectedCollarType,
                    }),
                  );
                  dispatch(setYakaType(selectedCollarType));
                  // Note: User will need to press back button to return
                },
              })
            }>
            <Text style={styles.detailText}>
              {categoryData.collarType || 'Yaka Tipi'}
            </Text>
            <SvgNext style={styles.button} />
          </TouchableOpacity>

          {/* LİNK */}
          <View style={styles.selection}>
            <TextInput
              placeholder="Ürün Linki"
              value={categoryData[category]?.url || ''}
              onChangeText={text => handleUrlChange(category, text)}
              style={styles.input}
              selectionColor="#D134AA"
            />
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = {
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 35,
    paddingHorizontal: 10,
    height: 40,
    width: 353,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 23,
    marginTop: 15,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 5,
  },
  searchIcon: {
    width: 20,
    height: 20,
  },
  searchInput: {
    flex: 1,
    fontSize: 13,
    paddingLeft: 5,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    marginHorizontal: 23,
    paddingLeft: 7,
  },
  column: {
    width: '22%',
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  checkbox: {
    width: 12,
    height: 12,
    borderWidth: 1,
    borderColor: 'black',
    marginRight: 5,
    borderRadius: 2,
  },
  checkedBox: {
    backgroundColor: 'black',
  },
  text: {
    fontSize: 12,
    color: '#000000',
  },

  detailContainer: {
    marginTop: 25,
    flex: 1,
  },
  header: {
    fontSize: 16,
    fontWeight: '500',
    marginHorizontal: 23,
    marginBottom: 10,
  },
  selectionContainer: {
    gap: 2,
  },
  selection: {
    backgroundColor: '#F0F0F0',
    height: 47,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailText: {
    marginHorizontal: 23,
    fontWeight: '300',
    fontSize: 14,
  },
  button: {
    marginHorizontal: 23,
  },
  input: {
    backgroundColor: 'white',
    height: 34,
    paddingHorizontal: 15,
    borderRadius: 20,
    fontSize: 14,
    color: '#000',
    marginHorizontal: 17, // Her iki yandan 23 birim boşluk
    flex: 1, // Kullanılabilir alanı tamamen doldurur
    justifyContent: 'center',
    includeFontPadding: false,
  },
};

export default CategorySearchComponent;
