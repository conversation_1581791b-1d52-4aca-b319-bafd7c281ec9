import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import SvgNext from '../../assets/nextSm';
import {TextInput} from 'react-native-gesture-handler';
import { useNavigation } from '@react-navigation/native';
import { setProductUrl } from '../../redux/slices/productLinkSlice';
import { useDispatch, useSelector } from 'react-redux';
import { updateCategoryProduct } from '../../redux/slices/categoriesSlice';

const CategoryDetail = ({ category }) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  
  // Kategoriye özel verileri al
  const categoryData = useSelector(state => 
    state.category.categoryData[category] || {}
  );

  return (
    <View style={styles.container}>
      <Text style={styles.header}>{category}</Text>
      <View style={styles.selectionContainer}>
        {/* Marka Seçimi */}
        <TouchableOpacity 
          style={styles.selection} 
          onPress={() => navigation.navigate('BrandSearch', { category })}
        >
          <Text style={styles.text}>
            {categoryData.brand || 'Marka Seçiniz'}
          </Text>
          <SvgNext style={styles.button} />
        </TouchableOpacity>

        {/* Renk Seçimi */}
        <TouchableOpacity 
          style={styles.selection} 
          onPress={() => navigation.navigate('ColorPicker', { category })}
        >
          <Text style={styles.text}>
            {categoryData.color || 'Renk Seçiniz'}
          </Text>
          <SvgNext style={styles.button} />
        </TouchableOpacity>

        {/* Yaka Tipi Seçimi */}
        <TouchableOpacity 
          style={styles.selection} 
          onPress={() => navigation.navigate('CollarType', { category })}
        >
          <Text style={styles.text}>
            {categoryData.collarType || 'Yaka Tipi Seçiniz'}
          </Text>
          <SvgNext style={styles.button} />
        </TouchableOpacity>

        {/* URL Girişi */}
        <View style={styles.selection}>
          <TextInput
            placeholder="Ürün Linki"
            value={categoryData.url || ''}
            onChangeText={(text) => 
              dispatch(updateCategoryUrl({ category, url: text }))
            }
            style={styles.input}
          />
        </View>
      </View>
    </View>
  );
};

export default CategoryDetail;

const styles = StyleSheet.create({
  container: {
    marginTop: 25,
    flex:1
  },
  header: {
    fontSize: 16,
    fontWeight: '500',
    marginHorizontal: 23,
    marginBottom: 10,
  },
  selectionContainer: {
    gap: 2,
  },
  selection: {
    backgroundColor: '#F0F0F0',
    height: 47,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  text: {
    marginHorizontal: 23,
    fontWeight: '300',
    fontSize: 14,
  },
  button: {
    marginHorizontal: 23,
  },
  input: {
    backgroundColor: 'white',
    height:34,
    paddingHorizontal: 15,
    borderRadius: 20,
    fontSize: 14,
    color: '#000',
    marginHorizontal: 17, // Her iki yandan 23 birim boşluk
    flex: 1, // Kullanılabilir alanı tamamen doldurur
    justifyContent: 'center',
    includeFontPadding: false,
  }
});
