import {Image, StyleSheet, Text, View, ActivityIndicator, TouchableOpacity} from 'react-native';
import {height, width} from '../../utils/helpers';
import { useEffect, useState } from 'react';
import api from '../../api/api';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';

const UserInfo = ({userInfo, groupInfo, isGroupChat}) => {
  console.log("userinfo", userInfo);
  console.log("groupinfo", groupInfo);
  console.log("isGroupChat", isGroupChat);

  const navigation = useNavigation();
  const [userData, setUserData] = useState(null);
  const [groupParticipants, setGroupParticipants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Get current user from Redux store
  const currentUser = useSelector(state => state.auth.user);

  // Log current user info for debugging
  useEffect(() => {
    if (currentUser) {
      console.log('Current logged-in user:', {
        id: currentUser._id,
        username: currentUser.username,
        hasId: !!currentUser._id,
        hasUsername: !!currentUser.username
      });
    }
  }, [currentUser]);

  // Helper function to format participant names with commas and "and" before the last name
  const formatParticipantNames = (names) => {
    if (!names || names.length === 0) return '';
    if (names.length === 1) return names[0];

    // Filter out the current user's name if present
    const filteredNames = names.filter(name => {
      // Check if this name belongs to the current user
      // This is a simplified check - you may need to adjust based on your data structure
      if (currentUser && currentUser.username) {
        return !name.includes(currentUser.username);
      }
      return true;
    });

    if (filteredNames.length === 0) return names[0]; // Return original if all were filtered
    if (filteredNames.length === 1) return filteredNames[0];

    // For multiple names, format with commas and "and" before the last name
    const lastItem = filteredNames.pop();
    return `${filteredNames.join(', ')} ve ${lastItem}`;
  };

  // Helper function to get initials from a user's full name
  const getInitials = (user) => {
    if (!user) return '';

    // Try to use fullName first, then name, then username
    const fullName = user.fullName || user.name || user.username || '';

    // Split the name by spaces and get the first letter of each part
    const nameParts = fullName.split(' ');

    if (nameParts.length >= 2) {
      // If there are at least two parts, use the first letter of first and last parts
      return `${nameParts[0].charAt(0)}${nameParts[nameParts.length - 1].charAt(0)}`.toUpperCase();
    } else if (nameParts.length === 1 && nameParts[0]) {
      // If there's only one part, use the first two letters or just the first if it's a single character
      return nameParts[0].length > 1
        ? `${nameParts[0].charAt(0)}${nameParts[0].charAt(1)}`.toUpperCase()
        : `${nameParts[0].charAt(0)}`.toUpperCase();
    }

    // Fallback to username if available
    if (user.username) {
      return user.username.substring(0, 2).toUpperCase();
    }

    return 'U'; // Default fallback
  };

  const handleProfilePress = () => {
    // Navigate to SearchProfile screen with the user data
    if (!isGroupChat) {
      navigation.navigate('SearchProfile', { user: userData || userInfo });
    }
  };

  useEffect(() => {
    console.log('UserInfo component mounted/updated:', {
      isGroupChat: !!isGroupChat,
      hasUserInfo: !!userInfo,
      hasGroupInfo: !!groupInfo,
      userId: userInfo?.userId,
      participantsCount: groupInfo?.participants?.length || 0,
      groupId: groupInfo?._id || groupInfo?.groupId,
      groupName: groupInfo?.name || groupInfo?.groupName
    });

    // For individual chats, fetch user data
    if (!isGroupChat && userInfo?.userId) {
      console.log('Fetching individual user data for:', userInfo.userId);
      fetchUserData(userInfo.userId);
    }

    // For group chats, fetch participants data
    if (isGroupChat && groupInfo?.participants && groupInfo.participants.length > 0) {
      console.log('Fetching group participants data:', {
        groupId: groupInfo._id || groupInfo.groupId,
        participantIds: groupInfo.participants,
        participantsCount: groupInfo.participants.length,
        firstParticipantType: typeof groupInfo.participants[0]
      });

      // Check if participants are already populated objects or just IDs
      const firstParticipant = groupInfo.participants[0];
      const areParticipantsPopulated = typeof firstParticipant === 'object' && firstParticipant !== null;

      if (areParticipantsPopulated) {
        console.log('Participants are already populated, using existing data:',
          groupInfo.participants.map(p => p.username || p._id));
        setGroupParticipants(groupInfo.participants);
      } else {
        console.log('Participants are IDs, fetching user data for each:', groupInfo.participants);
        fetchGroupParticipants(groupInfo.participants);
      }
    } else if (isGroupChat && (!groupInfo?.participants || groupInfo.participants.length === 0)) {
      console.log('Group chat without participants or empty participants array. Attempting to fetch group data.');

      // If we have a group ID but no participants, try to fetch the group data
      const groupId = groupInfo?._id || groupInfo?.groupId;
      if (groupId) {
        console.log('Fetching group data for ID:', groupId);

        // First try to get group messages, which should include participant information
        api.get(`messages/groups/${groupId}/messages`)
          .then(response => {
            console.log('Fetched group messages in UserInfo:', response.data);

            // Check if we have conversation data with participants
            if (response.data?.data?.conversation?.participants) {
              const participants = response.data.data.conversation.participants;
              console.log('Got participants from messages API:', participants.length);

              // Check if these are populated objects or just IDs
              if (participants.length > 0 && typeof participants[0] === 'object' && participants[0] !== null) {
                console.log('API returned populated participants');
                setGroupParticipants(participants);
              } else {
                console.log('API returned participant IDs, fetching user data');
                fetchGroupParticipants(participants);
              }
            } else {
              // Fallback to the direct group endpoint (which might not exist yet)
              console.log('No participants found in messages response, trying direct group endpoint');
              fetchDirectGroupData(groupId);
            }
          })
          .catch(error => {
            console.error('Error fetching group messages in UserInfo:', error);
            // Fallback to the direct group endpoint
            fetchDirectGroupData(groupId);
          });
      }
    }
  }, [userInfo?.userId, isGroupChat, groupInfo]);

  // Helper function to fetch group data directly
  const fetchDirectGroupData = (groupId) => {
    api.get(`messages/groups/${groupId}`)
      .then(response => {
        console.log('Fetched group data in UserInfo:', response.data);
        if (response.data?.data?.group?.participants) {
          const participants = response.data.data.group.participants;
          console.log('Got participants from API:', participants.length);

          // Check if these are populated objects or just IDs
          if (participants.length > 0 && typeof participants[0] === 'object' && participants[0] !== null) {
            console.log('API returned populated participants');
            setGroupParticipants(participants);
          } else {
            console.log('API returned participant IDs, fetching user data');
            fetchGroupParticipants(participants);
          }
        }
      })
      .catch(error => {
        console.error('Error fetching group data in UserInfo:', error);

        // If we have participants in groupInfo, use those as a fallback
        if (groupInfo?.participants && groupInfo.participants.length > 0) {
          console.log('Using existing participants from groupInfo as fallback');
          fetchGroupParticipants(groupInfo.participants);
        } else {
          setError("Failed to load group data");
        }
      });
  };

  const fetchUserData = async (userId) => {
    try {
      setLoading(true);
      setError(null);

      // Fetch the latest user data from the API
      const response = await api.get(`users/${userId}`);
      console.log("Fetched user data:", response.data);

      if (response.data && response.data.data && response.data.data.user) {
        const user = response.data.data.user;
        const stats = response.data.data.stats || {};

        // Update the user data with the latest information
        setUserData({
          ...user,
          followersCount: stats.followersCount || 0,
          postsCount: stats.postsCount || 0
        });
      }
    } catch (err) {
      console.error("Error fetching user data:", err);
      setError("Failed to load user data");
    } finally {
      setLoading(false);
    }
  };

  const fetchGroupParticipants = async (participantIds) => {
    try {
      console.log('Starting to fetch group participants:', {
        count: participantIds.length,
        ids: participantIds
      });

      setLoading(true);
      setError(null);

      // Create an array to store participant data
      const participants = [];

      // Check if participantIds is an array of strings (IDs) or objects
      const isIdArray = participantIds.length > 0 && typeof participantIds[0] === 'string';

      if (isIdArray) {
        // Fetch data for each participant ID
        for (const participantId of participantIds) {
          try {
            console.log(`Fetching participant data for ID: ${participantId}`);
            const response = await api.get(`users/${participantId}`);

            if (response.data && response.data.data && response.data.data.user) {
              const user = response.data.data.user;
              console.log(`Successfully fetched participant: ${user.username || 'Unknown'}`);
              participants.push(user);
            } else {
              console.warn(`Received invalid user data for participant ${participantId}:`, response.data);
            }
          } catch (err) {
            console.error(`Error fetching participant ${participantId}:`, err);
          }
        }
      } else {
        // If participantIds is already an array of user objects, use them directly
        console.log('Using provided participant objects directly');
        participantIds.forEach(participant => {
          if (participant && participant._id) {
            participants.push(participant);
          }
        });
      }

      console.log("Processed group participants:", {
        count: participants.length,
        usernames: participants.map(p => p.username),
        firstParticipant: participants[0] ? {
          id: participants[0]._id,
          username: participants[0].username,
          hasProfilePic: !!participants[0].profilePicture
        } : 'None'
      });

      setGroupParticipants(participants);
    } catch (err) {
      console.error("Error fetching group participants:", err);
      setError("Failed to load group participants");
    } finally {
      setLoading(false);
    }
  };

  // For individual chats
  const displayName = userData?.fullName || userInfo?.name;
  const displayUsername = userData?.username || userInfo?.username;
  const followerCount = userData?.followersCount !== undefined ? userData.followersCount : userInfo?.followers;
  const postCount = userData?.postsCount !== undefined ? userData.postsCount : userInfo?.post;

  // Render group chat info
  if (isGroupChat && groupInfo) {
    // Get all participants for display
    const allParticipants = [...groupParticipants];

    // Filter out the current logged-in user from the participants list for display
    const filteredParticipants = allParticipants.filter(participant => {
      if (!participant || !currentUser) return true;

      // Check if this participant is the current user
      // Convert IDs to strings for comparison to handle both ObjectId and string formats
      const participantId = participant._id?.toString() || participant.id?.toString();
      const currentUserId = currentUser._id?.toString() || currentUser.id?.toString();

      // Also check username as a fallback if IDs don't match or are missing
      const isCurrentUser =
        (participantId && currentUserId && participantId === currentUserId) ||
        (participant.username && currentUser.username && participant.username === currentUser.username);

      // Log for debugging
      if (isCurrentUser) {
        console.log('Filtering out current user from group participants:', {
          participantId,
          participantUsername: participant.username,
          currentUserId,
          currentUsername: currentUser.username
        });
      }

      return !isCurrentUser;
    });

    console.log('Group participants after filtering:', {
      originalCount: allParticipants.length,
      filteredCount: filteredParticipants.length,
      filteredUsernames: filteredParticipants.map(p => p.username)
    });

    // Create a formatted string of usernames with @ symbol
    const participantUsernames = formatParticipantNames(filteredParticipants.map(p => `@${p?.username || 'user'}`));

    // We're not displaying follower and post counts for groups anymore

    return (
      <View style={styles.userInfoContainer}>
        {/* Participant profile images */}
        <View style={styles.participantsImagesContainer}>
          {filteredParticipants.length > 0 ? (
            <View style={styles.participantsRow}>
              {filteredParticipants.map((participant, index) => (
                <View
                  key={participant._id || index}
                  style={[
                    styles.participantImageContainer,
                    { zIndex: filteredParticipants.length - index }
                  ]}
                >
                  <Image
                    source={participant.profilePicture
                      ? { uri: participant.profilePicture }
                      : require('../../assets/profilePhoto.png')}
                    style={styles.participantProfileImage}
                  />
                </View>
              ))}
            </View>
          ) : loading ? (
            <ActivityIndicator size="small" color="#D134AA" />
          ) : (
            <View style={styles.groupImageContainer}>
              <Text style={styles.groupImageText}>
                {groupInfo.groupName?.substring(0, 2).toUpperCase() || "GP"}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.info}>
          {/* Group name */}
          <Text style={styles.name}>
            {filteredParticipants.length > 0
              ? formatParticipantNames(filteredParticipants.map(p => getInitials(p)))
              : "Group Chat"}
          </Text>

          {/* Usernames with @ symbol */}
          <Text style={styles.username}>{participantUsernames}</Text>

          {/* Followers and posts count */}
          {/* <View style={styles.status}>
            <Text style={styles.stat}>{totalFollowers} takipçi</Text>
            <View style={styles.dot} />
            <Text style={styles.stat}>{totalPosts} gönderi</Text>
          </View> */}

          {error && <Text style={styles.errorText}>{error}</Text>}
        </View>
      </View>
    );
  }

  // Render individual chat info
  return (
    <View style={styles.userInfoContainer}>
      <TouchableOpacity onPress={handleProfilePress} activeOpacity={0.7}>
        <Image
          source={userData?.profilePicture ? { uri: userData.profilePicture } : userInfo?.userImage}
          style={styles.userImage}
        />
      </TouchableOpacity>
      <View style={styles.info}>
        <TouchableOpacity onPress={handleProfilePress} activeOpacity={0.7}>
          <Text style={styles.name}>{displayName}</Text>
          <Text style={styles.username}>@{displayUsername}</Text>
        </TouchableOpacity>
        <View style={styles.status}>
          <>
            <Text style={styles.stat}>{followerCount} takipçi</Text>
            <View style={styles.dot} />
            <Text style={styles.stat}>{postCount} gönderi</Text>
          </>
        </View>
        {error && <Text style={styles.errorText}>{error}</Text>}
      </View>
    </View>
  );
};

export default UserInfo;

const styles = StyleSheet.create({
  userInfoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
    marginTop: 25,
  },
  userImage: {
    width: width * 0.25,
    height: height * 0.12,
    borderRadius: 55,
  },
  info:{
    alignItems:"center"
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  username: {
    fontSize: 14,
    fontWeight: '500',
    color: '#9D9C9C',
    marginTop: 2,
    marginBottom: 2,
  },
  status: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    marginTop: 5,
  },
  dot: {
    width: 3,
    height: 3,
    backgroundColor: '#D134AA',
  },
  stat:{
    fontSize: 14,
    fontWeight: "500",
    color: "#9D9C9C"
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
  },
  // Group chat styles
  groupImageContainer: {
    width: width * 0.25,
    height: height * 0.12,
    borderRadius: 55,
    backgroundColor: "#D134AA",
    justifyContent: "center",
    alignItems: "center",
  },
  groupImageText: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "600",
  },
  // New participant images styles
  participantsImagesContainer: {
    width: width * 0.6,
    height: height * 0.12,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  participantsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  participantImageContainer: {
    marginHorizontal: -10, // Negative margin for overlapping effect
    borderRadius: width * 0.09,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  participantProfileImage: {
    width: width * 0.18,
    height: width * 0.18,
    borderRadius: width * 0.09,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  // Legacy overlapping profile images styles (keeping for backward compatibility)
  overlappingImagesContainer: {
    width: width * 0.35,
    height: height * 0.12,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  firstImageContainer: {
    position: 'absolute',
    left: 10,
    zIndex: 1,
  },
  secondImageContainer: {
    position: 'absolute',
    right: 10,
    zIndex: 0,
  },
  initialsContainer: {
    alignItems: 'center',
    marginTop: 5,
  },
  initialsText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#D134AA',
  },
  // Legacy group chat styles (keeping for backward compatibility)
  participantsContainer: {
    marginTop: 10,
    width: width * 0.8,
    alignItems: "center",
  },
  participantItem: {
    alignItems: "center",
    marginHorizontal: 5,
  },
  participantImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  participantName: {
    fontSize: 12,
    color: "#9D9C9C",
    marginTop: 3,
  },
  moreParticipants: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#F1F1F1",
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 5,
  },
  moreParticipantsText: {
    fontSize: 12,
    color: "#9D9C9C",
  }
});
