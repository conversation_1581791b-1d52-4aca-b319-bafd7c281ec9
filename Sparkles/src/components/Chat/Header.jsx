import { Image, StatusBar, StyleSheet, Text, TouchableOpacity, View, Platform } from 'react-native'
import SvgBack from "../../assets/back"
import { useNavigation } from '@react-navigation/native'
import { SCREENS } from '../../utils/router'

const Header = ({userInfo, groupInfo, isGroupChat}) => {
    const navigation = useNavigation()

    const handleProfilePress = () => {
      if (isGroupChat && groupInfo) {
        // For group chats, we could navigate to a group info screen in the future
        // For now, just log the group info
        console.log("Group info:", groupInfo)
      } else if (userInfo) {
        navigation.navigate(SCREENS.SEARCHPROFILE, { user: userInfo })
      }
    }

  return (
    <View style={styles.container}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <SvgBack/>
        </TouchableOpacity>

        {isGroupChat && groupInfo ? (
          // Group chat header
          <View style={styles.groupInfoContainer}>
            <TouchableOpacity onPress={handleProfilePress}>
              <View style={styles.groupImageContainer}>
                <Text style={styles.groupImageText}>
                  {groupInfo.groupName?.substring(0, 2).toUpperCase() || "GP"}
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleProfilePress}>
              <Text style={styles.username}>{groupInfo.groupName || "Group Chat"}</Text>
            </TouchableOpacity>
          </View>
        ) : (
          // Individual chat header
          <>
            <TouchableOpacity onPress={handleProfilePress}>
              <Image source={userInfo?.userImage} style={styles.userImage} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleProfilePress}>
              <Text style={styles.username}>{userInfo?.username}</Text>
            </TouchableOpacity>
          </>
        )}
    </View>
  )
}

export default Header

const styles = StyleSheet.create({
    container:{
        flexDirection:"row",
        gap:12,
        alignItems:"center",
        marginLeft:20,
        paddingBottom:10,
        marginTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    },
    userImage:{
        width:37,
        height:37,
        borderRadius:18.5
    },
    username:{
        fontSize:14,
        fontWeight:"600"
    },
    groupInfoContainer: {
        flexDirection: "row",
        alignItems: "center",
        gap: 12,
    },
    groupImageContainer: {
        width: 37,
        height: 37,
        borderRadius: 18.5,
        backgroundColor: "#D134AA",
        justifyContent: "center",
        alignItems: "center",
    },
    groupImageText: {
        color: "#FFFFFF",
        fontSize: 14,
        fontWeight: "600",
    }
})