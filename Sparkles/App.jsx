import { Provider } from "react-redux";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { LogBox } from "react-native";
import { useEffect } from "react";
import store from "./src/redux/store";
import AppNavigator from "./src/router/AppNavigator";
import setupFonts from "./src/utils/fontSetup";

// Set up global font settings
setupFonts();

LogBox.ignoreAllLogs();

const App = () => {
  return (
    <Provider store={store}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <AppNavigator />
      </GestureHandlerRootView>
    </Provider>
  );
};
export default App;
