// Test to verify main comments are sorted from oldest to newest (top to bottom)

// Mock the formatComments function from CommentModal.jsx
const mockFormatComments = (comments, currentUserId) => {
  const formatSingleComment = (comment) => ({
    id: comment._id || comment.id,
    _id: comment._id || comment.id,
    user: comment.user?.username || 'Unknown',
    text: comment.text,
    avatar: comment.user?.profilePicture || 'https://default-profile.jpg',
    likes: comment.likes || [],
    liked: comment.likes?.some(like => like.user === currentUserId) || false,
    parent: comment.parent,
    createdAt: comment.createdAt,
    replies: []
  });

  // Create a map to store all comments by their ID
  const commentMap = new Map();

  // First pass: format all comments and store them in the map
  comments.forEach(comment => {
    const formattedComment = formatSingleComment(comment);
    commentMap.set(formattedComment.id, formattedComment);
  });

  // Second pass: build the nested structure
  const rootComments = [];

  commentMap.forEach(comment => {
    if (comment.parent) {
      // This is a reply, find its parent and add it to the parent's replies
      const parentComment = commentMap.get(comment.parent);
      if (parentComment) {
        parentComment.replies.push(comment);
      }
    } else {
      // This is a root comment
      rootComments.push(comment);
    }
  });

  // Sort replies within each comment by creation time (oldest first)
  const sortReplies = (comment) => {
    if (comment.replies && comment.replies.length > 0) {
      comment.replies.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateA - dateB; // Oldest first for replies
      });

      // Recursively sort sub-replies
      comment.replies.forEach(reply => sortReplies(reply));
    }
  };

  // Sort root comments by creation time (oldest first) and sort their replies
  rootComments.sort((a, b) => {
    const dateA = new Date(a.createdAt || 0);
    const dateB = new Date(b.createdAt || 0);
    return dateA - dateB; // Oldest first for root comments
  });

  // Sort all replies
  rootComments.forEach(comment => sortReplies(comment));

  return rootComments;
};

describe('Main Comment Sorting', () => {
  test('should sort main comments from oldest to newest (top to bottom)', () => {
    const currentUserId = 'user123';
    
    // Create mock comments with different timestamps
    const mockComments = [
      {
        _id: 'comment3',
        text: 'Third comment (newest)',
        user: { username: 'user3', profilePicture: 'pic3.jpg' },
        createdAt: '2023-06-21T12:00:00.000Z', // Newest
        parent: null,
        likes: []
      },
      {
        _id: 'comment1',
        text: 'First comment (oldest)',
        user: { username: 'user1', profilePicture: 'pic1.jpg' },
        createdAt: '2023-06-21T10:00:00.000Z', // Oldest
        parent: null,
        likes: []
      },
      {
        _id: 'comment2',
        text: 'Second comment (middle)',
        user: { username: 'user2', profilePicture: 'pic2.jpg' },
        createdAt: '2023-06-21T11:00:00.000Z', // Middle
        parent: null,
        likes: []
      }
    ];

    const formattedComments = mockFormatComments(mockComments, currentUserId);

    // Should have three main comments
    expect(formattedComments).toHaveLength(3);

    // Main comments should be sorted from oldest to newest
    expect(formattedComments[0].text).toBe('First comment (oldest)');
    expect(formattedComments[1].text).toBe('Second comment (middle)');
    expect(formattedComments[2].text).toBe('Third comment (newest)');

    // Verify the timestamps are in ascending order
    const comment1Time = new Date(formattedComments[0].createdAt).getTime();
    const comment2Time = new Date(formattedComments[1].createdAt).getTime();
    const comment3Time = new Date(formattedComments[2].createdAt).getTime();

    expect(comment1Time).toBeLessThan(comment2Time);
    expect(comment2Time).toBeLessThan(comment3Time);
  });

  test('should handle mixed main comments and replies correctly', () => {
    const currentUserId = 'user123';
    
    // Create mock comments with main comments and replies
    const mockComments = [
      {
        _id: 'comment2',
        text: 'Second main comment',
        user: { username: 'user2', profilePicture: 'pic2.jpg' },
        createdAt: '2023-06-21T11:00:00.000Z',
        parent: null,
        likes: []
      },
      {
        _id: 'reply1',
        text: 'Reply to first comment',
        user: { username: 'user3', profilePicture: 'pic3.jpg' },
        createdAt: '2023-06-21T10:30:00.000Z',
        parent: 'comment1',
        likes: []
      },
      {
        _id: 'comment1',
        text: 'First main comment',
        user: { username: 'user1', profilePicture: 'pic1.jpg' },
        createdAt: '2023-06-21T10:00:00.000Z',
        parent: null,
        likes: []
      }
    ];

    const formattedComments = mockFormatComments(mockComments, currentUserId);

    // Should have two main comments
    expect(formattedComments).toHaveLength(2);

    // Main comments should be sorted from oldest to newest
    expect(formattedComments[0].text).toBe('First main comment');
    expect(formattedComments[1].text).toBe('Second main comment');

    // First comment should have one reply
    expect(formattedComments[0].replies).toHaveLength(1);
    expect(formattedComments[0].replies[0].text).toBe('Reply to first comment');
  });
});
