/**
 * Profile Update Test
 *
 * This test verifies that profile updates (especially profile pictures)
 * are properly reflected across all areas of the application without
 * requiring an app refresh.
 */

// Mock AsyncStorage before importing anything else
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  multiRemove: jest.fn(),
  getAllKeys: jest.fn(() => Promise.resolve([])),
}));

// Mock react-native-config
jest.mock('react-native-config', () => ({
  BASE_URL: 'http://localhost:3000',
  LOGIN_URL: 'auth/login',
  USER_INFO_URL: 'auth/me',
}));

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    defaults: { headers: { common: {} } },
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  })),
}));

import { configureStore } from '@reduxjs/toolkit';
import userReducer from '../src/redux/slices/userSlice';
import { profileUpdate } from '../src/redux/actions/userActions';

// Mock the API and image cache functions
jest.mock('../src/api/api', () => ({
  clearUserProfileCache: jest.fn(),
  clearApiCache: jest.fn(),
  default: {
    defaults: { headers: { common: {} } },
    interceptors: {
      request: { use: jest.fn() },
      response: { use: jest.fn() },
    },
  },
}));

jest.mock('../src/utils/imagePrefetch', () => ({
  clearProfilePictureCache: jest.fn(),
}));

// Mock the API request
jest.mock('../src/api/verbs', () => ({
  patchRequest: jest.fn(),
}));

// Mock the clearUserData utility
jest.mock('../src/utils/clearUserData', () => ({
  clearAllUserData: jest.fn(),
}));

describe('Profile Update Functionality', () => {
  let store;

  beforeEach(() => {
    // Create a simple store with just the user reducer
    store = configureStore({
      reducer: {
        user: userReducer,
      },
      preloadedState: {
        user: {
          userInfo: {
            id: 'user123',
            username: 'testuser',
            fullName: 'Test User',
            profilePicture: 'https://example.com/old-profile.jpg',
            bio: 'Old bio',
          },
          isLoading: false,
          error: null,
        },
      },
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  it('should update profile data in user slice', async () => {
    const { patchRequest } = require('../src/api/verbs');
    const { clearUserProfileCache } = require('../src/api/api');

    // Mock successful API response
    const mockUpdatedUser = {
      id: 'user123',
      username: 'testuser',
      fullName: 'Updated User',
      profilePicture: 'https://example.com/new-profile.jpg',
      bio: 'Updated bio',
    };

    patchRequest.mockResolvedValue({
      data: {
        data: {
          user: mockUpdatedUser,
        },
      },
    });

    // Create form data for profile update
    const formData = new FormData();
    formData.append('fullName', 'Updated User');
    formData.append('bio', 'Updated bio');
    formData.append('profilePicture', {
      uri: 'file://new-profile.jpg',
      type: 'image/jpeg',
      name: 'profile.jpg',
    });

    // Dispatch profile update action
    const result = await store.dispatch(profileUpdate(formData));

    // Verify the action was fulfilled
    expect(result.type).toBe('user/profileUpdate/fulfilled');
    expect(result.payload).toEqual(mockUpdatedUser);

    // Get updated state
    const state = store.getState();

    // Verify user slice was updated
    expect(state.user.userInfo).toEqual(mockUpdatedUser);
    expect(state.user.isLoading).toBe(false);

    // Verify cache was cleared
    expect(clearUserProfileCache).toHaveBeenCalledWith('user123');
  });

  it('should clear API cache when profile is updated', async () => {
    const { patchRequest } = require('../src/api/verbs');
    const { clearUserProfileCache } = require('../src/api/api');

    const mockUpdatedUser = {
      id: 'user123',
      username: 'testuser',
      fullName: 'Updated User',
      profilePicture: 'https://example.com/new-profile.jpg',
      bio: 'Updated bio',
    };

    patchRequest.mockResolvedValue({
      data: {
        data: {
          user: mockUpdatedUser,
        },
      },
    });

    const formData = new FormData();
    formData.append('fullName', 'Updated User');

    await store.dispatch(profileUpdate(formData));

    // Verify that API cache was cleared
    expect(clearUserProfileCache).toHaveBeenCalledWith('user123');
  });

  it('should clear image cache when profile picture is updated', async () => {
    const { patchRequest } = require('../src/api/verbs');
    const { clearProfilePictureCache } = require('../src/utils/imagePrefetch');

    const mockUpdatedUser = {
      id: 'user123',
      username: 'testuser',
      fullName: 'Test User',
      profilePicture: 'https://example.com/new-profile.jpg',
      bio: 'Old bio',
    };

    patchRequest.mockResolvedValue({
      data: {
        data: {
          user: mockUpdatedUser,
        },
      },
    });

    const formData = new FormData();
    formData.append('profilePicture', {
      uri: 'file://new-profile.jpg',
      type: 'image/jpeg',
      name: 'profile.jpg',
    });

    await store.dispatch(profileUpdate(formData));

    // Verify that image cache was cleared for both old and new profile pictures
    expect(clearProfilePictureCache).toHaveBeenCalledWith('https://example.com/old-profile.jpg');
    expect(clearProfilePictureCache).toHaveBeenCalledWith('https://example.com/new-profile.jpg');
  });

  it('should handle profile update errors gracefully', async () => {
    const { patchRequest } = require('../src/api/verbs');

    // Mock API error
    patchRequest.mockRejectedValue(new Error('Network error'));

    const formData = new FormData();
    formData.append('fullName', 'Updated User');

    const result = await store.dispatch(profileUpdate(formData));

    // Verify the action was rejected
    expect(result.type).toBe('user/profileUpdate/rejected');
    expect(result.payload).toBe('Network error');

    // Verify state wasn't updated
    const state = store.getState();
    expect(state.user.userInfo.fullName).toBe('Test User'); // Original value
    expect(state.user.error).toBe('Network error');
    expect(state.user.isLoading).toBe(false);
  });

  it('should not clear image cache if profile picture was not updated', async () => {
    const { patchRequest } = require('../src/api/verbs');
    const { clearProfilePictureCache } = require('../src/utils/imagePrefetch');
    const { clearUserProfileCache } = require('../src/api/api');

    const mockUpdatedUser = {
      id: 'user123',
      username: 'testuser',
      fullName: 'Updated User',
      profilePicture: 'https://example.com/old-profile.jpg', // Same as before
      bio: 'Updated bio',
    };

    patchRequest.mockResolvedValue({
      data: {
        data: {
          user: mockUpdatedUser,
        },
      },
    });

    const formData = new FormData();
    formData.append('fullName', 'Updated User');
    formData.append('bio', 'Updated bio');

    await store.dispatch(profileUpdate(formData));

    // Verify that image cache was NOT cleared since profile picture didn't change
    expect(clearProfilePictureCache).not.toHaveBeenCalled();

    // But API cache should still be cleared
    expect(clearUserProfileCache).toHaveBeenCalledWith('user123');
  });
});
