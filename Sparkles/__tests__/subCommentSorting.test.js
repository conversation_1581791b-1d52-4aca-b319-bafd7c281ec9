// Test the sorting logic for subcomments
// This tests the core sorting functionality without importing React components
const mockFormatComments = (comments, currentUserId) => {
  const formatSingleComment = (comment) => ({
    id: comment._id || comment.id,
    user: comment.user?.username || 'Unknown',
    text: comment.text,
    avatar: comment.user?.profilePicture || 'https://default-profile.jpg',
    likes: comment.likes || [],
    liked: comment.likes?.some(like => like.user === currentUserId) || false,
    parent: comment.parent || null,
    replies: [],
    createdAt: comment.createdAt
  });

  // Create a map to store all comments by their ID
  const commentMap = new Map();

  // First pass: format all comments and store them in the map
  comments.forEach(comment => {
    const formattedComment = formatSingleComment(comment);
    commentMap.set(formattedComment.id, formattedComment);
  });

  // Second pass: build the nested structure
  const rootComments = [];

  commentMap.forEach(comment => {
    if (comment.parent) {
      // This is a reply, find its parent and add it to the parent's replies
      const parentComment = commentMap.get(comment.parent);
      if (parentComment) {
        parentComment.replies.push(comment);
      }
    } else {
      // This is a root comment
      rootComments.push(comment);
    }
  });

  // Sort replies within each comment by creation time (oldest first)
  const sortReplies = (comment) => {
    if (comment.replies && comment.replies.length > 0) {
      comment.replies.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateA - dateB; // Oldest first for replies
      });

      // Recursively sort sub-replies
      comment.replies.forEach(reply => sortReplies(reply));
    }
  };

  // Sort root comments by creation time (oldest first) and sort their replies
  rootComments.sort((a, b) => {
    const dateA = new Date(a.createdAt || 0);
    const dateB = new Date(b.createdAt || 0);
    return dateA - dateB; // Oldest first for root comments
  });

  // Sort all replies
  rootComments.forEach(comment => sortReplies(comment));

  return rootComments;
};

describe('Sub-comment Sorting', () => {
  const currentUserId = 'user123';

  test('should sort subcomments from oldest to newest (top to bottom)', () => {
    const mockComments = [
      {
        _id: 'comment1',
        text: 'Main comment',
        user: { username: 'user1' },
        createdAt: '2024-01-01T10:00:00Z',
        parent: null
      },
      {
        _id: 'reply1',
        text: 'First reply (oldest)',
        user: { username: 'user2' },
        createdAt: '2024-01-01T11:00:00Z',
        parent: 'comment1'
      },
      {
        _id: 'reply2',
        text: 'Second reply (middle)',
        user: { username: 'user3' },
        createdAt: '2024-01-01T12:00:00Z',
        parent: 'comment1'
      },
      {
        _id: 'reply3',
        text: 'Third reply (newest)',
        user: { username: 'user4' },
        createdAt: '2024-01-01T13:00:00Z',
        parent: 'comment1'
      }
    ];

    const formattedComments = mockFormatComments(mockComments, currentUserId);

    // Should have one main comment
    expect(formattedComments).toHaveLength(1);

    const mainComment = formattedComments[0];
    expect(mainComment.replies).toHaveLength(3);

    // Replies should be sorted from oldest to newest
    expect(mainComment.replies[0].text).toBe('First reply (oldest)');
    expect(mainComment.replies[1].text).toBe('Second reply (middle)');
    expect(mainComment.replies[2].text).toBe('Third reply (newest)');

    // Verify the timestamps are in ascending order
    const reply1Time = new Date(mainComment.replies[0].createdAt).getTime();
    const reply2Time = new Date(mainComment.replies[1].createdAt).getTime();
    const reply3Time = new Date(mainComment.replies[2].createdAt).getTime();

    expect(reply1Time).toBeLessThan(reply2Time);
    expect(reply2Time).toBeLessThan(reply3Time);
  });

  test('should sort nested sub-replies from oldest to newest', () => {
    const mockComments = [
      {
        _id: 'comment1',
        text: 'Main comment',
        user: { username: 'user1' },
        createdAt: '2024-01-01T10:00:00Z',
        parent: null
      },
      {
        _id: 'reply1',
        text: 'First reply',
        user: { username: 'user2' },
        createdAt: '2024-01-01T11:00:00Z',
        parent: 'comment1'
      },
      {
        _id: 'subreply1',
        text: 'Sub-reply 1 (oldest)',
        user: { username: 'user3' },
        createdAt: '2024-01-01T12:00:00Z',
        parent: 'reply1'
      },
      {
        _id: 'subreply2',
        text: 'Sub-reply 2 (newest)',
        user: { username: 'user4' },
        createdAt: '2024-01-01T13:00:00Z',
        parent: 'reply1'
      }
    ];

    const formattedComments = mockFormatComments(mockComments, currentUserId);

    const mainComment = formattedComments[0];
    const firstReply = mainComment.replies[0];

    expect(firstReply.replies).toHaveLength(2);

    // Sub-replies should be sorted from oldest to newest
    expect(firstReply.replies[0].text).toBe('Sub-reply 1 (oldest)');
    expect(firstReply.replies[1].text).toBe('Sub-reply 2 (newest)');

    // Verify the timestamps are in ascending order
    const subReply1Time = new Date(firstReply.replies[0].createdAt).getTime();
    const subReply2Time = new Date(firstReply.replies[1].createdAt).getTime();

    expect(subReply1Time).toBeLessThan(subReply2Time);
  });

  test('should handle mixed timestamps correctly', () => {
    const mockComments = [
      {
        _id: 'comment1',
        text: 'Main comment',
        user: { username: 'user1' },
        createdAt: '2024-01-01T10:00:00Z',
        parent: null
      },
      {
        _id: 'reply3',
        text: 'Reply posted last but created first',
        user: { username: 'user2' },
        createdAt: '2024-01-01T09:00:00Z', // Earlier timestamp
        parent: 'comment1'
      },
      {
        _id: 'reply1',
        text: 'Reply posted first but created last',
        user: { username: 'user3' },
        createdAt: '2024-01-01T15:00:00Z', // Later timestamp
        parent: 'comment1'
      }
    ];

    const formattedComments = mockFormatComments(mockComments, currentUserId);

    const mainComment = formattedComments[0];
    expect(mainComment.replies).toHaveLength(2);

    // Should be sorted by createdAt, not by order of addition
    expect(mainComment.replies[0].text).toBe('Reply posted last but created first');
    expect(mainComment.replies[1].text).toBe('Reply posted first but created last');
  });
});
