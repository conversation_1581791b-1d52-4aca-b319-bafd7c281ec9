import AsyncStorage from '@react-native-async-storage/async-storage';
import { configureStore } from '@reduxjs/toolkit';
import { comprehensiveLogout } from '../src/redux/actions/logoutActions';
import { clearAllUserData } from '../src/utils/clearUserData';
import { GLOBAL_RESET } from '../src/redux/store';
import authReducer from '../src/redux/slices/authSlice';
import userReducer from '../src/redux/slices/userSlice';
import postsReducer from '../src/redux/slices/postSlice';
import conversationsReducer from '../src/redux/slices/conversationsSlice';
import messagesReducer from '../src/redux/slices/messagesSlice';
import discoverReducer from '../src/redux/slices/discoverSlice';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiRemove: jest.fn(),
  getAllKeys: jest.fn(),
  clear: jest.fn(),
}));

// Mock API
jest.mock('../src/api/api', () => ({
  post: jest.fn(),
  defaults: {
    headers: {
      common: {}
    }
  },
  clearApiCache: jest.fn(),
  clearUserSpecificCache: jest.fn(),
}));

// Mock the readMessageStorage utility
jest.mock('../src/utils/readMessageStorage', () => ({
  clearReadMessages: jest.fn(),
}));

// Mock the imagePrefetch utility
jest.mock('../src/utils/imagePrefetch', () => ({
  clearImageCache: jest.fn(),
}));

describe('Comprehensive Logout Functionality', () => {
  let store;

  beforeEach(() => {
    // Create a test store with the same structure as the main app
    const rootReducer = (state, action) => {
      if (action.type === GLOBAL_RESET) {
        state = undefined;
      }

      return {
        auth: authReducer(state?.auth, action),
        user: userReducer(state?.user, action),
        posts: postsReducer(state?.posts, action),
        conversations: conversationsReducer(state?.conversations, action),
        messages: messagesReducer(state?.messages, action),
        discover: discoverReducer(state?.discover, action),
      };
    };

    store = configureStore({
      reducer: rootReducer,
    });

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('clearAllUserData utility', () => {
    it('should clear all specified AsyncStorage keys', async () => {
      AsyncStorage.getAllKeys.mockResolvedValue(['token', 'refreshToken', 'someOtherKey']);

      await clearAllUserData();

      expect(AsyncStorage.multiRemove).toHaveBeenCalledWith([
        'token',
        'refreshToken',
        'isLoggedIn',
        'isRegistered',
        'currentBackgroundIndex',
        'savedUsers',
        'persist:root',
        'persist:auth',
        'persist:user',
        'persist:posts',
        'persist:conversations',
        'persist:messages',
      ]);
    });

    it('should handle AsyncStorage errors gracefully', async () => {
      AsyncStorage.multiRemove.mockRejectedValue(new Error('Storage error'));

      await expect(clearAllUserData()).rejects.toThrow('Storage error');
    });
  });

  describe('Global Redux Reset', () => {
    it('should reset all Redux state when GLOBAL_RESET is dispatched', () => {
      // Set some initial state by directly updating the store
      store.dispatch({
        type: 'posts/setPosts',
        payload: [{ id: 1, title: 'Test Post' }]
      });

      // Set auth state manually
      const authState = store.getState().auth;
      authState.user = { id: 1, username: 'testuser' };
      authState.token = 'test-token';
      authState.isLogin = true;

      // Verify state has data
      let state = store.getState();
      expect(state.posts.posts).toHaveLength(1);

      // Dispatch global reset
      store.dispatch({ type: GLOBAL_RESET });

      // Verify all state is reset
      state = store.getState();
      expect(state.auth.user).toBeNull();
      expect(state.auth.token).toBeNull();
      expect(state.auth.isLogin).toBe(false);
      expect(state.posts.posts).toEqual([]);
      expect(state.posts.feedPosts).toEqual([]);
      expect(state.conversations.conversations).toEqual([]);
      expect(state.messages.messages).toEqual([]);
      expect(state.discover.discoverPosts).toEqual([]);
    });
  });

  describe('comprehensiveLogout action', () => {
    it('should successfully complete comprehensive logout', async () => {
      // Mock successful API call
      const mockApi = require('../src/api/api');
      mockApi.post.mockResolvedValue({ data: { success: true } });

      // Mock successful AsyncStorage operations
      AsyncStorage.multiRemove.mockResolvedValue();
      AsyncStorage.getAllKeys.mockResolvedValue([]);

      // Dispatch comprehensive logout
      const result = await store.dispatch(comprehensiveLogout());

      // Verify the action was fulfilled
      expect(result.type).toBe('auth/comprehensiveLogout/fulfilled');
      expect(result.payload.success).toBe(true);

      // Verify AsyncStorage was cleared
      expect(AsyncStorage.multiRemove).toHaveBeenCalled();

      // Verify API logout was called
      expect(mockApi.post).toHaveBeenCalledWith('/auth/logout');
    });

    it('should handle API logout failure gracefully', async () => {
      // Mock API failure
      const mockApi = require('../src/api/api');
      mockApi.post.mockRejectedValue(new Error('Network error'));

      // Mock successful AsyncStorage operations
      AsyncStorage.multiRemove.mockResolvedValue();
      AsyncStorage.getAllKeys.mockResolvedValue([]);

      // Dispatch comprehensive logout
      const result = await store.dispatch(comprehensiveLogout());

      // Should still succeed even if API call fails
      expect(result.type).toBe('auth/comprehensiveLogout/fulfilled');
      expect(result.payload.success).toBe(true);

      // Verify AsyncStorage was still cleared
      expect(AsyncStorage.multiRemove).toHaveBeenCalled();
    });

    it('should handle complete failure with fallback cleanup', async () => {
      // Mock all operations to fail
      const mockApi = require('../src/api/api');
      mockApi.post.mockRejectedValue(new Error('Network error'));
      AsyncStorage.multiRemove.mockRejectedValue(new Error('Storage error'));

      // Dispatch comprehensive logout
      const result = await store.dispatch(comprehensiveLogout());

      // Should be rejected due to storage error
      expect(result.type).toBe('auth/comprehensiveLogout/rejected');
      expect(result.payload.message).toContain('Storage error');
    });
  });

  describe('State persistence after logout', () => {
    it('should not retain any user data after logout', async () => {
      // Set up initial user state
      store.dispatch({
        type: 'auth/loginUser/fulfilled',
        payload: {
          user: { id: 1, username: 'testuser', email: '<EMAIL>' },
          token: 'test-token',
          refreshToken: 'refresh-token'
        }
      });

      store.dispatch({
        type: 'user/getUserInfo/fulfilled',
        payload: { id: 1, username: 'testuser', bio: 'Test bio' }
      });

      store.dispatch({
        type: 'posts/setPosts',
        payload: [
          { id: 1, title: 'Post 1', userId: 1 },
          { id: 2, title: 'Post 2', userId: 1 }
        ]
      });

      store.dispatch({
        type: 'discover/fetchDiscoverPosts/fulfilled',
        payload: [
          { id: 3, title: 'Discover Post 1', userId: 1 },
          { id: 4, title: 'Discover Post 2', userId: 1 }
        ]
      });

      // Verify initial state
      let state = store.getState();
      expect(state.auth.user.id).toBe(1);
      expect(state.user.userInfo.username).toBe('testuser');
      expect(state.posts.posts).toHaveLength(2);
      expect(state.discover.discoverPosts).toHaveLength(2);

      // Perform logout
      await store.dispatch(comprehensiveLogout());

      // Verify all user data is cleared
      state = store.getState();
      expect(state.auth.user).toBeNull();
      expect(state.auth.token).toBeNull();
      expect(state.auth.refreshToken).toBeNull();
      expect(state.auth.isLogin).toBe(false);
      expect(state.user.userInfo).toBeNull();
      expect(state.posts.posts).toEqual([]);
      expect(state.posts.feedPosts).toEqual([]);
      expect(state.discover.discoverPosts).toEqual([]);
    });
  });
});
