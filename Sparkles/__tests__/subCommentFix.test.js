/**
 * Test for sub-comment immediate display fix
 */

// Mock the Redux Toolkit createSlice functionality
const mockAddReplyToComment = (state, action) => {
  const { parentCommentId, reply, isOptimistic, replaceOptimistic, removeOptimistic } = action.payload;

  // <PERSON>le removing optimistic updates
  if (removeOptimistic) {
    const removeFromReplies = (comments) => {
      comments.forEach(comment => {
        if (comment.replies) {
          comment.replies = comment.replies.filter(r => r.id !== reply.id && r._id !== reply._id);
          removeFromReplies(comment.replies);
        }
      });
    };
    removeFromReplies(state.comments);
    state.lastUpdated = Date.now();
    return state;
  }

  // Handle replacing optimistic updates with real data
  if (replaceOptimistic) {
    const replaceInReplies = (comments) => {
      comments.forEach(comment => {
        if (comment.replies) {
          const optimisticIndex = comment.replies.findIndex(r => r.id === replaceOptimistic || r._id === replaceOptimistic);
          if (optimisticIndex !== -1) {
            comment.replies[optimisticIndex] = reply;
            state.lastUpdated = Date.now();
            return true;
          }
          if (replaceInReplies(comment.replies)) return true;
        }
      });
      return false;
    };
    replaceInReplies(state.comments);
    return state;
  }

  // Find the target parent comment (could be a main comment or a sub-comment)
  const findParentAndAddReply = (comments, targetParentId) => {
    for (const comment of comments) {
      // Check if this is the direct parent
      if (comment._id === targetParentId || comment.id === targetParentId) {
        if (!comment.replies) {
          comment.replies = [];
        }

        // Check if the reply already exists to avoid duplicates
        const replyExists = comment.replies.some(r =>
          r._id === reply._id || r.id === reply.id
        );

        if (!replyExists) {
          // Add the reply to the beginning of the parent's replies array
          comment.replies.unshift(reply);
          state.lastUpdated = Date.now();
          return true;
        }
        return false;
      }

      // Recursively search in replies
      if (comment.replies && comment.replies.length > 0) {
        if (findParentAndAddReply(comment.replies, targetParentId)) {
          return true;
        }
      }
    }
    return false;
  };

  // Try to find and add the reply
  findParentAndAddReply(state.comments, parentCommentId);
  return state;
};

describe('Sub-comment immediate display fix', () => {
  let initialState;

  beforeEach(() => {
    initialState = {
      comments: [
        {
          _id: 'comment1',
          text: 'Main comment',
          user: { username: 'user1' },
          replies: [
            {
              _id: 'reply1',
              text: 'First reply',
              user: { username: 'user2' },
              parent: 'comment1',
              replies: []
            }
          ]
        }
      ],
      loading: false,
      error: null,
      status: 'idle',
      lastUpdated: null
    };
  });

  test('should add optimistic sub-comment immediately', () => {
    const optimisticSubReply = {
      id: 'temp-123',
      _id: 'temp-123',
      text: 'Sub-reply to first reply',
      user: { username: 'user3' },
      parent: 'reply1',
      replies: [],
      createdAt: new Date().toISOString()
    };

    const action = {
      payload: {
        parentCommentId: 'reply1',
        reply: optimisticSubReply,
        isOptimistic: true
      }
    };

    const newState = mockAddReplyToComment(JSON.parse(JSON.stringify(initialState)), action);

    // Check that the sub-reply was added to the correct parent
    const mainComment = newState.comments[0];
    const firstReply = mainComment.replies[0];

    expect(firstReply.replies).toHaveLength(1);
    expect(firstReply.replies[0].text).toBe('Sub-reply to first reply');
    expect(firstReply.replies[0].id).toBe('temp-123');
    expect(newState.lastUpdated).toBeTruthy();
  });

  test('should replace optimistic sub-comment with real data', () => {
    // First add optimistic sub-comment
    const optimisticSubReply = {
      id: 'temp-123',
      _id: 'temp-123',
      text: 'Sub-reply to first reply',
      user: { username: 'user3' },
      parent: 'reply1',
      replies: []
    };

    let state = mockAddReplyToComment(JSON.parse(JSON.stringify(initialState)), {
      payload: {
        parentCommentId: 'reply1',
        reply: optimisticSubReply,
        isOptimistic: true
      }
    });

    // Then replace with real data
    const realSubReply = {
      _id: 'real-sub-reply-456',
      text: 'Sub-reply to first reply',
      user: { username: 'user3' },
      parent: 'reply1',
      replies: [],
      createdAt: new Date().toISOString()
    };

    state = mockAddReplyToComment(state, {
      payload: {
        parentCommentId: 'reply1',
        reply: realSubReply,
        replaceOptimistic: 'temp-123'
      }
    });

    // Check that optimistic was replaced with real data
    const mainComment = state.comments[0];
    const firstReply = mainComment.replies[0];

    expect(firstReply.replies).toHaveLength(1);
    expect(firstReply.replies[0]._id).toBe('real-sub-reply-456');
    expect(firstReply.replies[0].id).toBeUndefined(); // Real data doesn't have temp id
  });

  test('should remove optimistic sub-comment on error', () => {
    // First add optimistic sub-comment
    const optimisticSubReply = {
      id: 'temp-123',
      _id: 'temp-123',
      text: 'Sub-reply to first reply',
      user: { username: 'user3' },
      parent: 'reply1',
      replies: []
    };

    let state = mockAddReplyToComment(JSON.parse(JSON.stringify(initialState)), {
      payload: {
        parentCommentId: 'reply1',
        reply: optimisticSubReply,
        isOptimistic: true
      }
    });

    // Verify it was added
    expect(state.comments[0].replies[0].replies).toHaveLength(1);

    // Then remove on error
    state = mockAddReplyToComment(state, {
      payload: {
        parentCommentId: 'reply1',
        reply: optimisticSubReply,
        removeOptimistic: true
      }
    });

    // Check that optimistic was removed
    const mainComment = state.comments[0];
    const firstReply = mainComment.replies[0];

    expect(firstReply.replies).toHaveLength(0);
  });

  test('should not create duplicate sub-comments when adding same reply twice', () => {
    // Add the same optimistic sub-comment twice
    const optimisticSubReply = {
      id: 'temp-123',
      _id: 'temp-123',
      text: 'Sub-reply to first reply',
      user: { username: 'user3' },
      parent: 'reply1',
      replies: []
    };

    let state = mockAddReplyToComment(JSON.parse(JSON.stringify(initialState)), {
      payload: {
        parentCommentId: 'reply1',
        reply: optimisticSubReply,
        isOptimistic: true
      }
    });

    // Try to add the same reply again
    state = mockAddReplyToComment(state, {
      payload: {
        parentCommentId: 'reply1',
        reply: optimisticSubReply,
        isOptimistic: true
      }
    });

    // Should still only have 1 sub-reply, not 2
    const mainComment = state.comments[0];
    const firstReply = mainComment.replies[0];

    expect(firstReply.replies).toHaveLength(1);
    expect(firstReply.replies[0].text).toBe('Sub-reply to first reply');
  });
});
